import React, { useEffect, useRef, useState } from 'react'

interface LoginIframeModalProps {
  isOpen: boolean
  onClose: () => void
}

const LoginIframeModal: React.FC<LoginIframeModalProps> = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(true)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [lastUrl, setLastUrl] = useState<string | null>(null)

  useEffect(() => {
    const checkIframeUrl = () => {
      try {
        const iframe = iframeRef.current
        if (iframe && iframe.contentWindow) {
          const currentUrl = iframe.contentWindow.location.href

          // 如果 URL 发生变化，打印新的 URL
          if (currentUrl !== lastUrl) {
            console.log('Iframe URL changed:', currentUrl)
            setLastUrl(currentUrl)
            if (currentUrl.indexOf('basic-search') > 0) {
              // 跳转到工作台
              window.location.href = 'https://www.portal.ai-smarties.com/basic-search'
            }
          } else {
            console.log('url not change: ', currentUrl)
          }
        }
      } catch (error) {
        // 如果 iframe 跨域，会抛出异常，忽略即可
        console.warn('Unable to access iframe URL due to cross-origin restrictions')
      }
    }

    // 定时器定期检查 iframe URL
    const intervalId = setInterval(checkIframeUrl, 500)

    return () => clearInterval(intervalId)
  }, [lastUrl])

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 打印接收到的消息
      console.log('Received message from iframe:', event.data)

      // 验证消息来源
      if (event.origin.indexOf('ai-smarties.com') < 0) {
        return
      }

      // 这里可以根据消息内容处理不同的逻辑
      if (event.data?.type === 'loginSuccess') {
        console.log('Login successful!')
        // 跳转到工作台
        window.location.href = 'https://www.portal.ai-smarties.com/basic-search'
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [])

  if (!isOpen) return null

  return (
    <div className='fixed inset-0 z-50 overflow-y-auto'>
      <div className='flex min-h-screen items-center justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0'>
        {/* Background overlay */}
        <div
          className='fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity'
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className='relative inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-[28rem] sm:align-middle'>
          {/* Close button */}
          <button
            onClick={onClose}
            className='absolute right-4 top-4 text-gray-400 hover:text-gray-500'
            type='button'>
            <span className='sr-only'>Close</span>
            <svg
              className='h-6 w-6'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
              xmlns='http://www.w3.org/2000/svg'>
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </button>

          {/* Iframe container */}
          <div className='relative h-[750px] w-full'>
            <div
              className={`absolute inset-0 flex items-center justify-center bg-white transition-opacity duration-200 ${
                isLoading ? 'opacity-100' : 'pointer-events-none opacity-0'
              }`}>
              <div className='h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-indigo-600' />
            </div>
            <iframe
              ref={iframeRef}
              src='https://www.portal.ai-smarties.com/signup?lang=en&redirectUrl=https://www.portal.ai-smarties.com/basic-search'
              className='h-full w-full'
              title='Login'
              allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
              allowFullScreen
              onLoad={() => setIsLoading(false)}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginIframeModal
