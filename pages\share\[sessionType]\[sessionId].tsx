import { getSessionById } from '@/api/getSessionById'
import BaseLayout from '@/components/layouts/BaseLayout'
import MarkdownParser from '@/components/markdown/MarkdownParser'
import CallToAction from '@/components/seo/CallToAction'
import FreeTrialPromo from '@/components/seo/FreeTrialPromo'
import { Message, MessageRoleEnum, PoTypeEnum } from '@/types'
import { ChevronDown, ChevronUp } from 'lucide-react'
import clsx from 'clsx'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import Loading from '@/components/common/Loading'
import { Namespace } from '@/i18n'

const GeneralPreviewPage = () => {
  const handleLoginClick = (e: React.MouseEvent) => {
    e.preventDefault()
    window.location.href = 'https://ai-smarties.com/deep-research'
  }

  const router = useRouter()
  const { sessionType, sessionId, title } = router.query
  const [messages, setMessages] = useState<Array<Message>>([])
  const [loading, setLoading] = useState(false)

  const getSession = async () => {
    try {
      setLoading(true)
      const res = await getSessionById({ sessionId: sessionId as string })
      if (res) {
        const message = res.content.map((item: Message, index: number) => ({
          ...item,
          key: index + item.role,
          reasoningExpand: false,
        }))
        setMessages(message)
        setLoading(false)
      }
    } catch (e) {
      console.error(e)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (sessionId) {
      getSession()
    }
  }, [sessionId])

  const handleExpandReasoning = (key: string) => {
    const nextMessages = messages.map((item) => {
      if (item.key === key) {
        return { ...item, reasoningExpand: !item.reasoningExpand }
      }
      return item
    })
    setMessages(nextMessages)
  }

  return (
    <>
      {/* Main Content */}
      <main className='py-2 md:py-4'>
        <div className='mx-auto max-w-7xl px-2 sm:px-4'>
          <div className='flex flex-col lg:flex-row lg:gap-6'>
            {/* Left Content Area */}
            <div className='w-full flex-1 lg:max-w-5xl'>
              <div className='mt-8 px-3 lg:px-12'>
                {loading ? (
                  <Loading />
                ) : (
                  <>
                    <h1 className='mt-4 text-4xl font-bold'>{title}</h1>
                    {messages.length > 0 &&
                      messages.map((item, index) => {
                        return (
                          <div key={item.key} className='mx-2 mb-10 lg:mx-0'>
                            <div className={clsx('mb-4 lg:flex')}>
                              <div className='shrink-0'>
                                <Image
                                  src={
                                    item.role === MessageRoleEnum.ASSISTANT
                                      ? '/images/system_avatar.svg'
                                      : '/images/default_avatar.svg'
                                  }
                                  width={32}
                                  height={32}
                                  className='mr-3 rounded-lg'
                                  alt={'avatar'}
                                />
                              </div>
                              <div>
                                {item.role == MessageRoleEnum.ASSISTANT && item.reasoning && (
                                  <>
                                    <div className='flex items-center'>
                                      <div
                                        className='mt-1 flex cursor-pointer items-center justify-center rounded-sm border bg-card px-3 py-2 hover:bg-slate-100'
                                        onClick={() => {
                                          handleExpandReasoning(item.key)
                                        }}>
                                        <div className='text-primary'>
                                          <Image
                                            src='/images/deepsearch.svg'
                                            alt='Reasoning icon'
                                            width={16}
                                            height={16}
                                          />
                                        </div>
                                        <div className='ml-1.5'>Reasoning</div>
                                        {item.reasoningExpand ? (
                                          <ChevronDown className='ml-1' width={16} height={16} />
                                        ) : (
                                          <ChevronUp className='ml-1' width={16} height={16} />
                                        )}
                                      </div>
                                    </div>
                                    <div className='mb-7 mt-5 border-l-2 pl-4 text-sm text-secondary-black-3'>
                                      {item.reasoningExpand && (
                                        <MarkdownParser content={item.reasoning || ''} />
                                      )}
                                    </div>
                                  </>
                                )}

                                {item.role === MessageRoleEnum.USER ? (
                                  index === 0 ? (
                                    <Questions
                                      type={sessionType as string}
                                      messages={item.content}
                                    />
                                  ) : (
                                    <div className='opacity-90'>
                                      <MarkdownParser content={item.content[0].value} />
                                    </div>
                                  )
                                ) : (
                                  <div className='w-[calc(100%-32px)] flex-1 opacity-90'>
                                    <MarkdownParser
                                      content={item.content}
                                      list={item.referenceList}
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )
                      })}
                  </>
                )}
              </div>
              <div className='py-12'>
                <CallToAction />
              </div>
            </div>

            {/* Right Sidebar */}
            <div className='mt-4 w-full lg:mt-0 lg:w-80'>
              <div className='lg:sticky lg:top-4'>
                {/* 免费试用引导 */}
                <div className='mb-4 mt-8'>
                  <FreeTrialPromo
                    asm-tracking='CLICK_SEO_SIGNUP:CLICK'
                    asm-tracking-p-view='Desktop'
                    asm-tracking-p-position='FreeTrialPromo'
                    onButtonClick={handleLoginClick}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

export default GeneralPreviewPage

const Questions = ({
  type,
  messages,
}: {
  type: string
  messages: Array<{ value: string; key: string }>
}) => {
  const { t } = useTranslation(Namespace.SHARE)

  type MessageKey =
    | 'description'
    | 'targetMarket'
    | 'companyName'
    | 'query'
    | 'currentLocation'
    | 'topic'
    | 'details'

  const [questionFilledValue, setQuestionFilledValue] = useState<
    Record<PoTypeEnum, Array<{ title?: string; value: string; key: MessageKey }>>
  >({
    GENERAL: [],
    MARKET: [],
    TOPIC: [],
    SWOT: [],
    COMPANY: [],
    REGULATION: [],
    RISK: [],
  })

  const questions = useMemo(
    () => ({
      GENERAL: [{ title: t('general.query'), value: '', key: 'query' }],
      MARKET: [
        { title: t('market.query1'), value: '', key: 'description' },
        { title: t('market.query2'), value: '', key: 'targetMarket' },
      ],
      TOPIC: [
        { title: t('topic.query1'), value: '', key: 'topic' },
        { title: t('topic.query2'), value: '', key: 'details' },
      ],
      SWOT: [{ title: t('swot.query1'), value: '', key: 'description' }],
      COMPANY: [{ title: t('company.query1'), value: '', key: 'companyName' }],
      REGULATION: [
        { title: t('oversea.query1'), value: '', key: 'description' },
        { title: t('oversea.query2'), value: '', key: 'targetMarket' },
        { title: t('oversea.query3'), value: '', key: 'currentLocation' },
      ],
      RISK: [{ title: t('risk.query1'), value: '', key: 'description' }],
    }),
    [t],
  )

  useEffect(() => {
    const newQuestions = questions[(type as string).toUpperCase() as PoTypeEnum].map(
      (item, index) => {
        const findObj = messages.find((message) => message.key === item.key)
        if (findObj)
          return {
            ...item,
            value: findObj?.value ?? '',
          }
        else {
          // 意图分析过来的，直接取一个 query
          if (index === 0)
            return {
              ...item,
              value: messages[0].value ?? '',
            }
          return {
            ...item,
          }
        }
      },
    )
    setQuestionFilledValue((prev) => ({
      ...prev,
      [(type as string).toUpperCase() as PoTypeEnum]: newQuestions,
    }))
  }, [type, messages, questions])

  return (
    <div className='flex'>
      <div className='flex flex-1 flex-col gap-2 font-sans'>
        {questionFilledValue &&
          type &&
          questionFilledValue[(type as string).toUpperCase() as PoTypeEnum].map((item, index) => {
            return (
              <div key={index}>
                <h5
                  className={clsx(
                    'mt-2 overflow-hidden text-ellipsis whitespace-nowrap font-bold',
                  )}>
                  {item && item?.title}
                </h5>
                <div className='mt-1'>{item.value}</div>
              </div>
            )
          })}
      </div>
    </div>
  )
}
