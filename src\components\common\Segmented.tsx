import React, { useState } from 'react'

interface SegmentedProps {
  options: Array<{ value: string; name: string }>
  defaultValue?: string
  onChange?: (value: string) => void
}

const Segmented: React.FC<SegmentedProps> = ({ options, defaultValue, onChange }) => {
  const [selected, setSelected] = useState<string>(defaultValue || options[0].value)

  const handleClick = (value: string) => {
    setSelected(value)
    if (onChange) {
      onChange(value)
    }
  }

  return (
    <div className='flex w-full flex-col items-center'>
      {/* Options Container */}
      <div className='flex overflow-hidden rounded-md border'>
        {options.map((option) => (
          <div
            key={option.value}
            className={`cursor-pointer px-2 py-2 text-center transition-all duration-200 md:px-4 ${selected === option.value ? 'bg-primary text-white' : 'bg-white text-gray-800 hover:bg-gray-200'}`}
            onClick={() => handleClick(option.value)}>
            {option.name}
          </div>
        ))}
      </div>
    </div>
  )
}

export default Segmented
