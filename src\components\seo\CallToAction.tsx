import { Namespace } from '@/i18n'
import { motion } from 'framer-motion'
import { AlertCircleIcon } from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

const CallToAction = () => {
  const [showTip, setShowTip] = useState<boolean>(false)
  const { t } = useTranslation(Namespace.GLOBAL)

  return (
    <div className='max-w-5xl px-4 sm:px-6 lg:px-8'>
      <div className='items-center justify-between rounded-2xl border border-gray-100 bg-white p-7 shadow-lg md:flex-row lg:flex'>
        <div className='mb-6 text-center md:mb-0 md:mr-8 md:text-left'>
          <h2 className='text-2xl font-bold text-gray-900'>{t('callToAction.title')}</h2>
        </div>
        <div className='relative mt-2 flex justify-center lg:mt-0'>
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ type: 'spring', stiffness: 400, damping: 10 }}>
            <Link
              href='/deep-research'
              className='inline-flex items-center rounded-md bg-primary px-6 py-3 text-center text-base font-medium text-white shadow-md transition-all duration-300 hover:-translate-y-1 hover:bg-primary-dark hover:shadow-lg'>
              <span className='mr-2'>{t('callToAction.noCost')}</span>
              <AlertCircleIcon
                width={16}
                height={16}
                onMouseEnter={() => {
                  setShowTip(true)
                }}
                onMouseLeave={() => {
                  setShowTip(false)
                }}
              />
            </Link>
          </motion.div>
          {showTip && (
            <div className='absolute -bottom-6 mt-2 flex items-center justify-center text-center text-sm text-gray-500'>
              <div className='mr-2'>{t('noCreditCard')}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CallToAction
