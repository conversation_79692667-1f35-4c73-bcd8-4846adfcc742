/* eslint-disable react/no-unknown-property */
import React from 'react'
import Link from 'next/link'

interface RelatedArticlesProps {
  articles: Array<{
    title: string
    url: string
  }>
}

const RelatedArticles: React.FC<RelatedArticlesProps> = ({ articles }) => {
  return (
    <div className='rounded-md border border-gray-100 bg-white p-4 shadow-sm'>
      {/*<h3 className='text-lg font-semibold text-gray-800'>Related Articles</h3>*/}
      <div className='grid space-y-2.5 text-sm'>
        {articles.map((article, index) => (
          <Link
            key={article.url}
            href={article.url}
            className='group block'
            asm-tracking='CLICK_SEO_RELATED_ARTICAL:CLICK'
            asm-tracking-p-index={index + 1}
            asm-tracking-p-title={article.title}>
            {/*<div*/}
            {/*    className='rounded-lg border border-transparent p-2 transition-all duration-200 hover:border-gray-200 hover:bg-gray-50'>*/}

            {/*</div>*/}
            <p className='break-words text-sm text-gray-700 group-hover:text-blue-600'>
              {index + 1}. {article.title}
            </p>
          </Link>
        ))}
      </div>
    </div>
  )
}

export default RelatedArticles
