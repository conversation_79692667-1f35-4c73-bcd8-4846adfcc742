# Intel Dossier FormSubmit 故障排除指南

## 📝 更新说明

**✅ 已升级为原生POST提交方式**

- 不再使用JavaScript fetch请求
- 使用HTML表单原生POST提交
- 大幅减少网络相关问题
- 提高兼容性和可靠性

## 🚨 可能遇到的问题（已大幅减少）

### 原因1: FormSubmit服务未激活 (最常见)

**症状：**

- 页面显示橙色提示框："FormSubmit需要激活"
- 控制台显示："需要邮箱验证"

**解决方案：**

1. 点击页面上的 **"点击激活FormSubmit服务"** 按钮
2. 在新页面中按照提示操作
3. 检查您的邮箱（包括垃圾邮件文件夹）
4. 点击激活邮件中的确认链接
5. 返回Intel Dossier页面，点击 **"重新检查状态"**

### 原因2: 邮箱地址配置错误

**检查项目：**

- 确认 `FORMSUBMIT_EMAIL` 设置为真实邮箱地址
- 确认邮箱格式正确（如：`<EMAIL>`）
- 不要使用临时邮箱或无效地址

**当前配置：** `<EMAIL>` ✅ (QQ邮箱格式正确)

### 原因3: 网络连接问题

**检查步骤：**

1. 测试网络连接：访问 https://formsubmit.co
2. 检查防火墙设置
3. 尝试使用VPN（如果在受限地区）
4. 确认在HTTPS环境下测试

### 原因4: CORS政策限制

**解决方案：**

- 确保在生产环境（HTTPS）下测试
- 开发环境的CORS问题通常在部署后解决
- 检查浏览器安全设置

## 🔍 调试步骤

### 1. 查看控制台日志

打开浏览器开发者工具 (F12)，查看Console输出：

**正常流程：**

```
✅ FormSubmit已配置为: <EMAIL>
🚀 正在提交到FormSubmit: https://formsubmit.co/<EMAIL>
📤 FormSubmit响应状态: 200 OK
✅ FormSubmit响应内容: Thank you!
```

**需要激活：**

```
❗ FormSubmit需要激活
📧 需要邮箱验证！请检查您的邮箱并点击确认链接激活FormSubmit服务
```

**网络错误：**

```
❌ FormSubmit请求失败: {status: 404, statusText: "Not Found"}
```

### 2. 网络请求检查

在开发者工具的Network标签中：

1. 查找对 `formsubmit.co` 的请求
2. 检查状态码和响应内容
3. 确认请求方法为 POST

### 3. 手动测试FormSubmit

访问：`https://formsubmit.co/<EMAIL>`

- 如果显示激活页面 → 需要验证邮箱
- 如果显示表单页面 → 服务正常，可能是代码问题

## 🛠️ 解决方案

### 立即操作建议：

1. **首先激活FormSubmit：**

   ```
   访问：https://formsubmit.co/<EMAIL>
   按提示完成邮箱验证
   ```

2. **检查邮箱：**

   - 主收件箱
   - 垃圾邮件文件夹
   - 促销邮件文件夹

3. **重新测试：**
   - 完成激活后回到Intel Dossier页面
   - 点击"重新检查状态"
   - 重新提交表单

### 如果仍然失败：

1. **清除浏览器缓存**
2. **使用隐私/无痕模式测试**
3. **尝试不同浏览器**
4. **检查网络环境**

## ⚠️ 注意事项

- FormSubmit有每日提交限制（通常50-100次）
- 首次使用必须验证邮箱
- 验证链接可能需要几分钟才能收到
- QQ邮箱有时会延迟接收国外邮件

## 📧 邮件问题排查

如果没有收到激活邮件：

1. **检查所有文件夹：**

   - 收件箱
   - 垃圾邮件
   - 促销邮件
   - 社交网络

2. **添加到白名单：**

   - 将 `<EMAIL>` 添加到联系人

3. **邮箱设置：**
   - 确认邮箱可以接收国外邮件
   - 检查邮箱存储空间

## 🔄 重新配置步骤

如果问题持续，可以尝试重新配置：

1. **更换邮箱地址**（可选）
2. **重新激活服务**
3. **清除所有状态**

---

**💡 90%的问题都是因为FormSubmit未激活！**
**请优先完成邮箱验证激活。**
