{"name": "smarties-web-seo", "version": "0.1.0", "private": true, "scripts": {"dev": "run-p dev:*", "dev:next": "next dev", "build": "next build", "build:test": "ANALYZE=true next build", "start": "next start", "prepare": "husky", "lint-staged": "lint-staged", "lint": "next lint"}, "husky": {"hooks": {"pre-commit": "npx lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --ext .js,.jsx,.ts,.tsx --fix", "prettier --write"], "src/**/*.{less,css}": ["prettier --write"]}, "dependencies": {"@paddle/paddle-js": "^1.4.2", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-tooltip": "^1.2.4", "@radix-ui/themes": "^3.1.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "clsx": "^2.1.1", "docx": "^9.5.0", "file-saver": "^2.0.5", "framer-motion": "^12.6.0", "html-react-parser": "^5.2.3", "html-to-image": "^1.11.13", "i18next": "^25.0.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "mermaid": "^11.6.0", "mixpanel-browser": "^2.59.0", "next": "^15.3.5", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "nookies": "^2.5.2", "pdfjs-dist": "^3.11.174", "react": "18", "react-cookie-consent": "^9.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-i18next": "^14.1.2", "react-markdown": "^9.0.1", "react-pdf": "^8.0.2", "react-resizable-panels": "^3.0.2", "react-responsive": "^10.0.1", "react-split": "^2.0.14", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "styled-components": "^6.1.17", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "youtube-metadata-from-url": "^1.0.3", "zustand": "^5.0.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/mixpanel-browser": "^2.51.0", "@types/node": "^20", "@types/postcss-pxtorem": "^6.0.3", "@types/react": "^18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.15.0", "autoprefixer": "^10.4.20", "copy-webpack-plugin": "^12.0.2", "eslint": "^8.57.0", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "husky": "^9.0.11", "lint-staged": "^15.2.7", "npm-run-all": "^4.1.5", "postcss": "^8", "postcss-pxtorem": "^6.1.0", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "sass": "^1.77.6", "tailwindcss": "^3.4.1", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}