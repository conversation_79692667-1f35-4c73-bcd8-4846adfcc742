import { getReportById } from '@/api/getReportById'
import BaseLayout from '@/components/layouts/BaseLayout'
import MarkdownParser from '@/components/markdown/MarkdownParser'
import CallToAction from '@/components/seo/CallToAction'
import FreeTrialPromo from '@/components/seo/FreeTrialPromo'
import { useRouter } from 'next/router'
import { useEffect } from 'react'

const a = {
  content:
    '### 非暴力沟通 - Mermaid 分析图报告\n\n#### 概述\n\n非暴力沟通（Nonviolent Communication, NVC）是一种通过关注观察、感受、需求和请求来促进理解与合作的沟通方式。它强调以尊重和同理心为基础，旨在改善人际关系并减少冲突。Mermaid 分析图通过可视化的方式，帮助我们更清晰地理解非暴力沟通的核心理念及其应用场景。\n\n---\n\n### 非暴力沟通的核心要素\n\n非暴力沟通的关键在于四个核心要素：**观察、感受、需求和请求**。这些要素环环相扣，为有效沟通提供了一个系统化的框架。\n\n#### 1. **观察**\n观察是非暴力沟通的起点，要求我们以客观的方式描述事实，而不掺杂个人评价或主观判断。例如，描述行为或现象时应避免使用“你总是”或“你从不”这样的语言，因为它可能引发对方的防御情绪。\n\n#### 2. **感受**\n感受是指我们在面对特定事件时的情绪反应。通过清晰地表达自己的感受，我们可以帮助对方更好地理解我们的内心状态。例如，可以使用“我感到失望”或“我感到欣慰”来准确描述自己的情绪，而不是模糊地表达为“我觉得不好”。\n\n#### 3. **需求**\n需求是感受的根源。非暴力沟通强调，我们的感受源于是否满足了内在的需求，而非外界的行为。例如，“我感到失望，因为我需要信任”比“你让我失望”更能让对方理解问题的核心。\n\n#### 4. **请求**\n请求是非暴力沟通的最后一步，是对需求的具体表达。请求应当是清晰、具体且可实现的。例如，“我希望我们每周安排一次团队会议”比“我希望你更重视团队沟通”更容易被接受。\n\n---\n\n### 非暴力沟通的应用场景\n\n#### 1. **职场沟通**\n在职场中，非暴力沟通可以帮助团队成员更好地表达需求，避免因误解导致的冲突。例如，在项目合作中，团队成员可以通过非暴力沟通表达对任务分配的不满，同时提出具体的改进建议。\n\n#### 2. **家庭关系**\n非暴力沟通在家庭关系中尤为重要。它可以帮助家庭成员更好地理解彼此的感受与需求，从而减少争吵，增强亲密感。例如，父母可以通过非暴力沟通来理解孩子的情绪，并用尊重的方式回应。\n\n#### 3. **跨文化交流**\n在不同文化背景下，非暴力沟通可以作为一种通用语言，帮助人们克服文化差异引发的误解。例如，在国际合作项目中，团队成员可以通过非暴力沟通表达对文化差异的尊重，同时明确自身需求。\n\n---\n\n### 技术支持与可视化工具\n\nMermaid 分析图是一种基于代码的可视化工具，能够将复杂的概念以图表的形式呈现。通过 Mermaid 分析图，非暴力沟通的核心流程可以被分解为一系列可视化的节点和连接，帮助用户更直观地理解其逻辑关系。\n\n#### 技术特点\n- **节点式呈现**：将观察、感受、需求和请求以节点形式展示。\n- **逻辑连接**：通过箭头展示各要素之间的逻辑关系。\n- **多场景适用性**：可应用于培训、团队沟通和个人学习等场景。\n\n#### 示例\nMermaid 分析图可以如下呈现非暴力沟通的流程：\n```\ngraph TD\n  A[观察] --> B[感受]\n  B --> C[需求]\n  C --> D[请求]\n```\n\n---\n\n### 合规认证\n\n以下是非暴力沟通在不同领域的应用可能涉及的合规认证及其细则和要求：\n\n| 认证名称         | 细则                                                   | 要求                                                     |\n|------------------|------------------------------------------------------|--------------------------------------------------------|\n| ISO 9001: 质量管理体系认证 | 强调沟通的规范性和透明性，确保组织内外的有效沟通。           | 组织需建立文件化的沟通流程，并定期审核和改进。                     |\n| ISO 26000: 社会责任指南 | 鼓励企业在社会责任实践中采用非暴力沟通方式，促进员工与利益相关方的对话。 | 企业需证明其在社会责任活动中应用了透明、尊重的沟通方式。               |\n| ICF: 国际教练联合会认证  | 要求教练在与客户互动中使用非暴力沟通的原则，增强客户的自我觉察能力。 | 教练需通过培训和实践，展示其在沟通中运用非暴力沟通的能力。              |\n| GDPR: 通用数据保护条例   | 在数据隐私沟通中应用非暴力沟通，确保信息透明且易于理解。           | 企业需提供清晰的隐私政策说明，并在沟通中避免使用模糊或误导性语言。         |\n\n---\n\n### 结论\n\n非暴力沟通作为一种有效的沟通方式，能够帮助个人和组织在复杂的环境中实现更高效、更和谐的互动。通过结合 Mermaid 分析图的可视化能力，这一沟通方法可以被更直观地学习和应用，从而在职场、家庭和跨文化交流中发挥重要作用。同时，合规认证的引入为非暴力沟通在专业领域的实施提供了保障。',
  referenceList: [],
}

const PreviewPage = () => {
  const handleLoginClick = (e: React.MouseEvent) => {
    e.preventDefault()
    window.location.href = 'https://ai-smarties.com/deep-research'
  }

  const router = useRouter()
  const { reportId } = router.query

  const getReport = async () => {
    try {
      await getReportById({ reportId: reportId as string })
    } catch (e) {
      console.error(e)
    }
  }

  useEffect(() => {
    if (reportId) {
      getReport()
    }
  }, [reportId])

  return (
    <>
      {/* Main Content */}
      <main className='py-2 md:py-4'>
        <div className='mx-auto max-w-7xl px-2 sm:px-4'>
          <div className='flex flex-col lg:flex-row lg:gap-6'>
            {/* Left Content Area */}
            <div className='w-full flex-1 lg:max-w-5xl'>
              <div className='mt-24 px-12 md:mt-0'>
                <MarkdownParser content={a.content} list={a.referenceList} />
              </div>
              <div className='py-12'>
                <CallToAction />
              </div>
            </div>

            {/* Right Sidebar */}
            <div className='mt-4 w-full lg:mt-0 lg:w-80'>
              <div className='lg:sticky lg:top-4'>
                {/* 免费试用引导 */}
                <div className='mb-4'>
                  <FreeTrialPromo
                    asm-tracking='CLICK_SEO_SIGNUP:CLICK'
                    asm-tracking-p-view='Desktop'
                    asm-tracking-p-position='FreeTrialPromo'
                    onButtonClick={handleLoginClick}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

export default PreviewPage
