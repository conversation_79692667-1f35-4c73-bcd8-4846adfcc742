import { isJsonStr } from '@/utils/isJson'
import { useEffect, useRef, useState } from 'react'
import { v4 as uuidv4 } from 'uuid'
import axios from 'axios'
import { getCookie, setCookie } from '@/lib/cookie'

export const enum SocketMessageStatus {
  OUTPUTING = 'OUTPUTING',
  FINISH = 'FINISH',
  ERROR = 'ERROR',
}

export interface SocketMessageData {
  message: any
  status: SocketMessageStatus
}

export interface SocketMessage {
  chunk_id: number
  data: SocketMessageData & string
  task_id: string
  type: string
}

type UseWebSocketWithReconnectionProps = {
  onMessage?: (message: SocketMessage) => void // 可选的消息回调函数
  onRetryRefresh?: () => void // 可选的消息回调函数
}

const useWebSocketWithReconnection = ({
  onMessage,
  onRetryRefresh,
}: UseWebSocketWithReconnectionProps) => {
  const socketRef = useRef<WebSocket | null>(null)
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const reconnectAttempts = useRef(0)
  const lastPongTimestamp = useRef(Date.now())
  const inactivityTimeout = 30 * 60 * 1000 // 30 分钟
  const isManualClose = useRef(false) // 新增标记变量
  const countPongRef = useRef<number[]>([])
  let inactivityTimer: ReturnType<typeof setTimeout>
  const startCountPong = (message: { type: string }) => {
    if (message.type === 'pong') {
      console.log(new Date(), 'pong message: ', message)
      countPongRef.current.push(Date.now())
      if (countPongRef.current.length >= 2) {
        const last = countPongRef.current[countPongRef.current.length - 1]
        const secondLast = countPongRef.current[countPongRef.current.length - 2]
        const interval = last - secondLast
        console.log(interval, '++++')
        if (interval > 35000) {
          onRetryRefresh?.()
        }
      }
    }
  }

  const handleMessage = (event: MessageEvent) => {
    const message = JSON.parse(isJsonStr(event.data) ? event.data : '{}')
    console.log(new Date(), 'receive message:', message)

    if (message.type === 'pong') {
      lastPongTimestamp.current = Date.now()
    }

    startCountPong(message)

    if (onMessage) {
      onMessage(message)
    }
  }

  // 非主动关闭后 重连
  const handleClose = (event: CloseEvent) => {
    if (!isManualClose.current) {
      console.log('WebSocket closed!', event.code, event.reason)
    }
    if (isManualClose.current) {
      console.log('WebSocket Manual closed!', event.code, event.reason)
    }
    setIsConnected(false)
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current)
      heartbeatIntervalRef.current = null
    }
    // 判断是否是主动关闭，非主动关闭时才执行重连
    if (!isManualClose.current) {
      attemptReconnect()
    }
  }

  // 获取或创建访问令牌
  // 如果本地没有token，则自动进行匿名登录获取新token
  const getOrCreateAccessToken = async () => {
    // 先从Cookie和localStorage中获取现有的token
    let token = getCookie('smartToken') || localStorage.getItem('accessToken')

    if (!token) {
      // 如果没有token，进行匿名登录
      try {
        // 获取或创建匿名用户ID
        let anonymousId = localStorage.getItem('anonymousId')
        if (!anonymousId) {
          anonymousId = uuidv4()
          localStorage.setItem('anonymousId', anonymousId)
          const smartiesDomain = process.env.NEXT_PUBLIC_SMARTIES_DOMAIN || 'ai-smarties.com'
          localStorage.setItem('smartiesDomain', smartiesDomain)
          setCookie('anonymousId', anonymousId, {
            httpOnly: false,
            domain: smartiesDomain,
          })
        }

        console.log('开始匿名登录获取访问令牌...')
        // 调用匿名登录API获取访问令牌
        const res = await axios.post(
          `${process.env.NEXT_PUBLIC_AUTH_API_URL}/auth/loginAnonymous`,
          {
            anonymousId,
          },
        )
        token = res.data.accessToken
        if (token) {
          // 将新获取的token保存到本地存储
          localStorage.setItem('accessToken', token)
          console.log('✅ 匿名登录成功，获取到访问令牌')
        }
      } catch (err) {
        console.error('❌ 匿名登录失败:', err)
        return null
      }
    }

    return token
  }

  // 启动WebSocket连接
  // 会自动处理身份验证，包括匿名登录获取token
  const startWebSocket = async () => {
    // 获取或创建访问令牌（如果没有token会自动匿名登录）
    const accessToken = await getOrCreateAccessToken()

    // 调试信息：检查环境变量和token
    const socketUrl = process.env.NEXT_PUBLIC_SOCKET
    console.log('🔗 WebSocket配置检查:')
    console.log('- SOCKET URL:', socketUrl)
    console.log('- Access Token存在:', !!accessToken)
    console.log('- Token长度:', accessToken?.length)

    if (!socketUrl) {
      console.error('❌ NEXT_PUBLIC_SOCKET 环境变量未配置！请检查环境变量设置。')
      return
    }

    if (!accessToken) {
      console.error('❌ 无法获取访问令牌！请检查网络连接后重试。')
      return
    }

    if (!socketRef.current && accessToken) {
      const url = `${socketUrl}?Authorization=${encodeURIComponent('Bearer ' + accessToken)}`
      console.log('🚀 尝试连接WebSocket:', url.replace(/Bearer\s+[^&]+/, 'Bearer ***'))

      try {
        socketRef.current = new WebSocket(url)
        isManualClose.current = false

        socketRef.current.onopen = () => {
          console.log(' WebSocket connected!')
          setIsConnected(true)
          reconnectAttempts.current = 0 // 重置重连次数
          lastPongTimestamp.current = Date.now()

          // 发送心跳的定时器
          heartbeatIntervalRef.current = setInterval(() => {
            if (socketRef.current?.readyState === WebSocket.OPEN) {
              socketRef.current.send(JSON.stringify({ type: 'ping' }))
              console.log(' send heartbeat!', JSON.stringify({ type: 'ping' }))
            }
          }, 30000)

          if (socketRef.current) {
            // 将消息和关闭事件绑定到 WebSocket 上
            socketRef.current.onmessage = handleMessage
            socketRef.current.onclose = handleClose
          }
        }

        socketRef.current.onerror = (error) => {
          console.error('❌ WebSocket连接错误:', error)
          console.error('可能的原因:')
          console.error('1. JWT Token已过期，请重新登录')
          console.error('2. WebSocket服务端无法访问')
          console.error('3. 网络连接问题')
          console.error('4. 认证权限不足')
        }
      } catch (error) {
        console.error('❌ WebSocket创建失败:', error)
      }
    } else {
      if (
        socketRef.current?.readyState === WebSocket.CLOSED ||
        socketRef.current?.readyState === WebSocket.CLOSING
      ) {
        setIsConnected(false)
        socketRef.current = null
        attemptReconnect()
      }
    }
  }

  // 重连
  const attemptReconnect = () => {
    const timeSinceLastPong = Date.now() - lastPongTimestamp.current
    if (reconnectAttempts.current < 4 && timeSinceLastPong >= 10000) {
      // 超过60秒未收到 pong
      reconnectAttempts.current += 1
      console.log(`retry connect ${reconnectAttempts.current}`)
      startWebSocket()
    } else if (reconnectAttempts.current > 3) {
      onRetryRefresh?.()
      console.log('Max reconnection attempts reached. Stopping further attempts.')
    }
  }

  // 监听用户活动，释放连接
  const handleUserActivity = () => {
    clearTimeout(inactivityTimer)
    inactivityTimer = setTimeout(() => {
      console.log('Disconnecting due to 30 minutes of inactivity.')
      closeWebSocket()
    }, inactivityTimeout)
  }

  // 主动关闭
  const closeWebSocket = () => {
    if (socketRef.current) {
      isManualClose.current = true // 标记为主动关闭
      socketRef.current.close()
      socketRef.current = null
      setIsConnected(false)
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
        heartbeatIntervalRef.current = null
      }
    }
  }
  // 初始化 - 建立连接,
  useEffect(() => {
    startWebSocket()

    // 监听用户活动以防止自动断开
    window.addEventListener('mousemove', handleUserActivity)
    window.addEventListener('keypress', handleUserActivity)

    // 清理资源
    return () => {
      window.removeEventListener('mousemove', handleUserActivity)
      window.removeEventListener('keypress', handleUserActivity)
      clearTimeout(inactivityTimer)
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
        heartbeatIntervalRef.current = null
      }

      if (socketRef.current) {
        isManualClose.current = true
        socketRef.current.close() // 关闭 WebSocket 连接
        socketRef.current = null
      }
    }
  }, [])

  return {
    socket: socketRef.current,
    isConnected,
    closeWebSocket,
    startWebSocket,
    retryFailed: reconnectAttempts.current > 3,
    startCountPong,
  }
}

export default useWebSocketWithReconnection
