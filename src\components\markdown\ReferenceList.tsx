import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import clsx from 'clsx'
import { ReferenceItem } from './MarkdownEnums'

export interface ReferenceList {
  referenceList?: ReferenceItem[]
  className?: string
}

const ReferenceList: React.FC<ReferenceList> = ({ referenceList, className }: ReferenceList) => {
  const { t } = useTranslation(Namespace.SHARE)

  const handleClick = (url: string) => {
    window.open(url, '_blank')
  }

  return (
    <>
      {referenceList && referenceList.length > 0 && (
        <>
          <br />
          <h5 className={clsx('my-1', className)}>{t(`refer`)}</h5>
          {referenceList.map((item, idx) => {
            const isReportItem = 'ref' in item
            return (
              <div key={idx} className='break-all'>
                <div className='mr-2 inline leading-4'>
                  {idx + 1}.&nbsp;&nbsp;{isReportItem ? item.ref.title : item.title}
                </div>
                <div
                  onClick={() => {
                    handleClick(isReportItem ? item.ref.url : item.url)
                  }}
                  className='inline cursor-pointer break-words leading-4 text-primary'>
                  {isReportItem ? item.ref.url : item.url}
                </div>
              </div>
            )
          })}
        </>
      )}
    </>
  )
}

export default ReferenceList
