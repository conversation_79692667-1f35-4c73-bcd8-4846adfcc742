/* eslint-disable react/no-unknown-property */
import React, { useRef } from 'react'
import { CollapsibleSection } from '../common/CollapsibleSection'
import dynamic from 'next/dynamic'
import { Language } from '@/types/market'
import RelatedArticles from './RelatedArticles'
import FreeTrialPromo from './FreeTrialPromo'
import BaseLayout from '../layouts/BaseLayout'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

const YouTubePreview = dynamic(() => import('../common/YouTubePreview'), {
  loading: () => <div className='h-48 animate-pulse rounded-lg bg-gray-200'></div>,
  ssr: false,
})

// import TableOfContents from './TableOfContents'

interface MarketLayoutProps {
  children: React.ReactNode
  relatedArticles?: Array<{
    title: string
    url: string
  }>
  currentLang?: Language
}

const CommonLayout: React.FC<MarketLayoutProps> = ({ children, relatedArticles, currentLang }) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const { t } = useTranslation(Namespace.GLOBAL)
  const handleLoginClick = (e: React.MouseEvent) => {
    e.preventDefault()
    window.location.href = 'https://ai-smarties.com/deep-research'
  }

  return (
    <>
      {/* Main Content */}
      <main className='py-2 md:py-4'>
        <div className='mx-auto max-w-7xl px-2 sm:px-4'>
          <div className='flex flex-col lg:flex-row lg:gap-6'>
            {/* Left Content Area */}
            <div className='w-full flex-1 lg:max-w-5xl'>
              <div className='min-w-0'>
                <div ref={contentRef}>{children}</div>
              </div>
            </div>

            {/* Right Sidebar */}
            <div className='mt-4 w-full lg:mt-0 lg:w-80'>
              <div className='lg:sticky lg:top-4'>
                {/* 免费试用引导 */}
                <div className='mb-4'>
                  <FreeTrialPromo
                    asm-tracking='CLICK_SEO_SIGNUP:CLICK'
                    asm-tracking-p-view='Desktop'
                    asm-tracking-p-position='FreeTrialPromo'
                    onButtonClick={handleLoginClick}
                  />
                </div>

                {/* 文章目录 */}
                {/*<CollapsibleSection*/}
                {/*  title='Section Navigation'*/}
                {/*  storageKey='toc_section'*/}
                {/*  className='mb-4'>*/}
                {/*  <TableOfContents containerRef={contentRef} />*/}
                {/*</CollapsibleSection>*/}

                {/* YouTube Videos */}
                <CollapsibleSection
                  title={t('introduction.whatIsAiSmarties.title')}
                  subtitle={t('introduction.whatIsAiSmarties.subTitle')}
                  storageKey='youtube_section'
                  initialExpanded={false}
                  className='mb-4'>
                  <YouTubePreview
                    videos={[
                      {
                        url: 'https://youtu.be/IL7tLNyyVgo',
                        description:
                          'See how AI Smarties transforms your business research workflow with intelligent automation and insights.',
                      },
                      {
                        url: 'https://youtu.be/o5zK6KubW2U',
                        description:
                          'Explore the powerful features that make AI Smarties your ultimate research companion.',
                      },
                      {
                        url: 'https://youtu.be/en0JbC5VwOI',
                        description:
                          'Learn how businesses are achieving better results with AI Smarties.',
                      },
                    ]}
                    titleMaxLength={50}
                    descriptionMaxLength={100}
                  />
                </CollapsibleSection>

                {/* 推荐文章 */}
                <CollapsibleSection
                  title={t('introduction.relatedArticles.title')}
                  subtitle={t('introduction.relatedArticles.subTitle')}
                  storageKey='related_section'>
                  {relatedArticles && currentLang && <RelatedArticles articles={relatedArticles} />}
                </CollapsibleSection>
                {/* 广告位 */}
                {/* <div className='mt-4 rounded-lg bg-white p-4 shadow-sm'>
                  <h3 className='mb-3 text-lg font-semibold text-gray-800'>Advertisement</h3>
                  <div className='rounded-lg bg-gradient-to-br from-gray-50 to-gray-100 p-4 text-center'>
                    <p className='text-gray-600'>Advertisement Space</p>
                  </div>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Login Modals */}
      {/* <LoginModal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} /> */}
      {/*<LoginIframeModal*/}
      {/*  isOpen={isLoginIframeModalOpen}*/}
      {/*  onClose={() => setIsLoginIframeModalOpen(false)}*/}
      {/*/>*/}
    </>
  )
}

export default CommonLayout
