import React, { useEffect, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'

// 组件类型接口
interface MarkdownRendererProps {
  content: string // Markdown 内容
  isStreaming?: boolean // 是否为流式渲染
  className?: string // 自定义样式类名
  rehypePlugins?: any[] // rehype 插件列表
  remarkPlugins?: any[] // remark 插件列表
  components?: Record<string, any> // 自定义组件配置，可使用 createMarkdownComponents 创建
  streamDelay?: number // 流式渲染的延迟时间(ms)
}

/**
 * 通用的 Markdown 渲染组件
 * 支持普通渲染和流式渲染两种模式
 *
 * 推荐使用方式:
 * 1. 导入 createMarkdownComponents 创建统一的组件
 * 2. 将组件传入 MarkdownRenderer 的 components 属性
 * 3. 流式渲染时设置 isStreaming=true
 */
const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content = '',
  isStreaming = false,
  className = 'prose max-w-none',
  rehypePlugins = [rehypeRaw],
  remarkPlugins = [remarkGfm],
  components = {},
  streamDelay = 0,
}) => {
  // 用于存储当前渲染的内容
  const [displayContent, setDisplayContent] = useState<string>(isStreaming ? '' : content)

  // 追踪流式渲染的上一次内容，用于增量更新
  const [prevContent, setPrevContent] = useState<string>('')

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (!isStreaming) {
      // 非流式模式下直接设置内容
      setDisplayContent(content)
      return
    }

    // 只有在内容有变化且变长时才更新（流式通常只会增加内容）
    if (content !== prevContent && content.length >= prevContent.length) {
      setPrevContent(content)

      if (streamDelay > 0) {
        // 有延迟的流式渲染
        const timer = setTimeout(() => {
          setDisplayContent(content)
        }, streamDelay)

        return () => clearTimeout(timer)
      } else {
        // 无延迟立即渲染
        setDisplayContent(content)
      }
    }
  }, [content, isStreaming, prevContent, streamDelay])

  return (
    <ReactMarkdown
      className={className}
      rehypePlugins={rehypePlugins}
      remarkPlugins={remarkPlugins}
      components={components}>
      {displayContent}
    </ReactMarkdown>
  )
}

export default MarkdownRenderer
