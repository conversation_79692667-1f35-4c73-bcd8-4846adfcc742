import { getUser } from '@/api/user'
import { Namespace } from '@/i18n'
import { useUserStore } from '@/store/userStore'
import { Environments, initializePaddle, Paddle, PaddleEventData, Theme } from '@paddle/paddle-js'
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENV! as Environments
const paddleToken = process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN!

export const PaddlePay = (props: any, ref: any) => {
  const { i18n } = useTranslation(Namespace.GLOBAL)

  const [paddle, setPaddle] = useState<Paddle>()

  const lastPaddleEvent = useRef<undefined | string>(undefined)

  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)

  useEffect(() => {
    if (!user) {
      getUser().then((data) => {
        if (data.userId) {
          updateUser(data)
        }
      })
    }
  }, [user])

  useImperativeHandle(ref, () => ({
    openCheckout: ({ priceId, quantity = 1 }: { priceId: string; quantity: number }) => {
      console.log('openCheckout', props.taskId)
      const user_language = i18n.language === 'zh' ? '中文' : '英文'
      const lang = localStorage.getItem('userLanguage') || user_language
      const paddleConfig: any = {
        settings: {
          locale: lang === 'zh' ? 'zh-Hans' : 'en',
        },
        items: [{ priceId, quantity }],
        customData: {
          userId: user?.userId,
          taskId: props.taskId,
        },
      }
      if (user?.email) {
        paddleConfig.customer = {
          email: user?.email,
        }
      }
      paddle?.Checkout.open(paddleConfig)
    },
  }))

  useEffect(() => {
    if (!paddleEnv || !paddleToken) {
      return
    }

    initializePaddle({
      environment: paddleEnv,
      token: paddleToken,
      eventCallback: (event: PaddleEventData) => {
        if (event.name === 'checkout.completed') {
          console.log('checkout.completed', event)
          lastPaddleEvent.current = event.name
          if (!paddle?.Checkout) {
            console.log('paddle?.Checkout is undefined')
            return
          }
          if (!props.afterPayAction) {
            console.log('props.afterPayAction is undefined')
            return
          }
          setTimeout(() => {
            // console.log(event.name, event?.data?.transaction_id, event?.data?.status)
            paddle?.Checkout.close()
            props.afterPayAction?.()
          }, 1000)
        }
      },
      checkout: {
        settings: {
          allowLogout: false,
          displayMode: 'overlay',
          allowedPaymentMethods: [
            'alipay',
            'apple_pay',
            'bancontact',
            'card',
            'google_pay',
            'ideal',
            'paypal',
          ],
        },
      },
    }).then((paddleInstance: Paddle | undefined) => {
      if (paddleInstance) {
        setPaddle(paddleInstance)
      }
    })
  }, [])

  return <></>
}

export default forwardRef(PaddlePay)
