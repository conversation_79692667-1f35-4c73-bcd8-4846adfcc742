import React from 'react'
import BaseLayout from './BaseLayout'

interface SimpleLayoutProps {
  children: React.ReactNode
  title?: string
  fullWidth?: boolean
}

const SimpleLayout: React.FC<SimpleLayoutProps> = ({ children, title, fullWidth = false }) => {
  return (
    <>
      {/* Simple content container with optional title */}
      <div className='lg:py-4'>
        <div className={`mx-auto ${!fullWidth ? 'max-w-7xl' : 'w-full'} sm:px-1 lg:px-8`}>
          {title && (
            <div className='mb-6'>
              <h1 className='text-2xl font-bold text-gray-900 sm:text-3xl'>{title}</h1>
            </div>
          )}
          <div className='sm:rounded-lg lg:p-6'>{children}</div>
        </div>
      </div>
    </>
  )
}

export default SimpleLayout
