import React from 'react'
import Mark<PERSON><PERSON>ender<PERSON> from './MarkdownRenderer'
import createMarkdownComponents from './MarkdownComponents'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'

/**
 * Mermaid 图表测试组件
 * 用于测试 Mermaid 图表的渲染功能
 */
const MermaidTest: React.FC = () => {
  // 使用统一的 Markdown 组件
  const components = createMarkdownComponents()

  // 测试用的 Markdown 内容，包含 Mermaid 图表
  const markdownContent = `
# Mermaid 图表测试

下面是一个饼图示例：

\`\`\`mermaid
pie
    title 行业收入占比
    "金融" : 52
    "零售" : 24
    "制造" : 15
    "其他" : 9
\`\`\`

下面是一个流程图示例：

\`\`\`mermaid
graph TD
    A[开始] --> B{是否有库存?}
    B -->|是| C[发货]
    B -->|否| D[补货]
    D --> B
    C --> E[结束]
\`\`\`

下面是一个时序图示例：

\`\`\`mermaid
sequenceDiagram
    participant 客户
    participant 系统
    participant 客服

    客户->>系统: 提交订单
    系统->>系统: 验证订单
    系统->>客户: 确认订单
    客户->>系统: 支付
    系统->>客服: 通知发货
    客服->>客户: 发送物流信息
\`\`\`

## 普通代码块

\`\`\`js
// 这是普通代码块
function hello() {
  console.log('Hello, world!');
}
\`\`\`
`

  return (
    <div className='mx-auto max-w-4xl p-4'>
      <h1 className='mb-4 text-2xl font-bold'>Mermaid 图表测试</h1>
      <div className='rounded-lg border border-gray-200 bg-white p-4'>
        <MarkdownRenderer
          content={markdownContent}
          className='prose max-w-none'
          rehypePlugins={[rehypeRaw]}
          remarkPlugins={[remarkGfm]}
          components={components}
        />
      </div>
    </div>
  )
}

export default MermaidTest
