import React, { useState, useRef } from 'react'
import Head from 'next/head'
import EditableTableOfContents, { TocItem, EditableTableOfContentsRef } from '@/components/common/EditableTableOfContents'
import SimpleLayout from '@/components/layouts/SimpleLayout'

// 使用静态ID的示例数据
const sampleData: TocItem[] = [
    {
        id: 'toc-item-1',
        title: '1. 引言',
        description: '介绍本报告的研究背景、目的和意义',
        level: 1,
        children: []
    },
    {
        id: 'toc-item-2',
        title: '2. 研究方法',
        description: '详细说明本研究采用的方法论和数据收集方式',
        level: 1,
        children: [
            {
                id: 'toc-item-2-1',
                title: '2.1 数据来源',
                description: '描述研究数据的收集方法和来源',
                level: 2,
                children: []
            },
            {
                id: 'toc-item-2-2',
                title: '2.2 分析方法',
                description: '解释数据分析过程中使用的统计方法',
                level: 2,
                children: []
            }
        ]
    },
    {
        id: 'toc-item-3',
        title: '3. 市场分析',
        description: '分析当前市场状况、竞争格局和发展趋势',
        level: 1,
        children: [
            {
                id: 'toc-item-3-1',
                title: '3.1 市场规模',
                description: '介绍全球及各区域市场的规模和增长率',
                level: 2,
                children: []
            },
            {
                id: 'toc-item-3-2',
                title: '3.2 主要参与者',
                description: '列出行业中的主要企业及其市场份额',
                level: 2,
                children: [
                    {
                        id: 'toc-item-3-2-1',
                        title: '3.2.1 国际企业',
                        description: '国际市场主要企业分析',
                        level: 3,
                        children: []
                    },
                    {
                        id: 'toc-item-3-2-2',
                        title: '3.2.2 国内企业',
                        description: '国内市场主要企业分析',
                        level: 3,
                        children: []
                    }
                ]
            }
        ]
    },
    {
        id: 'toc-item-4',
        title: '4. 结论与建议',
        description: '总结研究发现并提出战略建议',
        level: 1,
        children: []
    }
];

const EditableTocDemoPage = () => {
    const [tocData, setTocData] = useState<TocItem[]>(sampleData)
    const [latestData, setLatestData] = useState<TocItem[]>([])
    const [showPreview, setShowPreview] = useState<boolean>(true)
    const tocRef = useRef<EditableTableOfContentsRef>(null)

    // 处理目录数据变化
    const handleTocChange = (newData: TocItem[]) => {
        setTocData(newData)
    }

    // 获取最新数据
    const handleGetLatestData = () => {
        if (tocRef.current) {
            const latest = tocRef.current.getLatestData()
            setLatestData(latest)
        }
    }

    return (
        <>
            <Head>
                <title>可编辑目录示例 - AISmarties</title>
                <meta name="description" content="可编辑的内容目录示例页面" />
            </Head>

            <SimpleLayout>
                <div className="max-w-6xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                    <div className="mb-10 text-center">
                        <h1 className="text-3xl font-bold text-gray-900 mb-4">可编辑目录组件示例</h1>
                        <p className="text-gray-600">
                            这个演示页面展示了可编辑的目录组件功能，支持编辑标题和描述、删除单个或批量删除项目。
                        </p>
                    </div>

                    <div className="mb-6 flex justify-end">
                        <div className="flex items-center">
                            <label htmlFor="toggle-preview" className="mr-2 text-sm font-medium text-gray-700">
                                显示预览区
                            </label>
                            <div className="relative inline-block w-10 mr-2 align-middle select-none">
                                <input
                                    type="checkbox"
                                    id="toggle-preview"
                                    checked={showPreview}
                                    onChange={() => setShowPreview(!showPreview)}
                                    className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 border-gray-300 appearance-none cursor-pointer"
                                    style={{
                                        right: showPreview ? '0' : '4px',
                                        transition: 'right 0.2s',
                                        backgroundColor: showPreview ? '#3b82f6' : 'white',
                                    }}
                                />
                                <label
                                    htmlFor="toggle-preview"
                                    className="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"
                                    style={{
                                        backgroundColor: showPreview ? '#bfdbfe' : '#d1d5db',
                                    }}
                                ></label>
                            </div>
                        </div>
                    </div>

                    <div className="mb-6 bg-yellow-50 border-l-4 border-yellow-400 p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <p className="text-sm text-yellow-700">
                                    勾选目录项可以批量删除，编辑标题和描述将自动保存。拖拽目录项可以调整顺序和层级结构：
                                </p>
                                <ul className="mt-1 list-disc list-inside text-xs text-yellow-700 pl-2">
                                    <li>拖到项目上方1/4位置 - 放在该项目前面</li>
                                    <li>拖到项目下方1/4位置 - 放在该项目后面</li>
                                    <li>拖到项目中间位置 - 放在该项目内部作为子项</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                        <div className="p-6">
                            <h2 className="text-xl font-semibold text-gray-800 mb-4">报告目录</h2>
                            <EditableTableOfContents
                                ref={tocRef}
                                data={tocData}
                                onChange={handleTocChange}
                                className="mt-4"
                                showPreview={showPreview}
                            />
                        </div>
                    </div>

                    <div className="mt-8 flex justify-center">
                        <button
                            onClick={handleGetLatestData}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                        >
                            获取最新数据（含选中状态）
                        </button>
                    </div>

                    <div className="mt-10 bg-gray-50 rounded-lg p-6">
                        <h2 className="text-xl font-semibold text-gray-800 mb-4">原始目录数据:</h2>
                        <pre
                            suppressHydrationWarning={true}
                            className="bg-gray-800 text-gray-200 p-4 rounded-lg overflow-auto max-h-96 text-sm"
                        >
                            {JSON.stringify(tocData, null, 2)}
                        </pre>
                    </div>

                    {latestData.length > 0 && (
                        <div className="mt-10 bg-gray-50 rounded-lg p-6">
                            <h2 className="text-xl font-semibold text-gray-800 mb-4">最新目录数据（包含选中状态）:</h2>
                            <pre
                                suppressHydrationWarning={true}
                                className="bg-gray-800 text-gray-200 p-4 rounded-lg overflow-auto max-h-96 text-sm"
                            >
                                {JSON.stringify(latestData, null, 2)}
                            </pre>
                        </div>
                    )}
                </div>

                <style jsx>{`
                    .toggle-checkbox:checked {
                        right: 0;
                        border-color: #3b82f6;
                    }
                    .toggle-checkbox:checked + .toggle-label {
                        background-color: #bfdbfe;
                    }
                `}</style>
            </SimpleLayout>
        </>
    )
}

export default EditableTocDemoPage 