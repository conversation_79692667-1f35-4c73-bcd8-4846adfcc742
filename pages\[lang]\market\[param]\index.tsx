// @ts-nocheck
import React, { memo, useCallback, useEffect } from 'react'
import dynamic from 'next/dynamic'
import Head from 'next/head'
import { GetStaticPaths, GetStaticProps } from 'next'
import { Language, MarketData } from '@/types/market'
import { useRouter } from 'next/router'
import { References } from '@/components/seo/References'
import { getMarketData } from '@/utils/getMarketData'
import SeoMarkdownRenderer from '@/components/seo/SeoMarkdownRenderer'

// 动态导入组件
const MarketLayout = dynamic(() => import('@/components/seo/CommonLayout'), {
  loading: () => <div>Loading...</div>,
})

const LanguageSelector = dynamic(() => import('@/components/seo/LanguageSelector'), {
  loading: () => <div>Loading...</div>,
})

interface MarketPageProps {
  marketData: MarketData | null
  lang: Language
  relatedArticles: Array<{
    title: string
    url: string
  }>
  param: string
}

const MarketPage: React.FC<MarketPageProps> = memo(
  ({ marketData, lang, relatedArticles, param }) => {
    // 配置注册引导的插入位置
    const guideConfig = ['3', '4']

    // 追踪h2标签的计数
    // let h2Count = 0;
    const router = useRouter()

    useEffect(() => {
      // 追踪页面浏览事件
      // Tracking.trackPageView('[SEO]Market Page', true, {
      //   language: lang,
      //   market_param: param,
      //   market_domain: marketData?.marketDomain || '',
      //   market_region: marketData?.marketRegion || '',
      //   market_year: marketData?.marketYear || '',
      //   title: marketData?.title || '',
      // });
    }, [lang, param, marketData?.title])

    const handleLanguageChange = useCallback(
      (newLang: Language) => {
        const { param } = router.query
        router.push(`/${newLang}/market/${param}`)
      },
      [router],
    )

    const localizedContent = marketData?.localizedData[lang]
    const availableLanguages = marketData ? marketData.marketLangs : []

    // 如果页面正在生成中，显示加载状态
    if (router.isFallback || !marketData) {
      return (
        <MarketLayout>
          <div className='flex h-screen items-center justify-center'>
            <div className='text-lg'>Loading...</div>
          </div>
        </MarketLayout>
      )
    }

    // Get the current URL
    const currentURL =
      typeof window !== 'undefined'
        ? window.location.href
        : `https://smarties.ai/${lang}/market/${router.query.param}`

    // Get the logo URL based on environment
    const logoURL = process.env.NEXT_PUBLIC_LOGO_URL || 'https://smarties.ai/logo.png'

    // Format the date strings
    const publishDate = marketData.updatedAt
    const modifiedDate = marketData.updatedAt

    // Get keywords from related domains
    const keywords = [
      ...localizedContent.keywords.split(','),
      localizedContent.domain,
      ...marketData.relatedDomains,
    ].filter(Boolean)

    const jsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      isAccessibleForFree: true,
      headline: localizedContent.title,
      // "alternativeHeadline": "",
      description: localizedContent.description.substring(0, 200),
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': currentURL,
      },
      author: {
        '@type': 'Organization',
        name: 'AI-Smarties Group',
      },
      publisher: {
        '@type': 'Organization',
        name: 'AI-Smarties Group',
        logo: {
          '@type': 'ImageObject',
          url: logoURL,
        },
      },
      datePublished: publishDate,
      dateModified: modifiedDate,
      inLanguage: lang,
      articleSection: localizedContent.domain,
      keywords: keywords,
      about: [
        {
          '@type': 'Market Name',
          name: localizedContent.domain,
        },
        {
          '@type': 'Market Region',
          name: marketData.marketRegion,
        },
        {
          '@type': 'Market Year',
          name: marketData.marketYear,
        },
      ],
    }

    return (
      <div>
        <Head>
          <title>{localizedContent.title}</title>
          <meta
            name='description'
            content={(localizedContent.description || localizedContent.content).substring(0, 160)}
          />
          <meta name='keywords' content={localizedContent.keywords} />

          {/* Open Graph tags */}
          <meta property='og:title' content={localizedContent.title} />
          <meta
            property='og:description'
            content={(localizedContent.description || localizedContent.content).substring(0, 160)}
          />
          <meta property='og:url' content={currentURL} />
          <meta property='og:type' content='article' />
          <meta property='og:site_name' content='Smarties AI' />
          <meta property='og:image' content='https://ai-smarties.com/seo/images/logo.svg' />

          {/* Twitter Card tags */}
          <meta name='twitter:card' content='summary_large_image' />
          <meta name='twitter:title' content={localizedContent.title} />
          <meta
            name='twitter:description'
            content={(marketData.seoDescription || localizedContent.content).substring(0, 160)}
          />
          <meta name='twitter:image' content='https://ai-smarties.com/seo/images/logo.svg' />
          <meta property='twitter:creator' content='Jie@SmartiesAI' />

          {/* Alternate language versions */}
          {availableLanguages.map((altLang) => (
            <link
              key={altLang}
              rel='alternate'
              hrefLang={altLang}
              href={`https://smarties.ai/${altLang}/market/${router.query.param}`}
            />
          ))}

          {/* JSON-LD structured data */}
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
          />
        </Head>
        <MarketLayout relatedArticles={relatedArticles} currentLang={lang}>
          <div
            className='min-h-screen pb-20'
            asm-tracking='VISIT_SEO_PAGE:VIEW'
            asm-tracking-p-page='SEO_MARKET'
            asm-tracking-p-language={lang}
            asm-tracking-p-research_domain={marketData?.marketDomain || ''}
            asm-tracking-p-research_param={param}
            asm-tracking-p-research_region={marketData?.marketRegion || ''}
            asm-tracking-p-research_year={marketData?.marketYear || ''}
            asm-tracking-p-title={marketData?.title || ''}>
            <article className='overflow-hidden rounded-lg bg-white shadow-sm'>
              <div className='p-3 sm:p-4 md:p-6 lg:p-8'>
                <header className='border-b border-gray-100 pb-4'>
                  <h1 className='mb-4 bg-gradient-to-r from-indigo-600 to-indigo-400 bg-clip-text text-2xl font-bold text-transparent sm:mb-6 sm:text-3xl md:text-4xl'>
                    {localizedContent.title}
                  </h1>
                  <div className='flex flex-wrap gap-3 text-sm text-gray-500 sm:gap-3'>
                    <div className='flex items-center'>
                      <span className='mr-2 h-2.5 w-2.5 rounded-full bg-indigo-100 sm:h-3 sm:w-3 md:h-4 md:w-4'></span>
                      <span className='font-medium text-gray-700'>Theme:</span>
                      <span className='ml-1 sm:ml-2'>{localizedContent?.domain}</span>
                    </div>
                    <div className='flex items-center'>
                      <span className='mr-2 h-2.5 w-2.5 rounded-full bg-green-100 sm:h-3 sm:w-3 md:h-4 md:w-4'></span>
                      <span className='font-medium text-gray-700'>Region:</span>
                      <span className='ml-1 sm:ml-2'>{marketData.marketRegion}</span>
                    </div>
                    <div className='flex items-center'>
                      <span className='mr-2 h-2.5 w-2.5 rounded-full bg-yellow-100 sm:h-3 sm:w-3 md:h-4 md:w-4'></span>
                      <span className='font-medium text-gray-700'>Year:</span>
                      <span className='ml-1 sm:ml-2'>{marketData.marketYear}</span>
                    </div>
                  </div>

                  <div className='flex flex-wrap gap-3 text-sm text-gray-500 sm:gap-3'>
                    <div className='flex items-center'>
                      <span className='mr-2 h-2.5 w-2.5 rounded-full bg-blue-100 sm:h-3 sm:w-3 md:h-4 md:w-4'></span>
                      <span className='font-medium text-gray-700'>Updated:</span>
                      <span className='ml-1 sm:ml-2'>{marketData.updatedAt}</span>
                    </div>
                    <LanguageSelector
                      currentLang={lang}
                      availableLanguages={availableLanguages}
                      onLanguageChange={handleLanguageChange}
                    />
                  </div>
                </header>

                <div className='prose prose-sm sm:prose lg:prose-lg mx-auto mt-6'>
                  <SeoMarkdownRenderer
                    content={localizedContent.content}
                    citations={marketData?.citations}
                    guideConfig={guideConfig}
                  />
                </div>

                <hr className='my-8 border-t border-gray-100' />
                <References references={marketData?.references} citations={marketData?.citations} />

                <footer className='mt-6 border-t border-gray-100 pt-4 sm:mt-8 sm:pt-6 md:mt-12'>
                  <h2 className='mb-3 text-lg font-medium text-gray-800 sm:mb-4'>
                    Related Domains
                  </h2>
                  <div className='flex flex-wrap gap-2'>
                    {marketData.relatedDomains.map((domain) => (
                      <span
                        key={domain}
                        className='rounded-full border border-indigo-100 bg-gradient-to-r from-indigo-50 to-blue-50 px-2.5 py-1 text-xs font-medium text-indigo-700 transition-colors duration-200 hover:border-indigo-200 sm:px-3 sm:py-1.5 sm:text-sm md:px-4 md:py-2'>
                        {domain}
                      </span>
                    ))}
                  </div>
                </footer>

                {/* <TextEditMenu
              onExpand={handleExpand}
              onPolish={handlePolish}
              onClose={() => { }}
            /> */}
              </div>
            </article>
          </div>
        </MarketLayout>
      </div>
    )
  },
)

MarketPage.displayName = 'MarketPage' // 移动 displayName 到这里

export const getStaticPaths: GetStaticPaths = async () => {
  return {
    paths: [],
    fallback: 'blocking',
  }
}

export const getStaticProps: GetStaticProps<MarketPageProps> = async ({ params }) => {
  const lang = params?.lang as Language
  const param = params?.param as string

  try {
    // 获取当前文章数据
    const marketData = await getMarketData(param)

    if (!marketData) {
      return {
        notFound: true, // 使用 Next.js 的 404 页面
        revalidate: 60, // 1分钟后重新验证
      }
    }

    // 获取相关文章数据
    const relatedArticles = marketData.localizedData[lang]?.relates || [] // 这里可以实现相关文章的逻辑
    console.log('localizedData: ', marketData.localizedData[lang])
    console.log('relatedArticles: ', relatedArticles)

    // 优化：只保留当前语言的数据，减少数据量
    const optimizedMarketData = {
      ...marketData,
      localizedData: {
        [lang]: marketData.localizedData[lang],
      },
      // 只保留必要的字段
      marketDomain: marketData.marketDomain,
      marketRegion: marketData.marketRegion,
      marketYear: marketData.marketYear,
      marketLangs: Object.keys(marketData.localizedData) as Language[],
      updatedAt: marketData.updatedAt,
    }

    console.log('marketLangs: ', optimizedMarketData.marketLangs)

    return {
      props: {
        marketData: optimizedMarketData,
        lang,
        relatedArticles,
        param,
      },
      revalidate: 3600, // 每小时重新验证一次
    }
  } catch (error) {
    console.error('Error in getStaticProps:', error)
    return {
      notFound: true,
      revalidate: 60,
    }
  }
}

export default MarketPage
