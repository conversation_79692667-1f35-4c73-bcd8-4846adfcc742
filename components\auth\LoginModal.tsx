import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
}

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  if (!isOpen) return null

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle login logic here
    console.log('Login attempt with:', { email, password })
  }

  const handleGoogleLogin = () => {
    // Handle Google login
    console.log('Google login clicked')
  }

  const handleLinkedinLogin = () => {
    // Handle Linkedin login
    console.log('Linkedin login clicked')
  }

  return (
    <div className='bg-black/30 fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm'>
      <div className='relative w-full max-w-md transform overflow-hidden rounded-2xl border border-indigo-500/50 bg-white/90 p-8 text-left shadow-xl transition-all'>
        {/* Close button */}
        <button
          onClick={onClose}
          className='absolute right-4 top-4 text-gray-500 hover:text-gray-700'>
          <svg
            className='h-6 w-6'
            fill='none'
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth='2'
            viewBox='0 0 24 24'
            stroke='currentColor'>
            <path d='M6 18L18 6M6 6l12 12'></path>
          </svg>
        </button>

        {/* Modal content */}
        <div className='text-center'>
          <h2 className='mb-2 text-4xl font-bold text-gray-900'>Welcome!</h2>
          <p className='mb-8 text-sm text-gray-600'>
            Your AI Assistant for Real-Time, Deeper Business Research.
          </p>

          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='space-y-2 text-left'>
              <label className='block font-medium text-gray-700'>Email Address</label>
              <input
                type='email'
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder='Enter Email Address'
                className='w-full rounded-lg border border-gray-300 bg-white p-3 text-gray-900 placeholder-gray-400 focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500'
                required
              />
            </div>

            <div className='space-y-2 text-left'>
              <label className='block font-medium text-gray-700'>Password</label>
              <div className='relative'>
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder='8-20 characters, include upper, lower, and numbers.'
                  className='w-full rounded-lg border border-gray-300 bg-white p-3 text-gray-900 placeholder-gray-400 focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500'
                  required
                />
                <button
                  type='button'
                  onClick={() => setShowPassword(!showPassword)}
                  className='absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600'>
                  {showPassword ? (
                    <svg
                      className='h-5 w-5'
                      fill='none'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      viewBox='0 0 24 24'
                      stroke='currentColor'>
                      <path d='M15 12a3 3 0 11-6 0 3 3 0 016 0z'></path>
                      <path d='M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'></path>
                    </svg>
                  ) : (
                    <svg
                      className='h-5 w-5'
                      fill='none'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      viewBox='0 0 24 24'
                      stroke='currentColor'>
                      <path d='M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21'></path>
                    </svg>
                  )}
                </button>
              </div>
            </div>

            <Link
              href='/recover'
              className='block text-left text-sm text-indigo-600 hover:text-indigo-500'>
              Forget password?
            </Link>

            <button
              type='submit'
              className='w-full rounded-lg bg-primary py-3 text-white transition-colors hover:bg-indigo-700'>
              Continue
            </button>
          </form>

          <div className='my-6 flex items-center justify-center'>
            <div className='flex-grow border-t border-gray-200'></div>
            <span className='mx-4 text-gray-500'>or</span>
            <div className='flex-grow border-t border-gray-200'></div>
          </div>

          <div className='grid grid-cols-2 gap-4'>
            <button
              onClick={handleGoogleLogin}
              className='flex items-center justify-center space-x-2 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50'>
              <Image src='/images/google.png' alt='Google' width={20} height={20} />
              <span>Google</span>
            </button>
            <button
              onClick={handleLinkedinLogin}
              className='flex items-center justify-center space-x-2 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50'>
              <Image src='/images/linkedin.png' alt='LinkedIn' width={20} height={20} />
              <span>LinkedIn</span>
            </button>
          </div>

          <p className='mt-6 text-gray-600'>
            Don&#39;t have an account?{' '}
            <Link href='/signup' className='text-indigo-600 hover:text-indigo-500'>
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

export default LoginModal
