import { Language, LocalizedContent, MarketData } from '@/types/market'

function removeFirstH1(content: string): string {
  if (content != undefined) {
    // 移除开头的 # 标题（包括可能的空行）
    return content.replace(/^#\s+[^\n]+\n+/, '')
  }
  return content
}

export async function getMarketData(param: string): Promise<MarketData | null> {
  try {
    const url = `https://digismarties-public.s3.ap-northeast-1.amazonaws.com/seo/data/market/${param}.json`
    console.log('url: ', url)
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const marketData = (await response.json()) as MarketData

    // 处理每种语言的内容，创建新对象而不是直接修改
    const processedMarketData: MarketData = {
      ...marketData,
      localizedData: Object.fromEntries(
        Object.entries(marketData.localizedData).map(([lang, data]) => [
          lang as Language,
          {
            ...data,
            content: removeFirstH1(data.content),
          },
        ]),
      ) as Record<Language, LocalizedContent>,
    }

    return processedMarketData
  } catch (error) {
    console.error(`Failed to load market data for ${param}:`, error)
    return null
  }
}
