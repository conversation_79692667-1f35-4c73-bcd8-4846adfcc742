import type { DocumentProps } from 'next/document'
import { Head, Html, Main, NextScript } from 'next/document'

const VALID_LANGUAGES = ['en', 'de', 'fr', 'ja', 'zh-CN']

export default function Document(props: DocumentProps) {
  // Get the pathname from asPath
  const pathname = props.__NEXT_DATA__?.page || ''

  // Extract language from the pathname (first segment after /)
  const pathLang = pathname.split('/')[1]

  // Use the path language if it's valid, otherwise default to 'en'
  const lang = VALID_LANGUAGES.includes(pathLang) ? pathLang : 'en'

  return (
    <Html lang={lang}>
      <Head>
        <meta charSet='utf-8' />
        {/* Favicon 设置 */}
        <link rel='icon' href='/images/logo.svg' type='image/svg+xml' />
        <link rel='icon' href='/images/logo.svg' type='image/x-icon' />
        <link rel='shortcut icon' href='/images/logo.svg' />
        <link rel='apple-touch-icon' href='/images/logo.svg' />
        <meta name='msapplication-TileImage' content='/images/logo.svg' />
        {/*<meta*/}
        {/*  name='description'*/}
        {/*  content='Your AI Assistant for Real-Time Market Research, Competitor Analysis, SWOT, and Compliance Insights.'*/}
        {/*/>*/}
        {/*<meta*/}
        {/*  name='keywords'*/}
        {/*  content='Al-powered answer engine, AI copilot for small business, AI assistant, market research tools, ai market research, small business using ai, business writing, business plan software, free swot analysis software, export compliance AI tool, free risk assessment software'*/}
        {/*/>*/}
        {/*<meta name='google' content='notranslate' />*/}
        {/*<script src='https://accounts.google.com/gsi/client' async />*/}
        {/*<link*/}
        {/*  rel='preload'*/}
        {/*  href='https://d1ij1j35k83uw7.cloudfront.net/player/player.js'*/}
        {/*  as='script'*/}
        {/*/>*/}
        {/*<link*/}
        {/*  rel='preload'*/}
        {/*  href='https://d1ij1j35k83uw7.cloudfront.net/pdfjs/3.11.174/pdf.worker.min.js'*/}
        {/*  as='script'*/}
        {/*/>*/}
        {/*<link rel='stylesheet' href='https://at.alicdn.com/t/c/font_4801925_8hptmglrc39.css' />*/}
        {/*<script src='https://at.alicdn.com/t/c/font_4801925_8hptmglrc39.js' async />*/}
      </Head>
      <body className='overscroll-y-none'>
        <Main />
        <NextScript />
        <div id='modal' />
      </body>
    </Html>
  )
}
