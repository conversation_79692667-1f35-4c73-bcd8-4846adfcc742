import { ask as ask<PERSON><PERSON>A<PERSON> } from './openai'
import { ask as askDeepseek, ask_with_stream as askDeepseekStream } from './volcengine'

// 定义支持的模型类型
export type LLMProvider =
  | 'deepseekV3'
  | 'deepseekR1'
  | 'doubao256k'
  | 'openai'
  | 'anthropic'
  | 'google'

// 定义 LLM 配置接口
export interface LLMConfig {
  model: LLMProvider
  temperature?: number
}

// 默认配置
const DEFAULT_CONFIG: LLMConfig = {
  model: 'deepseekV3',
  temperature: 0.7,
}

// 统一的 LLM 调用接口
export async function ask(
  sys_prompt: string,
  user_query: string,
  config: Partial<LLMConfig> = {},
): Promise<any> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }

  try {
    switch (finalConfig.model) {
      case 'deepseekV3':
      case 'deepseekR1':
      case 'doubao256k':
        return await askDeepseek(sys_prompt, user_query, finalConfig.model)
      case 'openai':
        return await askOpenAI(sys_prompt, user_query)
      case 'anthropic':
        // TODO: 实现 Anthropic Claude 调用
        throw new Error('Anthropic Claude integration not implemented yet')
      case 'google':
        // TODO: 实现 Google PaLM 调用
        throw new Error('Google PaLM integration not implemented yet')
      default:
        throw new Error(`Unsupported LLM provider: ${finalConfig.model}`)
    }
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`LLM API call failed: ${error.message}`)
    }
    throw new Error('An unexpected error occurred during LLM API call')
  }
}

// 统一的流式 LLM 调用接口
export async function ask_with_stream(
  sys_prompt: string,
  user_query: string,
  onChunk: (chunk: string) => void,
  config: Partial<LLMConfig> = {},
): Promise<void> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }

  try {
    switch (finalConfig.model) {
      case 'deepseekV3':
      case 'deepseekR1':
      case 'doubao256k':
        return await askDeepseekStream(sys_prompt, user_query, onChunk, finalConfig.model)
      case 'openai':
        // TODO: 实现 OpenAI 流式调用
        throw new Error('OpenAI streaming not implemented yet')
      case 'anthropic':
        // TODO: 实现 Anthropic Claude 流式调用
        throw new Error('Anthropic Claude streaming not implemented yet')
      case 'google':
        // TODO: 实现 Google PaLM 流式调用
        throw new Error('Google PaLM streaming not implemented yet')
      default:
        throw new Error(`Unsupported LLM provider: ${finalConfig.model}`)
    }
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`LLM streaming API call failed: ${error.message}`)
    }
    throw new Error('An unexpected error occurred during LLM streaming API call')
  }
}
