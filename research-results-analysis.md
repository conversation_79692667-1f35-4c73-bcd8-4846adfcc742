# Research Results 页面详细分析

## 概述

`research-results.tsx` 是一个复杂的 React 页面组件，用于展示深度研究的结果。该页面的核心功能是通过 WebSocket 实时接收研究进度和内容，并以结构化的方式展示给用户。

## 主要功能模块

### 1. 导入和依赖

```typescript
import React, { useEffect, useState, useRef } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import SimpleLayout from '@/components/layouts/SimpleLayout'
import { useTranslation } from 'react-i18next'
import ResearchMarkdownRenderer from '@/components/common/ResearchMarkdownRenderer'
import Split from 'react-split'
import useWebSocketWithReconnection, { SocketMessage } from '@/lib/hooks/socket'
import authRequest from '@/lib/authRequest'
import { isJsonStr } from '@/utils/isJson'
import { Namespace } from '@/i18n'
import { useDownloadMarkdown } from '@/utils/useDownloadMarkdown'
import PaddlePay from '@/components/pricing'
import { useMediaQuery } from 'react-responsive'
```

**关键依赖说明：**

- `useWebSocketWithReconnection`: 自定义 WebSocket Hook，负责实时通信
- `authRequest`: 认证请求工具
- `Split`: 用于创建可调整大小的分割面板
- `useTranslation`: 国际化支持

### 2. 类型定义

#### 大纲项类型

```typescript
interface OutlineItem {
  id: string
  title: string
  description: string
  chapterIndex: string
  level: string
  children: OutlineItem[]
}
```

#### 研究消息类型

```typescript
interface ResearchMessage {
  ROLE: string // 消息角色：WORKFLOW, RESEARCHER, REPORTER
  TYPE: string // 消息类型：STATUS, THINKING, REPORTING
  STEP: string // 步骤：START, PLANNING, RESEARCHING, DRAFTING, REFLECTING, REPORTING, END
  CONTENT: {
    ACTION?: string
    CHAPTER?: string
    MESSAGE?: string
  }
}
```

#### 章节内容项类型

```typescript
interface ChapterContentItem {
  type: 'message' | 'markdown'
  message?: ResearchMessage
  content?: string
  isStreaming?: boolean
}
```

### 3. 状态管理

页面使用了大量的 React 状态来管理复杂的研究流程：

```typescript
// 基础数据状态
const [researchData, setResearchData] = useState<any | null>(null)
const [isLoading, setIsLoading] = useState<boolean>(false)
const [isGlobalLoading, setIsGlobalLoading] = useState<boolean>(false)

// 研究流程状态
const [researchStatus, setResearchStatus] = useState<string>('START')
const [outline, setOutline] = useState<OutlineItem[]>([])
const [researchMessages, setResearchMessages] = useState<ResearchMessage[]>([])

// 章节内容状态
const [chapterContents, setChapterContents] = useState<Map<string, ResearchMessage[]>>(new Map())
const [chapterContentItems, setChapterContentItems] = useState<Map<string, ChapterContentItem[]>>(
  new Map(),
)
const [chapterMarkdownActive, setChapterMarkdownActive] = useState<Map<string, boolean>>(new Map())

// 报告内容状态
const [reportContent, setReportContent] = useState<string>('')
const [isReportStreaming, setIsReportStreaming] = useState<boolean>(false)

// UI 状态
const [showToast, setShowToast] = useState<boolean>(false)
const [isFailed, setIsFailed] = useState<boolean>(false)
```

## WebSocket 实现详解

### 1. WebSocket Hook 使用

```typescript
useWebSocketWithReconnection({
  onMessage: handleSocketMessage,
})
```

页面通过自定义的 `useWebSocketWithReconnection` Hook 建立 WebSocket 连接。这个 Hook 提供了：

- 自动重连机制
- 心跳检测
- 连接状态管理
- 消息处理回调

### 2. WebSocket 消息处理核心逻辑

```typescript
const handleSocketMessage = (message: SocketMessage) => {
  try {
    setLD(true)

    // 处理确认消息（大纲数据）
    if (message.type === 'confirm') {
      const outline = message.data.message['报告大纲']
      setOutline(outline)
      outlineRef.current = outline
      return
    }

    // 验证消息有效性
    if (
      !researchDataRef.current ||
      !researchDataRef.current?.task_id ||
      outlineRef.current.length === 0 ||
      !isJsonStr(message.data) ||
      message.task_id !== researchDataRef.current?.task_id
    ) {
      return
    }

    // 标记流式请求已开始
    streamRequestSentRef.current = true
    console.log(
      `${new Date()} receive task result socket message, task_id: ${researchDataRef.current.task_id}`,
    )

    setActiveTask(t('researchResults.processingWait'))
    setIsGlobalLoading(true) // 开始流式请求时激活全局loading
    setResearchStatus('START') // 初始化研究状态

    // 解析 JSON 消息
    const messageData = JSON.parse(message.data)

    // 处理状态消息
    if (messageData.ROLE === 'WORKFLOW' && messageData.TYPE === 'STATUS' && messageData.STEP) {
      const newStep = messageData.STEP

      if (newStep === 'END') {
        // 研究结束
        setIsGlobalLoading(false)
        setStreamingComplete(true)
        setShowToast(true)
        setTimeout(() => {
          setShowToast(false)
        }, 3000)
      } else if (newStep === 'FAILED') {
        // 研究失败
        setIsFailed(true)
        setIsGlobalLoading(false)
        setStreamingComplete(true)
      } else {
        // 其他状态保持loading
        setIsGlobalLoading(true)
      }

      // 更新当前研究状态
      setResearchStatus(newStep)
    }

    // 添加消息到状态数组
    setResearchMessages((prev) => [...prev, messageData])
  } catch (error) {
    console.error(t('researchResults.errorOccurred'), error)
    setIsGlobalLoading(false)
    setActiveTask(t('researchResults.errorOccurred'))
  }
}
```

### 3. WebSocket 消息类型和处理流程

#### 消息类型分类：

1. **确认消息 (confirm)**

   - 包含研究大纲数据
   - 在研究开始前发送
   - 用于初始化页面结构

2. **工作流状态消息 (WORKFLOW + STATUS)**

   - 表示整个研究流程的状态变化
   - 状态包括：START → PLANNING → RESEARCHING → DRAFTING → REFLECTING → REPORTING → END
   - 用于更新全局进度指示器

3. **研究者消息 (RESEARCHER)**

   - **THINKING 类型**: 研究者的思考过程
   - **REPORTING 类型**: 章节内容的流式输出

4. **报告者消息 (REPORTER)**
   - **REPORTING 类型**: 最终报告的流式输出

### 4. WebSocket Hook 内部实现

#### 连接建立流程：

```typescript
// 1. 获取或创建访问令牌
const getOrCreateAccessToken = async () => {
  let token = getCookie('smartToken') || localStorage.getItem('accessToken')

  if (!token) {
    // 匿名登录获取token
    let anonymousId = localStorage.getItem('anonymousId')
    if (!anonymousId) {
      anonymousId = uuidv4()
      localStorage.setItem('anonymousId', anonymousId)
    }

    const res = await axios.post(`${process.env.NEXT_PUBLIC_AUTH_API_URL}/auth/loginAnonymous`, {
      anonymousId,
    })
    token = res.data.accessToken
    localStorage.setItem('accessToken', token)
  }

  return token
}

// 2. 建立WebSocket连接
const startWebSocket = async () => {
  const accessToken = await getOrCreateAccessToken()
  const socketUrl = process.env.NEXT_PUBLIC_SOCKET

  if (accessToken && socketUrl) {
    const url = `${socketUrl}?Authorization=${encodeURIComponent('Bearer ' + accessToken)}`
    socketRef.current = new WebSocket(url)

    socketRef.current.onopen = () => {
      setIsConnected(true)
      // 启动心跳检测
      heartbeatIntervalRef.current = setInterval(() => {
        if (socketRef.current?.readyState === WebSocket.OPEN) {
          socketRef.current.send(JSON.stringify({ type: 'ping' }))
        }
      }, 30000)
    }
  }
}
```

#### 心跳检测机制：

```typescript
// 每30秒发送一次ping
heartbeatIntervalRef.current = setInterval(() => {
  if (socketRef.current?.readyState === WebSocket.OPEN) {
    socketRef.current.send(JSON.stringify({ type: 'ping' }))
  }
}, 30000)

// 处理pong响应
const handleMessage = (event: MessageEvent) => {
  const message = JSON.parse(event.data)

  if (message.type === 'pong') {
    lastPongTimestamp.current = Date.now()
  }

  // 检测pong间隔，如果超过35秒则触发重连
  if (message.type === 'pong') {
    countPongRef.current.push(Date.now())
    if (countPongRef.current.length >= 2) {
      const last = countPongRef.current[countPongRef.current.length - 1]
      const secondLast = countPongRef.current[countPongRef.current.length - 2]
      const interval = last - secondLast
      if (interval > 35000) {
        onRetryRefresh?.()
      }
    }
  }
}
```

#### 自动重连机制：

```typescript
const attemptReconnect = () => {
  const timeSinceLastPong = Date.now() - lastPongTimestamp.current

  if (reconnectAttempts.current < 4 && timeSinceLastPong >= 10000) {
    reconnectAttempts.current += 1
    console.log(`retry connect ${reconnectAttempts.current}`)
    startWebSocket()
  } else if (reconnectAttempts.current > 3) {
    onRetryRefresh?.()
    console.log('Max reconnection attempts reached.')
  }
}
```

## 数据流处理

### 1. 初始化流程

```typescript
useEffect(() => {
  if (!router.isReady) return
  if (requestSentRef.current) return // 防止重复请求

  const fetchTaskData = async () => {
    requestSentRef.current = true
    const id = router.query.id as string

    // 从后端获取任务数据
    const res = await authRequest.get(`${API_BASE_URL}/deep-research/task/${id}`)
    const taskData = res.data.data.task

    // 设置研究数据
    setResearchData({
      task_id: taskData.task_id,
      question: taskData.question,
      requirement: taskData.requirement || '',
    })

    // 设置大纲
    if (taskData.outline && Array.isArray(taskData.outline)) {
      setOutline(taskData.outline)
      outlineRef.current = taskData.outline
    }

    // 恢复历史消息
    if (taskData.record) {
      for (const msg of taskData.record) {
        setResearchMessages((prev) => [...prev, msg])
      }
    }
  }

  fetchTaskData()
}, [router.isReady, router.query])
```

### 2. 消息处理和状态更新

```typescript
// 当研究消息更新时，触发多个处理函数
useEffect(() => {
  updateChapterContents() // 更新章节内容映射
  updateChapterMarkdownActive() // 更新章节活跃状态
  updateReportContent() // 更新研究报告内容
  updateResearchStatus() // 更新研究状态
}, [researchMessages])
```

### 3. 章节内容处理

```typescript
const updateChapterContents = () => {
  const newChapterContents = new Map<string, ResearchMessage[]>()
  const newChapterContentItems = new Map<string, ChapterContentItem[]>()

  // 按章节分组所有消息
  researchMessages.forEach((message) => {
    if (message.CONTENT?.CHAPTER) {
      const chapter = message.CONTENT.CHAPTER
      const messages = newChapterContents.get(chapter) || []
      messages.push(message)
      newChapterContents.set(chapter, messages)
    }
  })

  // 处理每个章节的消息
  newChapterContents.forEach((messages, chapter) => {
    const items: ChapterContentItem[] = []

    // 处理普通消息
    const regularMessages = messages.filter(
      (msg) =>
        !(
          msg.ROLE === 'RESEARCHER' &&
          msg.TYPE === 'REPORTING' &&
          ['START', 'RUNNING'].includes(msg.STEP)
        ),
    )

    regularMessages.forEach((msg) => {
      items.push({
        type: 'message',
        message: msg,
      })
    })

    // 处理REPORTING类型消息，合并成Markdown内容
    const reportingMessages = messages.filter(
      (msg) =>
        msg.ROLE === 'RESEARCHER' &&
        msg.TYPE === 'REPORTING' &&
        ['START', 'RUNNING'].includes(msg.STEP),
    )

    let markdownContent = ''
    let isStreaming = false

    reportingMessages.forEach((msg) => {
      if (msg.CONTENT?.MESSAGE) {
        markdownContent = msg.CONTENT.MESSAGE
      }
      if (msg.STEP !== 'END') {
        isStreaming = true
      }
    })

    if (markdownContent) {
      items.push({
        type: 'markdown',
        content: markdownContent,
        isStreaming: isStreaming,
      })
    }

    newChapterContentItems.set(chapter, items)
  })

  setChapterContents(newChapterContents)
  setChapterContentItems(newChapterContentItems)
}
```

## UI 渲染逻辑

### 1. 研究状态指示器

```typescript
const renderResearchStatusIndicator = () => {
  const statusText = t(`researchResults.status.${researchStatus}`)

  let statusColor = 'bg-indigo-100 text-indigo-800'
  let isAnimated = true

  // 根据不同状态设置颜色和动画
  switch(researchStatus) {
    case 'END':
      statusColor = 'bg-green-100 text-green-800'
      isAnimated = false
      break
    case 'PLANNING':
      statusColor = 'bg-purple-100 text-purple-800'
      break
    case 'RESEARCHING':
      statusColor = 'bg-yellow-100 text-yellow-800'
      break
    // ... 其他状态
  }

  return (
    <div className={`inline-flex items-center rounded-full px-3 py-1 text-sm font-medium ${statusColor} ${isAnimated ? 'animate-pulse' : ''}`}>
      {isAnimated && (
        <div className='relative mr-2'>
          <div className='h-2 w-2 rounded-full bg-current opacity-75'></div>
          <div className='absolute left-0 top-0 h-2 w-2 animate-ping rounded-full bg-current opacity-75'></div>
        </div>
      )}
      {statusText}
    </div>
  )
}
```

### 2. 大纲渲染

```typescript
const renderOutlineItems = (items: OutlineItem[], indent = 0) => {
  return items.map((item) => {
    // 获取章节状态
    const chapterStatus = researchMessages.find(
      (msg) => msg.ROLE === 'RESEARCHER' &&
               msg.TYPE === 'REPORTING' &&
               msg.STEP === 'END' &&
               msg.CONTENT?.CHAPTER === item.chapterIndex
    ) ? 'END' : researchStatus

    // 根据状态设置颜色
    let statusColor = 'bg-primary'
    if (chapterStatus === 'END') {
      statusColor = 'text-primary-600'
    } else if (chapterStatus === 'RESEARCHING') {
      statusColor = 'bg-yellow-600'
    }
    // ... 其他状态颜色

    return (
      <div key={item.id} className='mb-3'>
        <div className='flex flex-col' style={{ marginLeft: `${indent * 16}px` }}>
          <button
            onClick={() => scrollToChapter(item.chapterIndex)}
            className='group flex items-center text-left text-sm text-indigo-700'>
            <span>{item.chapterIndex} {item.title}</span>
            {/* 状态指示器 */}
            {chapterStatus === 'END' ? (
              <svg className={`h-5 w-5 ${statusColor}`}>
                {/* 完成图标 */}
              </svg>
            ) : (
              <div className='relative flex items-center'>
                <div className={`h-3 w-3 rounded-full ${statusColor}`}></div>
                <div className={`h-3 w-3 rounded-full ${statusColor} absolute animate-ping`}></div>
              </div>
            )}
          </button>
        </div>
        {/* 递归渲染子项 */}
        {item.children && item.children.length > 0 && (
          <div className='mt-2'>{renderOutlineItems(item.children, indent + 1)}</div>
        )}
      </div>
    )
  })
}
```

## 关键特性

### 1. 实时性

- 通过 WebSocket 实现真正的实时数据传输
- 支持流式内容更新，用户可以看到内容逐步生成

### 2. 可靠性

- 自动重连机制确保连接稳定
- 心跳检测监控连接状态
- 防重复请求机制

### 3. 用户体验

- 分割面板布局，可调整查看区域
- 实时状态指示器显示研究进度
- 章节导航支持快速跳转
- 移动端适配

### 4. 错误处理

- 连接失败自动重试
- 消息解析错误处理
- 用户友好的错误提示

## 总结

这个页面是一个典型的实时数据展示应用，核心是通过 WebSocket 实现与后端的实时通信。其设计亮点包括：

1. **复杂状态管理**: 使用多个 useState 和 useRef 管理复杂的研究流程状态
2. **实时通信**: 自定义 WebSocket Hook 提供稳定的实时连接
3. **数据处理**: 智能的消息分类和章节内容组织
4. **用户体验**: 丰富的视觉反馈和交互设计
5. **错误恢复**: 完善的错误处理和重连机制

整个实现展现了现代 React 应用在处理复杂实时数据流时的最佳实践。
