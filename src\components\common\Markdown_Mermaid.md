# Markdown 组件与 Mermaid 图表支持指南

## 目录

1. [简介](#简介)
2. [Markdown 组件系统](#markdown-组件系统)
   - [组件架构](#组件架构)
   - [核心组件](#核心组件)
   - [工作流程](#工作流程)
3. [Mermaid 图表支持](#mermaid-图表支持)
   - [什么是 Mermaid](#什么是-mermaid)
   - [支持的图表类型](#支持的图表类型)
   - [渲染机制](#渲染机制)
4. [快速开始](#快速开始)
   - [安装依赖](#安装依赖)
   - [基本用法](#基本用法)
   - [高级配置](#高级配置)
5. [核心逻辑详解](#核心逻辑详解)
   - [Markdown 渲染流程](#markdown-渲染流程)
   - [Mermaid 图表处理](#mermaid-图表处理)
   - [错误处理机制](#错误处理机制)
6. [集成到其他项目](#集成到其他项目)
   - [步骤指南](#步骤指南)
   - [常见问题](#常见问题)
   - [最佳实践](#最佳实践)
7. [示例与参考](#示例与参考)
   - [基础示例](#基础示例)
   - [高级示例](#高级示例)
   - [参考资料](#参考资料)

## 简介

Markdown 是一种轻量级标记语言，用于创建格式化文本文档。我们的 Markdown 组件系统不仅支持标准 Markdown 语法，还集成了 Mermaid 图表支持，使您能够在 Markdown 文档中嵌入各种类型的图表，如流程图、时序图、甘特图等。

本文档将帮助您从零开始理解我们的 Markdown 组件系统和 Mermaid 图表支持，包括它们的工作原理、核心逻辑以及如何将它们集成到您的项目中。

## Markdown 组件系统

### 组件架构

我们的 Markdown 渲染系统采用了模块化设计，主要由以下几个核心组件组成：

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│ MarkdownRenderer│────▶│ MarkdownComponents│────▶│ MermaidPrerender│
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                       │
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  SeoMarkdownRenderer│     │  ResearchMarkdownRenderer│     │  MermaidTest  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

这种架构设计遵循了关注点分离原则，每个组件负责特定的功能，使系统更加灵活和可维护。

### 核心组件

1. **MarkdownRenderer**：基础渲染组件，负责将 Markdown 文本转换为 HTML
   - 支持标准 Markdown 语法
   - 集成 rehype 和 remark 插件
   - 支持流式渲染（用于实时内容）

2. **MarkdownComponents**：提供统一的 Markdown 标签样式定义和自定义组件
   - 统一所有 Markdown 标签的样式
   - 提供自定义组件（如引用编号、注册引导等）
   - 识别并处理 Mermaid 代码块

3. **MermaidPrerender**：专门处理 Mermaid 图表的渲染
   - 初始化 Mermaid 库
   - 预处理图表代码
   - 处理渲染错误
   - 提供错误回退机制

4. **特定用途渲染器**：
   - **SeoMarkdownRenderer**：针对 SEO 页面优化，添加引用编号、注册引导等
   - **ResearchMarkdownRenderer**：支持流式渲染、基于角色的样式

### 工作流程

1. **内容输入**：用户提供 Markdown 文本内容
2. **组件创建**：使用 `createMarkdownComponents` 创建自定义组件
3. **渲染处理**：MarkdownRenderer 将 Markdown 转换为 HTML
4. **代码块处理**：MarkdownComponents 识别 Mermaid 代码块
5. **图表渲染**：MermaidPrerender 处理 Mermaid 图表渲染
6. **结果输出**：最终渲染的 HTML 内容

## Mermaid 图表支持

### 什么是 Mermaid

Mermaid 是一个基于 JavaScript 的图表和图表工具，它使用 Markdown 风格的语法来创建图表。它允许您使用文本和代码生成图表，而不是使用传统的图表工具。

### 支持的图表类型

我们的系统支持以下 Mermaid 图表类型：

* 流程图 (Flowchart)
* 时序图 (Sequence Diagram)
* 类图 (Class Diagram)
* 状态图 (State Diagram)
* 甘特图 (Gantt Chart)
* 饼图 (Pie Chart)
* 关系图 (Entity Relationship Diagram)
* 用户旅程图 (User Journey)

### 渲染机制

Mermaid 图表的渲染过程包括以下步骤：

1. **初始化**：确保 Mermaid 库只初始化一次
2. **预处理**：检测图表类型，清理和规范化图表代码
3. **渲染**：调用 Mermaid 的 render 方法生成 SVG
4. **错误处理**：处理渲染错误，提供错误回退机制
5. **清理**：移除错误提示，清理 DOM 中的临时元素

## 快速开始

### 安装依赖

首先，安装必要的依赖：

```bash
npm install mermaid react-markdown remark-gfm rehype-raw
```

### 基本用法

最简单的使用方式是使用特定用途的渲染器：

```tsx
// SEO页面
import SeoMarkdownRenderer from '@/components/seo/SeoMarkdownRenderer'

<SeoMarkdownRenderer 
  content={markdownContent} 
  citations={citations} 
  guideConfig={['1', '3']} 
/>

// 研究结果页面
import ResearchMarkdownRenderer from '@/components/common/ResearchMarkdownRenderer'

<ResearchMarkdownRenderer 
  content={markdownContent} 
  role="researcher" 
  onCopy={handleCopy} 
/>
```

### 高级配置

如果需要更多定制，可以直接使用基础组件和统一的标签定义：

```tsx
import MarkdownRenderer from '@/components/common/MarkdownRenderer'
import createMarkdownComponents from '@/components/common/MarkdownComponents'

// 创建自定义组件
const customComponents = createMarkdownComponents({
  citations: myCitations,
  trackingPage: 'MY_CUSTOM_PAGE',
  guideRegisterFn: (num) => {
    // 自定义引导注册逻辑
    return showGuideForSection(num) ? <MyCustomGuide /> : null
  },
  disableMermaid: false // 是否禁用 Mermaid 图表渲染
})

// 使用
<MarkdownRenderer
  content={markdownContent}
  isStreaming={isStreamMode}
  className="my-custom-class"
  components={customComponents}
/>
```

## 核心逻辑详解

### Markdown 渲染流程

1. **内容解析**：MarkdownRenderer 接收 Markdown 文本内容
2. **插件处理**：通过 rehype 和 remark 插件处理内容
3. **组件映射**：将 Markdown 元素映射到自定义组件
4. **HTML 生成**：生成最终的 HTML 内容

### Mermaid 图表处理

1. **代码块识别**：MarkdownComponents 识别 Mermaid 代码块
2. **图表类型检测**：MermaidPrerender 检测图表类型
3. **代码预处理**：根据图表类型进行特定处理
4. **SVG 生成**：调用 Mermaid 的 render 方法生成 SVG
5. **错误处理**：处理渲染错误，提供错误回退机制

### 错误处理机制

我们的系统提供了完善的错误处理机制：

1. **初始化错误**：处理 Mermaid 库初始化错误
2. **渲染错误**：处理图表渲染错误
3. **回退机制**：当渲染失败时，显示原始代码块
4. **错误日志**：记录错误信息，便于调试

## 集成到其他项目

### 步骤指南

1. **安装依赖**：
   ```bash
   npm install mermaid react-markdown remark-gfm rehype-raw
   ```

2. **导入组件**：
   ```tsx
   import MarkdownRenderer from '@/components/common/MarkdownRenderer'
   import createMarkdownComponents from '@/components/common/MarkdownComponents'
   import MermaidPrerender from '@/components/common/MermaidPrerender'
   ```

3. **创建 Markdown 组件**：
   ```tsx
   const components = createMarkdownComponents({
     disableMermaid: false, // 是否禁用 Mermaid 图表渲染
   })
   ```

4. **使用 MarkdownRenderer**：
   ```tsx
   <MarkdownRenderer
     content={markdownContent}
     className='prose max-w-none'
     rehypePlugins={[rehypeRaw]}
     remarkPlugins={[remarkGfm]}
     components={components}
   />
   ```

### 常见问题

1. **图表不显示**：
   - 检查 Mermaid 语法是否正确
   - 确保 `disableMermaid` 设置为 `false`
   - 查看控制台是否有错误信息

2. **样式问题**：
   - 检查 CSS 样式是否正确加载
   - 确保没有样式冲突

3. **性能问题**：
   - 对于复杂的图表，考虑使用懒加载
   - 减少同时渲染的图表数量

### 最佳实践

1. **组件复用**：
   - 尽量使用已有的特定用途渲染器
   - 需要自定义时，使用 `createMarkdownComponents` 创建组件

2. **样式维护**：
   - 对样式的修改应该集中在 `MarkdownComponents.tsx` 文件中
   - 使用 CSS 模块或 styled-components 避免样式冲突

3. **错误处理**：
   - 实现完善的错误处理机制
   - 提供用户友好的错误提示

4. **性能优化**：
   - 使用 React.memo 包装 MermaidPrerender 组件
   - 对于复杂的图表，考虑使用懒加载

## 示例与参考

### 基础示例

**基本 Markdown 渲染**：

```tsx
import MarkdownRenderer from '@/components/common/MarkdownRenderer'
import createMarkdownComponents from '@/components/common/MarkdownComponents'

const components = createMarkdownComponents()

const MyComponent = () => {
  const markdownContent = `
# 标题
这是一个段落。

- 列表项 1
- 列表项 2

\`\`\`mermaid
pie
    title 行业收入占比
    "金融" : 52
    "零售" : 24
    "制造" : 15
    "其他" : 9
\`\`\`
  `

  return (
    <MarkdownRenderer
      content={markdownContent}
      className='prose max-w-none'
      components={components}
    />
  )
}
```

### 高级示例

**流式渲染与自定义组件**：

```tsx
import { useState, useEffect } from 'react'
import MarkdownRenderer from '@/components/common/MarkdownRenderer'
import createMarkdownComponents from '@/components/common/MarkdownComponents'

const MyStreamingComponent = () => {
  const [content, setContent] = useState('')
  const [isComplete, setIsComplete] = useState(false)
  
  const components = createMarkdownComponents({
    trackingPage: 'STREAMING_PAGE',
    guideRegisterFn: (num) => {
      return num === 1 ? <div className="guide">这是一个引导提示</div> : null
    }
  })

  useEffect(() => {
    // 模拟流式内容
    const streamContent = async () => {
      const fullContent = `
# 实时生成的内容

这是一个段落。

\`\`\`mermaid
graph TD
    A[开始] --> B{是否有库存?}
    B -->|是| C[发货]
    B -->|否| D[补货]
    D --> B
    C --> E[结束]
\`\`\`
      `
      
      for (let i = 0; i < fullContent.length; i++) {
        setContent(prev => prev + fullContent[i])
        await new Promise(resolve => setTimeout(resolve, 50))
      }
      
      setIsComplete(true)
    }
    
    streamContent()
  }, [])

  return (
    <MarkdownRenderer
      content={content}
      isStreaming={!isComplete}
      className='prose max-w-none'
      components={components}
    />
  )
}
```

### 参考资料

- [Mermaid 官方文档](https://mermaid-js.github.io/mermaid/)
- [React Markdown 文档](https://github.com/remarkjs/react-markdown)
- [Remark GFM 插件](https://github.com/remarkjs/remark-gfm)
- [Rehype Raw 插件](https://github.com/rehypejs/rehype-raw)

## 总结

我们的 Markdown 组件系统通过模块化设计和关注点分离，实现了对 Mermaid 图表的无缝支持。系统由基础渲染组件、统一样式定义、专用 Mermaid 渲染器和特定用途渲染器组成，各组件协同工作，提供了灵活、可扩展的 Markdown 渲染解决方案。

通过使用 `createMarkdownComponents` 函数，可以轻松创建自定义组件，并通过 `disableMermaid` 参数控制是否启用 Mermaid 图表渲染。对于需要更多定制的场景，可以直接使用 `MermaidPrerender` 组件进行渲染。

希望本文档能帮助您快速理解和使用我们的 Markdown 组件系统和 Mermaid 图表支持。如有任何问题，请随时联系我们。 