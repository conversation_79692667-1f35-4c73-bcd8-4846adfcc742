import React, { useEffect, useRef, useState, CSSProperties } from 'react'
import ReferenceNumber from '../seo/ReferenceNumber'
import mermaid from 'mermaid'
import MermaidPrerender from './MermaidPrerender'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { createPortal } from 'react-dom'

// 为 window 对象扩展类型以支持 mermaidInitialized 属性
declare global {
  interface Window {
    mermaidInitialized?: boolean
  }
}

// 组件属性类型
export type ComponentProps = {
  node: any
  children: any
  [key: string]: any
}

// 引用数据类型
export interface Citations {
  [key: string]: any
}

/**
 * SourceReference组件 - 用于渲染外部来源引用
 */
const SourceReference: React.FC<{
  children: React.ReactNode
  r_url?: string
  r_title?: string
  r_content?: string
  id?: string
  trackingPage?: string
}> = ({ children, r_url, r_title, r_content, id, trackingPage = 'DEFAULT' }) => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const [showTooltip, setShowTooltip] = useState(false)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const anchorRef = useRef<HTMLAnchorElement>(null)
  const [tooltipStyle, setTooltipStyle] = useState<CSSProperties>({
    position: 'fixed',
    top: '0px',
    left: '0px',
    zIndex: 9999,
    visibility: 'hidden', // 初始状态设为隐藏
  })

  // 根据元素位置调整提示框的位置，防止在屏幕边缘被截断
  const updateTooltipPosition = () => {
    if (!anchorRef.current || !tooltipRef.current) return

    const anchorRect = anchorRef.current.getBoundingClientRect()
    const tooltipRect = tooltipRef.current.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const viewportHeight = window.innerHeight

    // 计算水平位置
    let left = anchorRect.left + anchorRect.width / 2 - tooltipRect.width / 2

    // 防止提示框超出左边缘
    if (left < 10) {
      left = 10
    }

    // 防止提示框超出右边缘
    if (left + tooltipRect.width > viewportWidth - 10) {
      left = viewportWidth - tooltipRect.width - 10
    }

    // 默认位置在引用上方
    let top = anchorRect.top - tooltipRect.height - 10
    let arrowClass = 'bottom' // 箭头在底部

    // 如果上方空间不足，则显示在下方
    if (top < 10) {
      top = anchorRect.bottom + 10
      arrowClass = 'top' // 箭头在顶部
    }

    // 设置新位置
    setTooltipStyle({
      position: 'fixed',
      top: `${top}px`,
      left: `${left}px`,
      zIndex: 9999,
      visibility: 'visible',
    })

    // 更新箭头位置
    const arrow = tooltipRef.current.querySelector('.arrow') as HTMLElement
    if (arrow) {
      if (arrowClass === 'bottom') {
        arrow.style.top = '100%'
        arrow.style.bottom = 'auto'
        arrow.style.transform = 'translateX(-50%) translateY(-50%) rotate(45deg)'
      } else {
        arrow.style.top = '0'
        arrow.style.bottom = 'auto'
        arrow.style.transform = 'translateX(-50%) translateY(-50%) rotate(225deg)'
      }
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target as Node) &&
        anchorRef.current &&
        !anchorRef.current.contains(event.target as Node)
      ) {
        setShowTooltip(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  useEffect(() => {
    // 显示提示框时更新位置
    if (showTooltip) {
      // 使用setTimeout确保提示框已经渲染并有宽度
      setTimeout(updateTooltipPosition, 0)

      // 添加窗口大小变化时的处理
      window.addEventListener('resize', updateTooltipPosition)

      // 添加滚动时的处理
      window.addEventListener('scroll', updateTooltipPosition, true)
    }

    return () => {
      window.removeEventListener('resize', updateTooltipPosition)
      window.removeEventListener('scroll', updateTooltipPosition, true)
    }
  }, [showTooltip])

  const handleOpenReference = (e: React.MouseEvent) => {
    e.preventDefault()
    if (r_url) {
      window.open(r_url, '_blank')
    }
  }

  const displayText = children || 'R'

  return (
    <>
      <span className='source-reference-wrapper inline-block'>
        <a
          ref={anchorRef}
          href={r_url || '#'}
          onClick={handleOpenReference}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          className='source-reference-number cursor-pointer rounded-full bg-blue-500 px-[0.4em] py-[0.15em] text-[0.7em] font-medium leading-[1.2em] text-white no-underline hover:bg-blue-600'
          data-id={id}
          data-tracking={`CLICK_SEO_SOURCE_REFERENCE:CLICK`}
          data-tracking-p-page={trackingPage}>
          {displayText}
        </a>
      </span>

      {showTooltip &&
        createPortal(
          <div
            ref={tooltipRef}
            className='source-reference-tooltip w-64 rounded-md border border-gray-200 bg-white p-3 shadow-lg'
            style={tooltipStyle}>
            <div className='arrow absolute left-1/2 h-3 w-3 -translate-x-1/2 translate-y-[-50%] rotate-45 border-b border-r border-gray-200 bg-white'></div>

            {r_title && <h4 className='mb-1 text-sm font-medium text-gray-800'>{r_title}</h4>}

            {r_content && <p className='text-xs text-gray-600'>{r_content}</p>}

            {r_url && (
              <div className='mt-2 text-right'>
                <a
                  href={r_url}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-xs text-blue-500 hover:text-blue-700 hover:underline'>
                  {t('button.viewSource', '查看来源')}
                </a>
              </div>
            )}
          </div>,
          document.body,
        )}
    </>
  )
}

/**
 * Mermaid 组件 - 渲染 mermaid 图表
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const MermaidChart: React.FC<{ chartCode: string }> = ({ chartCode }) => {
  const [svgContent, setSvgContent] = useState<string>('')
  const [error, setError] = useState<string>('')
  const [renderFailed, setRenderFailed] = useState<boolean>(false)
  const chartRef = useRef<HTMLDivElement>(null)
  const [chartId] = useState<string>(`mermaid-${Math.random().toString(36).substring(2, 9)}`)

  useEffect(() => {
    // 动态导入所有 Mermaid 模块以确保所有图表类型都能被加载
    const setupMermaid = async () => {
      try {
        // 确保 mermaid 初始化只执行一次
        if (!window.mermaidInitialized) {
          // 这些额外的配置可以帮助解决一些特定图表类型的问题
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'sans-serif',
            logLevel: 'error',
            gantt: {
              titleTopMargin: 25,
              barHeight: 20,
              barGap: 4,
            },
            er: {
              layoutDirection: 'TB',
              minEntityWidth: 100,
              minEntityHeight: 75,
            },
            // 状态图的配置
            state: {},
          })

          window.mermaidInitialized = true
        }

        // 对每种图表类型的特殊处理
        const chartType = detectChartType(chartCode)
        console.log('检测到图表类型:', chartType)

        // 清理代码
        let cleanCode = chartCode.trim().replace(/\r/g, '')

        // 针对特定图表类型进行调整
        if (chartType === 'gantt') {
          // 确保甘特图有正确的日期格式
          if (!cleanCode.includes('dateFormat')) {
            cleanCode =
              cleanCode.split('\n').slice(0, 2).join('\n') +
              '\ndateFormat YYYY-MM-DD\n' +
              cleanCode.split('\n').slice(2).join('\n')
          }
        }

        // 异步渲染
        try {
          const { svg } = await mermaid.render(chartId, cleanCode)
          setSvgContent(svg)
          setError('')
          setRenderFailed(false)
        } catch (err) {
          console.error('Mermaid 图表渲染失败:', err, '图表代码:', cleanCode)
          setError(`图表渲染失败: ${err instanceof Error ? err.message : '未知错误'}`)
          setRenderFailed(true)
        }
      } catch (err) {
        console.error('Mermaid 初始化或图表渲染失败:', err)
        setError(`初始化失败: ${err instanceof Error ? err.message : '未知错误'}`)
        setRenderFailed(true)
      }
    }

    setupMermaid()
  }, [chartCode, chartId])

  // 检测图表类型的函数
  const detectChartType = (code: string): string => {
    const cleanCode = code.trim().toLowerCase()
    if (cleanCode.startsWith('graph ') || cleanCode.startsWith('flowchart ')) return 'flowchart'
    if (cleanCode.startsWith('sequencediagram')) return 'sequence'
    if (cleanCode.startsWith('pie')) return 'pie'
    if (cleanCode.startsWith('gantt')) return 'gantt'
    if (cleanCode.startsWith('classdiagram')) return 'class'
    if (cleanCode.startsWith('statediagram')) return 'state'
    if (cleanCode.startsWith('erdiagram')) return 'er'
    if (cleanCode.startsWith('journey')) return 'journey'
    return 'unknown'
  }

  // 渲染失败时显示代码块
  if (renderFailed) {
    return (
      <div className='mermaid-fallback'>
        <div className='mb-2 rounded border border-yellow-300 bg-yellow-50 p-4 text-sm text-yellow-700'>
          <p className='font-bold'>Mermaid 图表无法渲染</p>
          <p>{error}</p>
        </div>
        <pre className='overflow-auto rounded bg-gray-100 p-4'>
          <code className='text-sm'>{chartCode}</code>
        </pre>
      </div>
    )
  }

  // 出现错误但未完全失败时显示警告
  if (error && !renderFailed) {
    return (
      <div>
        <div className='mb-2 rounded border border-yellow-300 bg-yellow-50 p-4 text-sm text-yellow-700'>
          <p className='font-bold'>Mermaid 图表警告</p>
          <p>{error}</p>
        </div>
        <div
          ref={chartRef}
          className='mermaid-chart my-4 max-w-full overflow-auto'
          dangerouslySetInnerHTML={{ __html: svgContent }}
        />
      </div>
    )
  }

  // 正常渲染
  return (
    <div
      ref={chartRef}
      className='mermaid-chart my-4 max-w-full overflow-auto'
      dangerouslySetInnerHTML={{ __html: svgContent }}
    />
  )
}

/**
 * 创建通用的Markdown组件
 * @param options 配置选项
 * @returns 返回Markdown组件对象
 */
export function createMarkdownComponents(
  options: {
    citations?: Citations
    guideRegisterFn?: (num: string) => React.ReactNode
    trackingPage?: string
    disableMermaid?: boolean
  } = {},
) {
  const {
    citations = {},
    guideRegisterFn,
    trackingPage = 'DEFAULT',
    disableMermaid = false,
  } = options

  return {
    // 标题组件
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    h1: ({ node, children, ...props }: ComponentProps) => (
      <h1
        style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          color: '#1f2937',
          marginBottom: '1rem',
        }}
        {...props}>
        {children}
      </h1>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    h2: ({ node, children, ...props }: ComponentProps) => {
      // 如果提供了引导注册函数，且内容是以数字开头，则尝试插入引导组件
      let guideComponent = null
      if (guideRegisterFn && children) {
        const num = String(children).split('.')[0]
        guideComponent = guideRegisterFn(num)
      }

      return (
        <div>
          {guideComponent}
          <h2
            style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: '#1f2937',
              margin: '1.5rem 0 1rem',
            }}
            {...props}>
            {children}
          </h2>
        </div>
      )
    },

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    h3: ({ node, children, ...props }: ComponentProps) => (
      <h3
        style={{
          fontSize: '1.25rem',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '1rem 0 0.75rem',
        }}
        {...props}>
        {children}
      </h3>
    ),

    // 段落和列表组件
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    p: ({ node, children, ...props }: ComponentProps) => (
      <p
        style={{
          marginBottom: '1rem',
          lineHeight: '1.625',
          color: '#4b5563',
        }}
        {...props}>
        {children}
      </p>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ul: ({ node, children, ...props }: ComponentProps) => (
      <ul
        style={{
          listStyleType: 'disc',
          paddingLeft: '1.5rem',
          marginBottom: '1rem',
        }}
        {...props}>
        {children}
      </ul>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ol: ({ node, children, ...props }: ComponentProps) => (
      <ol
        style={{
          listStyleType: 'decimal',
          paddingLeft: '1.5rem',
          marginBottom: '1rem',
        }}
        {...props}>
        {children}
      </ol>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    li: ({ node, children, ...props }: ComponentProps) => (
      <li
        style={{
          marginBottom: '0.25rem',
          color: '#4b5563',
        }}
        {...props}>
        {children}
      </li>
    ),

    // 强调和引用组件
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    strong: ({ node, children, ...props }: ComponentProps) => (
      <strong
        style={{
          fontWeight: '600',
          color: '#1f2937',
        }}
        {...props}>
        {children}
      </strong>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    blockquote: ({ node, children, ...props }: ComponentProps) => (
      <blockquote
        style={{
          borderLeftWidth: '4px',
          borderLeftColor: '#818cf8',
          paddingLeft: '1rem',
          margin: '1rem 0',
          fontStyle: 'italic',
          color: '#4b5563',
        }}
        {...props}>
        {children}
      </blockquote>
    ),

    // 代码组件
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    code: ({
      inline,
      className,
      children,
      ...props
    }: {
      node?: any
      inline?: boolean
      className?: string
      children?: React.ReactNode
    } & React.HTMLAttributes<HTMLElement>) => {
      // 处理内联代码
      if (inline) {
        return (
          <code
            style={{
              padding: '0.2rem 0.4rem',
              backgroundColor: '#f3f4f6',
              borderRadius: '0.25rem',
              fontSize: '0.875rem',
              color: '#4f46e5',
            }}
            {...props}>
            {children}
          </code>
        )
      }

      // 检查是否为 mermaid 图表
      const match = /language-(\w+)/.exec(className || '')
      const language = match ? match[1] : ''
      const code = String(children).replace(/\n$/, '')

      // 对 mermaid 代码块特殊处理
      if (language === 'mermaid') {
        // 使用新的 MermaidPrerender 组件进行渲染
        return (
          <MermaidPrerender
            id={Math.random().toString(36).substring(2, 11)}
            chart={code}
            onError={(error) => {
              console.error('Mermaid 图表渲染错误:', error)
            }}
          />
        )
      }

      // 普通代码块渲染
      return (
        <pre
          style={{
            backgroundColor: '#f9fafb',
            padding: '1rem',
            borderRadius: '0.5rem',
            overflow: 'auto',
            marginBottom: '1rem',
          }}>
          <code className={className} style={{ color: '#1f2937', fontSize: '0.875rem' }} {...props}>
            {children}
          </code>
        </pre>
      )
    },

    // 表格组件
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    table: ({ node, children, ...props }: ComponentProps) => (
      <div style={{ overflowX: 'auto', maxWidth: '100%' }}>
        <table
          style={{
            borderCollapse: 'collapse',
            width: '100%',
            marginBottom: '1rem',
            border: '1px solid #e5e7eb',
          }}
          {...props}>
          {children}
        </table>
      </div>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    thead: ({ node, children, ...props }: ComponentProps) => (
      <thead style={{ backgroundColor: '#f3f4f6' }} {...props}>
        {children}
      </thead>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    tr: ({ node, children, ...props }: ComponentProps) => (
      <tr
        style={{
          borderBottom: '1px solid #e5e7eb',
        }}
        className='odd:bg-white even:bg-gray-50'
        {...props}>
        {children}
      </tr>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    th: ({ node, children, ...props }: ComponentProps) => (
      <th
        style={{
          padding: '0.75rem',
          borderBottom: '2px solid #d1d5db',
          borderRight: '1px solid #e5e7eb',
          textAlign: 'left',
          fontWeight: '600',
          backgroundColor: '#f9fafb',
        }}
        {...props}>
        {children}
      </th>
    ),

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    td: ({ node, children, ...props }: ComponentProps) => (
      <td
        style={{
          padding: '0.75rem',
          borderBottom: '1px solid #e5e7eb',
          borderRight: '1px solid #e5e7eb',
        }}
        {...props}>
        {children}
      </td>
    ),

    // 引用编号组件 - 只在提供了引用数据时启用
    ref: citations
      ? ({ node, children, ...props }: ComponentProps) => {
          const number = Array.isArray(children) ? children.join('') : String(children)
          if (number === 'undefined') {
            return null
          }
          return (
            <ReferenceNumber
              asm-tracking={`CLICK_SEO_REFERENCE:CLICK`}
              asm-tracking-p-page={trackingPage}
              id={node.attributes?.id || ''}
              number={number}
              citations={citations}
              {...props}
            />
          )
        }
      : undefined,

    // 外部来源引用组件
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    sref: ({ node, children, ...props }: ComponentProps) => {
      const { r_url, r_title, r_content } = node.properties || {}
      return (
        <SourceReference
          r_url={r_url}
          r_title={r_title}
          r_content={r_content}
          id={node.properties?.id || ''}
          trackingPage={trackingPage}>
          {children}
        </SourceReference>
      )
    },

    // 代码块
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    pre: ({ node, children, ...props }: ComponentProps) => {
      const childNode = children && children[0]
      // 如果不是代码块，返回普通的 pre 元素
      if (!childNode || childNode.type !== 'code') {
        return <pre {...props}>{children}</pre>
      }

      const { className = '', children: codeContent = '' } = childNode.props || {}
      const code = String(codeContent).replace(/\n$/, '')
      const langMatch = /language-(\w+)/.exec(className)
      const language = langMatch ? langMatch[1] : ''

      // 检查是否为 mermaid 图表
      if (language === 'mermaid') {
        if (disableMermaid) {
          // 如果禁用了 mermaid 图表渲染，显示加载提示
          return <div className='mermaid-loading p-4 text-center text-gray-500'>图表加载中...</div>
        }

        // 使用新的 MermaidPrerender 组件进行渲染
        const id = `mermaid-${Math.random().toString(36).substring(2, 11)}`
        return (
          <MermaidPrerender
            id={id}
            chart={code}
            onError={(e) => console.error('Mermaid 预渲染错误:', e.message)}
          />
        )
      }

      // 处理其他语言的代码块
      return (
        <pre
          className={`relative overflow-auto rounded bg-gray-50 p-4 ${
            language ? `language-${language}` : ''
          }`}
          {...props}>
          <code className={className} style={{ color: '#1f2937', fontSize: '0.875rem' }} {...props}>
            {codeContent}
          </code>
        </pre>
      )
    },
  }
}

export default createMarkdownComponents
