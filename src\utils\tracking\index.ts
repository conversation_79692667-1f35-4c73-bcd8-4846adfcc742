import { TrackingEventType } from './types'
import { getCommonProperties } from './common'
import { autoTracking } from './auto-tracking'
import { trackingHighlight } from './tracking-highlight'
import { debugTracking } from './debug'
import mixpanel from 'mixpanel-browser'

// 初始化 Mixpanel
// 注意：在实际使用时，你需要将 token 放在环境变量中
const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN || ''

// 只在客户端初始化 Mixpanel
if (typeof window !== 'undefined') {
  mixpanel.init(MIXPANEL_TOKEN, {
    debug: process.env.NODE_ENV !== 'production',
    track_pageview: false, // 我们会手动追踪页面浏览
    ignore_dnt: true, // 忽略 "Do Not Track"（DNT）设置
  })

  // 将mixpanel实例暴露到window对象上，方便调试和使用
  ;(window as any).mixpanel = mixpanel

  // 开发环境下输出初始化信息
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 Mixpanel初始化完成:', {
      token: MIXPANEL_TOKEN ? '已配置' : '未配置',
      debug: true,
      instance: mixpanel,
    })
  }
}

// 获取用户ID (可以根据你的应用逻辑修改)
// const getUserId = (): string => {
//   // 这里可以从你的应用状态、localStorage、cookie等获取用户ID
//   if (typeof window === 'undefined') return '';
//
//   // 示例：从localStorage获取
//   const userId = localStorage.getItem('userId');
//   if (userId) return userId;
//
//   // 如果没有找到用户ID，可以生成一个匿名ID
//   const anonymousId = localStorage.getItem('anonymousId');
//   if (anonymousId) return `anon_${anonymousId}`;
//
//   // 如果连匿名ID都没有，生成一个新的
//   const newAnonymousId = `anon_${Math.random().toString(36).substring(2, 15)}`;
//   localStorage.setItem('anonymousId', newAnonymousId);
//   return newAnonymousId;
// };

/**
 * 主要的追踪功能接口
 */
export const Tracking = {
  /**
   * 手动追踪页面浏览事件
   */
  trackPageView(pageName: string, properties: Record<string, any> = {}) {
    if (typeof window === 'undefined') return

    try {
      const commonProps = getCommonProperties()

      // 合并通用属性和自定义属性
      const allProperties = {
        ...commonProps,
        page_name: pageName,
        ...properties,
      }

      // 发送到 Mixpanel
      mixpanel.track('Page View', allProperties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] Page View:', pageName, allProperties)
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to track page view:', error)
    }
  },

  /**
   * 手动追踪自定义事件
   */
  trackEvent(
    eventName: string,
    eventType: TrackingEventType,
    properties: Record<string, any> = {},
  ) {
    if (typeof window === 'undefined') return

    try {
      const commonProps = getCommonProperties()

      // 合并通用属性和自定义属性
      const allProperties = {
        ...commonProps,
        event_type: eventType,
        ...properties,
      }

      // 发送到 Mixpanel
      mixpanel.track(eventName, allProperties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Tracking] ${eventType} Event:`, eventName, allProperties)
      }
    } catch (error) {
      console.error(`[Tracking Error] Failed to track ${eventType} event:`, error)
    }
  },

  /**
   * 初始化自动追踪功能
   * 应在应用初始化时调用
   */
  initAutoTracking() {
    if (typeof window === 'undefined') return

    try {
      // 初始化自动追踪
      autoTracking.init()

      // 显式初始化追踪高亮功能
      trackingHighlight.init()

      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] Auto-tracking and highlight feature initialized')
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to initialize tracking:', error)
    }
  },

  /**
   * 识别用户
   */
  identifyUser(userId: string, userProperties: Record<string, any> = {}) {
    if (typeof window === 'undefined') return

    try {
      // 在 Mixpanel 中识别用户
      mixpanel.identify(userId)

      // 设置用户属性
      if (Object.keys(userProperties).length > 0) {
        mixpanel.people.set(userProperties)
      }

      // 存储用户ID
      localStorage.setItem('userId', userId)

      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] User identified:', userId, userProperties)
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to identify user:', error)
    }
  },

  /**
   * 重置用户（退出登录时调用）
   */
  resetUser() {
    if (typeof window === 'undefined') return

    try {
      // 重置 Mixpanel 用户
      mixpanel.reset()

      // 移除存储的用户ID
      localStorage.removeItem('userId')

      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] User reset')
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to reset user:', error)
    }
  },

  /**
   * 初始化追踪高亮功能
   * 可以在任何页面上单独调用此方法来启用高亮功能
   */
  initTrackingHighlight() {
    if (typeof window === 'undefined') return

    try {
      trackingHighlight.init()

      if (process.env.NODE_ENV !== 'production') {
        console.log('[Tracking] Highlight feature initialized')
      }
    } catch (error) {
      console.error('[Tracking Error] Failed to initialize highlight feature:', error)
    }
  },
}

// 导出类型
export { TrackingEventType } from './types'

// 导出追踪高亮功能
export { trackingHighlight }

// 导出调试工具
export { debugTracking }
