// import { useCallback, useContext, useEffect } from 'react'
// import { I18nContext } from 'react-i18next'
// import { SupportedLangs } from '.'
// import { updateSetting } from '@/api/updateSetting'
// import { getCookie, setLangToCookies } from '@/lib/cookie'
// import { useUserStore } from '@/store/userStore'
//
// export const LANGUAGEOPTIONS = [
//   { label: 'English', value: 'en' },
//   { label: '中文', value: 'zh' },
// ]
//
// export const useLanguage = ({ loggedIn = true }: { loggedIn: boolean }) => {
//   const { i18n: instance } = useContext(I18nContext)
//   const user = useUserStore((state) => state.user)
//   const updateUser = useUserStore((state) => state.updateUser)
//
//   const changeLanguage = useCallback(async (val: string) => {
//     if (loggedIn) {
//       const res = await updateSetting({
//         language: val as SupportedLangs,
//       })
//       // update store 的 lang
//       if (user)
//         updateUser({
//           ...user,
//           setting: {
//             language: res.language,
//             isAutoAgent: user?.setting?.isAutoAgent,
//           },
//         })
//       setLangToCookies(val)
//     } else {
//       setLangToCookies(val)
//     }
//     instance.changeLanguage(val)
//   }, [])
//
//   useEffect(() => {
//     const lang = getCookie('lang') ?? SupportedLangs.EN
//     instance.changeLanguage(lang)
//   }, [])
//
//   return {
//     language: instance.language,
//     changeLanguage,
//   }
// }
//
// export default useLanguage
