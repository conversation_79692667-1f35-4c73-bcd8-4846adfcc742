import React, { useState, useEffect } from 'react'

interface TextEditMenuProps {
  onExpand: (text: string) => void
  onPolish: (text: string) => void
  onClose: () => void
}

const TextEditMenu: React.FC<TextEditMenuProps> = ({ onExpand, onPolish, onClose }) => {
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [selectedText, setSelectedText] = useState('')
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleSelection = () => {
      const selection = window.getSelection()
      if (!selection || selection.isCollapsed) {
        setIsVisible(false)
        return
      }

      const text = selection.toString().trim()
      if (text) {
        const range = selection.getRangeAt(0)
        const rect = range.getBoundingClientRect()

        setSelectedText(text)
        setPosition({
          x: rect.left + rect.width / 2,
          y: rect.bottom + window.scrollY + 10,
        })
        setIsVisible(true)
      }
    }

    document.addEventListener('mouseup', handleSelection)
    return () => document.removeEventListener('mouseup', handleSelection)
  }, [])

  if (!isVisible) return null

  return (
    <div
      className='fixed z-50 rounded-lg border border-gray-200 bg-white p-2 shadow-lg'
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translateX(-50%)',
      }}>
      <div className='flex gap-2'>
        <button
          onClick={() => {
            onExpand(selectedText)
            setIsVisible(false)
          }}
          className='rounded-md bg-blue-50 px-3 py-1.5 text-sm text-blue-700 hover:bg-blue-100'>
          扩写
        </button>
        <button
          onClick={() => {
            onPolish(selectedText)
            setIsVisible(false)
          }}
          className='rounded-md bg-purple-50 px-3 py-1.5 text-sm text-purple-700 hover:bg-purple-100'>
          润色
        </button>
        <button
          onClick={() => {
            onClose()
            setIsVisible(false)
          }}
          className='rounded-md bg-gray-50 px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100'>
          取消
        </button>
      </div>
    </div>
  )
}

export default TextEditMenu
