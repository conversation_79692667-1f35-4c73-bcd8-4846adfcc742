import Accordion from '@/components/common/Accordion'
import Segmented from '@/components/common/Segmented'
import BaseLayout from '@/components/layouts/BaseLayout'
import CallToAction from '@/components/seo/CallToAction'
import { Namespace } from '@/i18n'
import { motion } from 'framer-motion'
import { AlertCircleIcon } from 'lucide-react'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import CookieConsent from 'react-cookie-consent'
import { useTranslation } from 'react-i18next'

// Scenario card component props interface
interface ScenarioCardProps {
  title: string
  description: string
  icon: string
  gradient: string
}

// Scenario card component
const ScenarioCard: React.FC<ScenarioCardProps> = ({ title, description, icon, gradient }) => {
  // 从gradient字符串中提取主色调
  const colorMap: Record<string, string> = {
    'from-blue-600': 'border-blue-100 bg-blue-50 text-blue-600',
    'from-green-600': 'border-green-100 bg-green-50 text-green-600',
    'from-red-600': 'border-red-100 bg-red-50 text-red-600',
    'from-purple-600': 'border-purple-100 bg-purple-50 text-purple-600',
  }

  // 根据gradient值查找对应的配色
  const colorClass = Object.keys(colorMap).find((key) => gradient.includes(key))
  const themeColors = colorClass ? colorMap[colorClass] : 'border-gray-100 bg-gray-50 text-gray-600'

  return (
    <div className='relative overflow-hidden rounded-xl border border-gray-100 bg-white p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-md'>
      <div
        className={`absolute left-0 top-0 h-1 w-full ${gradient.replace('bg-gradient-to-br', 'bg-gradient-to-r')} opacity-70`}></div>
      <div className='relative z-10 pt-4'>
        <div className={`mb-5 flex h-14 w-14 items-center justify-center rounded-full bg-blue-50`}>
          <Image
            src={icon}
            alt={title}
            width={28}
            height={28}
            className={themeColors.split(' ')[3]}
          />
        </div>
        <h3 className='mb-3 text-xl font-bold text-gray-900'>{title}</h3>
        <p className='leading-relaxed text-gray-600'>{description}</p>
      </div>
    </div>
  )
}

// Homepage component
const HomePage: React.FC = () => {
  const [index, setIndex] = useState(0)
  const { t, i18n } = useTranslation(Namespace.HOME)
  const { t: tGlobal } = useTranslation(Namespace.GLOBAL)
  const [currentOption, setCurrentOption] = useState('search')
  const [currentToolSuite, setCurrentToolSuite] = useState('webpage summarizer')
  const [scenariosVisible, setScenariosVisible] = useState(false)
  const [setTestimonialsVisible] = useState(false)

  const scenariosRef = useRef<HTMLDivElement>(null)
  const testimonialsRef = useRef<HTMLDivElement>(null)

  const descriptionTexts = [
    t('description.text1'),
    t('description.text2'),
    t('description.text3'),
    t('description.text4'),
    t('description.text5'),
  ]

  const workflowArr = useMemo(
    () => [
      {
        id: 'search',
        title: t('workflowSegment.search.title'),
        desc: t('workflowSegment.search.description'),
        imgSrc: '/images/search.png',
        imgAlt: 'Search demo image',
      },
      {
        id: 'execution',
        title: t('workflowSegment.execution.title'),
        desc: t('workflowSegment.execution.description'),
        imgSrc: '/images/execution.png',
        imgAlt: 'Execution demo image',
      },
      {
        id: 'verification',
        title: t('workflowSegment.verification.title'),
        desc: t('workflowSegment.verification.description'),
        imgSrc: '/images/verification.png',
        imgAlt: 'Verification demo image',
      },
      {
        id: 'action',
        title: t('workflowSegment.action.title'),
        desc: t('workflowSegment.action.description'),
        imgSrc: '/images/action.png',
        imgAlt: 'Action demo image',
      },
    ],
    [t, i18n],
  )

  const toolsSuiteArr = [
    {
      id: 'webpage summarizer',
      title: t('toolSuiteList.tool1.title'),
      imageSrc: '/images/webpage_summarizer.png',
      imageAlt: 'AI webpage summarizer demo image',
      content: (
        <div>
          <div>{t('toolSuiteList.tool1.description')}</div>
          <div className='mt-5 rounded-lg bg-primary p-2 md:p-4 xl:hidden'>
            <img
              src='/images/webpage_summarizer.png'
              className='rounded-[4px]'
              alt='AI webpage summarizer demo image'
            />
          </div>
        </div>
      ),
    },
    {
      id: 'image describer',
      title: t('toolSuiteList.tool2.title'),
      imageSrc: '/images/image_describer.png',
      imageAlt: 'AI Image describer demo image',
      content: (
        <div>
          <div>{t('toolSuiteList.tool2.description')}</div>
          <div className='mt-5 rounded-lg bg-primary p-2 md:p-4 xl:hidden'>
            <img
              src='/images/image_describer.png'
              className='rounded-[4px]'
              alt='AI Image describer demo image'
            />
          </div>
        </div>
      ),
    },
    {
      id: 'pdf analysis',
      title: t('toolSuiteList.tool3.title'),
      imageSrc: '/images/pdf_analysis.png',
      imageAlt: 'AI PDF analysis demo image',
      content: (
        <div>
          <div>{t('toolSuiteList.tool3.description')}</div>
          <div className='mt-5 rounded-lg bg-primary p-2 md:p-4 xl:hidden'>
            <img
              src='/images/pdf_analysis.png'
              className='rounded-[4px]'
              alt='AI PDF analysis demo image'
            />
          </div>
        </div>
      ),
    },
    {
      id: 'file analysis',
      title: t('toolSuiteList.tool4.title'),
      imageSrc: '/images/file_analysis.png',
      imageAlt: 'AI File Analysis demo image',
      content: (
        <div>
          <div>{t('toolSuiteList.tool4.description')}</div>
          <div className='mt-5 rounded-lg bg-primary p-2 md:p-4 xl:hidden'>
            <img
              src='/images/file_analysis.png'
              className='rounded-[4px]'
              alt='AI File Analysis demo image'
            />
          </div>
        </div>
      ),
    },
    {
      id: 'report generator',
      title: t('toolSuiteList.tool5.title'),
      imageSrc: '/images/report_generator.png',
      imageAlt: 'AI Report generator demo image',
      content: (
        <div>
          <div>{t('toolSuiteList.tool5.description')}</div>
          <div className='mt-5 rounded-lg bg-primary p-2 md:p-4 xl:hidden'>
            <img
              src='/images/report_generator.png'
              className='rounded-[4px]'
              alt='AI Report generator demo image'
            />
          </div>
        </div>
      ),
    },
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      // 控制 index 变动
      setIndex((prev) => {
        const nextIndex = (prev + 1) % descriptionTexts.length
        return nextIndex
      })
    }, 3000) // 每 3 秒切换一次

    return () => clearInterval(interval)
  }, [])

  // Monitor scroll events for element animations when entering viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.target === scenariosRef.current && entry.isIntersecting) {
            setScenariosVisible(true)
          } else if (entry.target === testimonialsRef.current && entry.isIntersecting) {
            // @ts-ignore
            setTestimonialsVisible(true)
          }
        })
      },
      { threshold: 0.1 },
    )

    if (scenariosRef.current) {
      observer.observe(scenariosRef.current)
    }
    if (testimonialsRef.current) {
      observer.observe(testimonialsRef.current)
    }

    return () => {
      if (scenariosRef.current) {
        observer.unobserve(scenariosRef.current)
      }
      if (testimonialsRef.current) {
        observer.unobserve(testimonialsRef.current)
      }
    }
  }, [])

  const currentWorkflow = useMemo(() => {
    const option = workflowArr.find((item) => item.id === currentOption)!
    return (
      <div className='items-center justify-center px-4 lg:flex xl:mx-auto xl:max-w-[1200px]'>
        <div
          style={{ lineHeight: 1.25 }}
          className='mb-2 mr-5 flex-1 justify-end text-center text-base text-gray-700 md:text-lg lg:text-2xl xl:text-left'>
          <div>{option.desc}</div>
        </div>
        <div className='flex-1'>
          <img className='rounded-lg' src={option.imgSrc} alt={option.imgAlt} />
        </div>
      </div>
    )
  }, [currentOption, workflowArr])

  return (
    <>
      <Head>
        <title>Your Intelligent Business Deep Research Agent</title>
        <meta
          name='description'
          content='Transform your business research with AI Smarties. Our PDCA workflow combines AI efficiency with human expertise for superior analysis. Extract insights from web, PDF reports, and images to create professional business intelligence reports.'
        />
        <meta
          name='keywords'
          content='deep research; ai in research; AI copilot for business; business research assistant, AI research workflow, PDCA business intelligence, PDF data extraction, professional report generator, business image analysis, transparent AI research, business decision making, AI document analysis, research automation for experts'
        />
        <meta
          property='og:title'
          content=' Your Intelligent Business Research Assistant | AI-Powered Research Workflow for Business Experts'
        />
        <meta
          property='og:description'
          content='Transform your business research with AI Smarties. Our PDCA workflow combines AI efficiency with human expertise for superior analysis. Extract insights from web, PDF reports, and images to create professional business intelligence reports.'
        />
        <meta property='og:type' content='website' />
        <meta property='og:image' content='/images/hero-image.png' />
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        {/* <!-- Meta Pixel Code --> */}
        <script>
          {`
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '1264521329015852');
            fbq('track', 'PageView');
          `}
        </script>
        {/* End Meta Pixel Code */}
      </Head>

      {/* Hero Section */}
      <section className='flex h-screen flex-col justify-center bg-[radial-gradient(circle_at_bottom_right,_#5661f6_0%,_white_50%)] py-12 md:mt-0 lg:h-[calc(100vh-82px)]'>
        <div className='flex justify-center px-2'>
          <h1 className='text-center text-[40px] md:w-2/3 md:text-6xl lg:w-[810px] lg:text-8xl'>
            {t('title')}
          </h1>
        </div>
        <div className='mt-7 h-9 overflow-hidden text-center text-base font-light text-gray-500 md:text-3xl'>
          <div
            className='transition-transform duration-500 ease-in-out'
            style={{
              transform: `translateY(-${index * 2.25}rem)`, // 每项高度为 1.5rem，根据实际内容调整
            }}>
            {descriptionTexts.map((text, i) => (
              <div key={i} className='h-9'>
                {text}
              </div>
            ))}
          </div>
        </div>
        <div className='mt-7 flex items-center justify-center'>
          <Link
            href='/deep-research'
            className='cursor-pointer rounded-md border border-primary bg-primary px-4 py-2 text-white hover:bg-primary-dark'>
            {t('freeTry')}
          </Link>
        </div>
        <div className='mt-2 flex items-center justify-center text-center text-sm text-gray-500'>
          <AlertCircleIcon width={16} height={16} />
          <div className='ml-2'>{tGlobal('noCreditCard')}</div>
        </div>
      </section>

      <section className='bg-gray-50 py-16'>
        <h2 className='mb-7 mt-14 text-center text-3xl font-bold text-gray-900 md:text-4xl'>
          {t('subTitle1')}
        </h2>
        <div>
          <Segmented
            options={workflowArr.map((item) => ({ name: item.title, value: item.id }))}
            defaultValue='search'
            onChange={(value) => {
              setCurrentOption(value)
            }}
          />
        </div>
        <div className='mt-7'>{currentWorkflow}</div>
      </section>

      <section className='bg-white py-16' ref={scenariosRef}>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <h2 className='mb-16 text-center text-3xl font-bold text-gray-900 md:text-4xl'>
            {t('subTitle2')}
          </h2>
          <div
            className={`grid grid-cols-1 gap-8 transition-all duration-1000 lg:grid-cols-3 ${scenariosVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <ScenarioCard
              title={t('scenarioList.scenario1.title')}
              description={t('scenarioList.scenario1.description')}
              icon='/images/company-research-icon.svg'
              gradient='bg-gradient-to-br from-blue-600 to-purple-600'
            />
            <ScenarioCard
              title={t('scenarioList.scenario2.title')}
              description={t('scenarioList.scenario2.description')}
              icon='/images/regulatory-research-icon.svg'
              gradient='bg-gradient-to-br from-red-600 to-orange-600'
            />
            <ScenarioCard
              title={t('scenarioList.scenario3.title')}
              description={t('scenarioList.scenario3.description')}
              icon='/images/report-icon.svg'
              gradient='bg-gradient-to-br from-purple-600 to-pink-600'
            />
          </div>
        </div>
      </section>

      <section className='bg-gray-50 py-16'>
        <div className='flex items-center justify-center px-4 xl:mx-auto xl:max-w-[1200px]'>
          <div className='flex-1 md:max-w-[700px] xl:mr-5 xl:min-w-[450px]'>
            <h2 className='mb-5 text-center text-3xl font-bold text-gray-900 md:text-4xl'>
              {t('subTitle3')}
            </h2>
            <Accordion
              items={toolsSuiteArr}
              onChange={(value) => {
                setCurrentToolSuite(value)
              }}
            />
          </div>
          <div className='hidden overflow-hidden rounded-lg bg-primary py-16 pl-4 xl:block'>
            <img
              className='rounded-[4px]'
              src={toolsSuiteArr.find((item) => item.id === currentToolSuite)?.imageSrc}
              alt={toolsSuiteArr.find((item) => item.id === currentToolSuite)?.imageAlt}
            />
          </div>
        </div>
      </section>

      <section className='bg-white py-16'>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <h3 className='mb-10 text-center text-xl font-bold text-gray-900'>{t('surpport')}</h3>
          <div className='mt-7 flex flex-col items-center justify-center md:flex-row'>
            <Image
              src='/images/aws_logo.png'
              alt='AWS support logo image'
              width={200}
              height={100}
            />
            <Image
              className='mt-5 md:-mt-3 md:ml-20'
              src='/images/microsoft_logo.png'
              alt='Microsoft support logo image'
              width={200}
              height={100}
            />
          </div>
        </div>
      </section>

      <section className='relative overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-50 py-16'>
        {/* Background decoration */}
        <div className='bg-primary/5 absolute -left-32 top-20 h-64 w-64 rounded-full blur-3xl'></div>
        <div className='absolute bottom-0 right-0 h-96 w-96 rounded-full bg-blue-500/5 blur-3xl'></div>
        <div className='bg-primary/3 absolute left-1/2 top-1/2 h-[500px] w-[500px] -translate-x-1/2 -translate-y-1/2 rounded-full opacity-30 blur-3xl'></div>

        <div className='relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className='mb-12 text-center'>
            <h2 className='mb-4 text-3xl font-bold text-gray-900 md:text-4xl'>{t('subTitle4')}</h2>
            <p className='mx-auto max-w-3xl text-xl text-gray-600'>{t('introduction')}</p>
          </motion.div>

          <div className='grid gap-6 md:gap-8'>
            {/* Primary testimonial card - AI typewriter effect */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className='group relative overflow-hidden rounded-xl border border-gray-100 bg-white p-6 shadow-md'
              whileHover={{
                y: -5,
                boxShadow: '0 15px 30px -10px rgba(0, 0, 0, 0.1)',
                borderColor: 'rgba(79, 70, 229, 0.2)',
              }}>
              <div className='flex items-start gap-4'>
                <div className='relative shrink-0'>
                  <div className='ring-primary/10 h-14 w-14 overflow-hidden rounded-full shadow-sm ring-2'>
                    <div className='flex h-full w-full items-center justify-center bg-gradient-to-r from-blue-600 to-violet-600 text-xl font-bold text-white'>
                      L
                    </div>
                  </div>
                </div>

                <div className='flex-1'>
                  <div className='mb-1 flex flex-col justify-between md:flex-row md:items-center'>
                    <div>
                      <h3 className='text-lg font-bold text-gray-900'>
                        {t('resultList.result1.userName')}
                      </h3>
                      <p className='text-xs text-gray-500'> {t('resultList.result1.userRole')}</p>
                    </div>
                    <div className='mt-2 hidden items-center md:mt-0 md:flex'>
                      <div className='bg-primary/5 flex rounded-full px-2 py-0.5 text-xs font-medium text-primary'>
                        {t('resultList.result1.timeSaving')}
                      </div>
                    </div>
                  </div>

                  <div className='relative mt-3 font-light'>
                    <div className='bg-primary/10 absolute left-0 top-0 h-full w-1 rounded-full'></div>
                    <TypewriterText
                      text={t('resultList.result1.feedback')}
                      delay={3000}
                      className='pl-3 text-sm italic text-gray-700'
                    />
                  </div>

                  <div className='mt-3 flex flex-wrap gap-2 md:hidden'>
                    <span className='bg-primary/10 inline-block rounded-full px-2 py-0.5 text-xs font-medium text-primary'>
                      85% Time Savings
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Multiple card layout */}
            <div className='grid gap-4 md:grid-cols-3 md:gap-6'>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className='group relative overflow-hidden rounded-xl border border-gray-100 bg-white p-4 shadow-md'
                whileHover={{
                  y: -5,
                  boxShadow: '0 15px 30px -10px rgba(0, 0, 0, 0.1)',
                  borderColor: 'rgba(79, 70, 229, 0.2)',
                }}>
                <div className='flex items-start gap-3'>
                  <div className='shrink-0'>
                    <div className='h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-green-500/10'>
                      <div className='flex h-full w-full items-center justify-center bg-gradient-to-r from-green-500 to-teal-500 text-lg font-bold text-white'>
                        S
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className='text-base font-bold text-gray-900'>
                      {t('resultList.result2.userName')}
                    </h3>
                    <p className='text-xs text-gray-500'>{t('resultList.result2.userRole')}</p>

                    <div className='relative mt-2'>
                      <TypewriterText
                        text={t('resultList.result2.feedback')}
                        delay={4000}
                        className='text-xs leading-relaxed text-gray-700'
                      />
                    </div>

                    <div className='mt-3 flex flex-wrap gap-1'>
                      <span className='inline-block rounded-full bg-green-50 px-2 py-0.5 text-xs font-medium text-green-600'>
                        {t('resultList.result2.usage')}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className='group relative overflow-hidden rounded-xl border border-gray-100 bg-white p-4 shadow-md'
                whileHover={{
                  y: -5,
                  boxShadow: '0 15px 30px -10px rgba(0, 0, 0, 0.1)',
                  borderColor: 'rgba(79, 70, 229, 0.2)',
                }}>
                <div className='flex items-start gap-3'>
                  <div className='shrink-0'>
                    <div className='h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-purple-500/10'>
                      <div className='flex h-full w-full items-center justify-center bg-gradient-to-r from-purple-500 to-indigo-600 text-lg font-bold text-white'>
                        A
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className='text-base font-bold text-gray-900'>
                      {t('resultList.result3.userName')}
                    </h3>
                    <p className='text-xs text-gray-500'>{t('resultList.result3.userRole')}</p>

                    <div className='relative mt-2'>
                      <TypewriterText
                        text={t('resultList.result3.feedback')}
                        delay={5000}
                        className='text-xs leading-relaxed text-gray-700'
                      />
                    </div>

                    <div className='mt-3 flex flex-wrap gap-1'>
                      <span className='inline-block rounded-full bg-purple-50 px-2 py-0.5 text-xs font-medium text-purple-600'>
                        {t('resultList.result3.usage')}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className='group relative overflow-hidden rounded-xl border border-gray-100 bg-white p-4 shadow-md'
                whileHover={{
                  y: -5,
                  boxShadow: '0 15px 30px -10px rgba(0, 0, 0, 0.1)',
                  borderColor: 'rgba(79, 70, 229, 0.2)',
                }}>
                <div className='flex items-start gap-3'>
                  <div className='shrink-0'>
                    <div className='h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-yellow-500/10'>
                      <div className='flex h-full w-full items-center justify-center bg-gradient-to-r from-yellow-500 to-amber-500 text-lg font-bold text-white'>
                        E
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className='text-base font-bold text-gray-900'>
                      {t('resultList.result4.userName')}
                    </h3>
                    <p className='text-xs text-gray-500'>{t('resultList.result4.userRole')}</p>

                    <div className='relative mt-2'>
                      <TypewriterText
                        text={t('resultList.result4.feedback')}
                        delay={6000}
                        className='text-xs leading-relaxed text-gray-700'
                      />
                    </div>

                    <div className='mt-3 flex flex-wrap gap-1'>
                      <span className='inline-block rounded-full bg-yellow-50 px-2 py-0.5 text-xs font-medium text-yellow-600'>
                        {t('resultList.result4.usage')}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className='group relative overflow-hidden rounded-xl border border-gray-100 bg-white p-4 shadow-md'
                whileHover={{
                  y: -5,
                  boxShadow: '0 15px 30px -10px rgba(0, 0, 0, 0.1)',
                  borderColor: 'rgba(79, 70, 229, 0.2)',
                }}>
                <div className='flex items-start gap-3'>
                  <div className='shrink-0'>
                    <div className='h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-blue-500/10'>
                      <div className='flex h-full w-full items-center justify-center bg-gradient-to-r from-blue-500 to-cyan-500 text-lg font-bold text-white'>
                        M
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className='text-base font-bold text-gray-900'>
                      {t('resultList.result5.userName')}
                    </h3>
                    <p className='text-xs text-gray-500'>{t('resultList.result5.userRole')}</p>

                    <div className='relative mt-2'>
                      <TypewriterText
                        text={t('resultList.result5.feedback')}
                        delay={7000}
                        className='text-xs leading-relaxed text-gray-700'
                      />
                    </div>

                    <div className='mt-3 flex flex-wrap gap-1'>
                      <span className='inline-block rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-600'>
                        {t('resultList.result5.usage')}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className='group relative overflow-hidden rounded-xl border border-gray-100 bg-white p-4 shadow-md'
                whileHover={{
                  y: -5,
                  boxShadow: '0 15px 30px -10px rgba(0, 0, 0, 0.1)',
                  borderColor: 'rgba(79, 70, 229, 0.2)',
                }}>
                <div className='flex items-start gap-3'>
                  <div className='shrink-0'>
                    <div className='h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-pink-500/10'>
                      <div className='flex h-full w-full items-center justify-center bg-gradient-to-r from-pink-500 to-rose-500 text-lg font-bold text-white'>
                        J
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className='text-base font-bold text-gray-900'>
                      {t('resultList.result6.userName')}
                    </h3>
                    <p className='text-xs text-gray-500'>{t('resultList.result6.userRole')}</p>

                    <div className='relative mt-2'>
                      <TypewriterText
                        text={t('resultList.result6.feedback')}
                        delay={8000}
                        className='text-xs leading-relaxed text-gray-700'
                      />
                    </div>

                    <div className='mt-3 flex flex-wrap gap-1'>
                      <span className='inline-block rounded-full bg-pink-50 px-2 py-0.5 text-xs font-medium text-pink-600'>
                        {t('resultList.result6.usage')}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className='group relative overflow-hidden rounded-xl border border-gray-100 bg-white p-4 shadow-md'
                whileHover={{
                  y: -5,
                  boxShadow: '0 15px 30px -10px rgba(0, 0, 0, 0.1)',
                  borderColor: 'rgba(79, 70, 229, 0.2)',
                }}>
                <div className='flex items-start gap-3'>
                  <div className='shrink-0'>
                    <div className='h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-orange-500/10'>
                      <div className='flex h-full w-full items-center justify-center bg-gradient-to-r from-orange-500 to-red-500 text-lg font-bold text-white'>
                        Z
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className='text-base font-bold text-gray-900'>
                      {t('resultList.result7.userName')}
                    </h3>
                    <p className='text-xs text-gray-500'>{t('resultList.result7.userRole')}</p>

                    <div className='relative mt-2'>
                      <TypewriterText
                        text={t('resultList.result7.feedback')}
                        delay={9000}
                        className='text-xs leading-relaxed text-gray-700'
                      />
                    </div>

                    <div className='mt-3 flex flex-wrap gap-1'>
                      <span className='inline-block rounded-full bg-orange-50 px-2 py-0.5 text-xs font-medium text-orange-600'>
                        {t('resultList.result7.usage')}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
        {/* Statistics */}
        <div className='mt-16 grid grid-cols-2 gap-6 md:grid-cols-4 md:gap-8'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className='text-center'>
            <h3 className='mb-2 text-4xl font-bold text-primary md:text-5xl'>85%</h3>
            <p className='text-gray-600'>{t('performance.averageTimeSaving')}</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className='text-center'>
            <h3 className='mb-2 text-4xl font-bold text-primary md:text-5xl'>98%</h3>
            <p className='text-gray-600'>{t('performance.userSatisfaction')}</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className='text-center'>
            <h3 className='mb-2 text-4xl font-bold text-primary md:text-5xl'>8K+</h3>
            <p className='text-gray-600'>{t('performance.monthlyActiveUsers')}</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className='text-center'>
            <h3 className='mb-2 text-4xl font-bold text-primary md:text-5xl'>50+</h3>
            <p className='text-gray-600'>{t('performance.industryApplications')}</p>
          </motion.div>
        </div>
      </section>

      <section className='flex justify-center py-12'>
        <CallToAction />
      </section>
      <CookieConsent
        enableDeclineButton
        location='bottom'
        declineButtonText={tGlobal('gdpr.reject')}
        buttonText={tGlobal('gdpr.accept')}
        cookieName='CookieConsent'
        style={{ background: '#ffffff' }}
        containerClasses='py-6 pl-2 !items-center'
        buttonWrapperClasses='flex md:flex-col'
        buttonStyle={{
          order: 1,
          display: 'block',
          color: '#ffffff',
          backgroundColor: '#5661f6',
          fontSize: '16px',
          fontWeight: 'bold',
          borderRadius: 4,
          margin: '0 16px 3px',
        }}
        declineButtonStyle={{
          order: 2,
          display: 'block',
          color: '#ffffff',
          backgroundColor: '#5661f6',
          fontSize: '16px',
          fontWeight: 'bold',
          borderRadius: 4,
          margin: '0px 16px 3px',
        }}
        expires={365}>
        <div className='text-xs text-black-1-70'>
          <div className='font-semibold text-black-1'>{tGlobal('gdpr.title')}</div>
          <div className='text-black-1-7 mt-2'>{tGlobal('gdpr.description')}</div>
        </div>
      </CookieConsent>
      <noscript>
        <img
          className='hidden'
          height='1'
          width='1'
          alt={'facebook pixel no script image'}
          src='https://www.facebook.com/tr?id=1264521329015852&ev=PageView&noscript=1'
        />
      </noscript>
    </>
  )
}

// TypewriterText 组件
interface TypewriterTextProps {
  text: string
  delay?: number
  className?: string
}

const TypewriterText: React.FC<TypewriterTextProps> = ({ text, delay = 0, className = '' }) => {
  const [displayText, setDisplayText] = useState('')
  const [startTyping, setStartTyping] = useState(false)

  useEffect(() => {
    const timeout = setTimeout(() => {
      setStartTyping(true)
    }, delay)

    return () => clearTimeout(timeout)
  }, [delay])

  useEffect(() => {
    if (!startTyping) return

    if (displayText.length < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(text.substring(0, displayText.length + 1))
      }, 30)
      return () => clearTimeout(timeout)
    }
  }, [displayText, text, startTyping])

  return (
    <p className={className}>
      {displayText}
      {displayText.length < text.length && startTyping && (
        <span className='ml-0.5 inline-block h-3 w-1 animate-pulse bg-primary'></span>
      )}
    </p>
  )
}

export default HomePage
