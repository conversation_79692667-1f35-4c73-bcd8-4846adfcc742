/* eslint-disable react/no-unknown-property */
import React, { useEffect, useState } from 'react'
import YouTubeModal from './YouTubeModal'

interface YouTubeVideoMetadata {
  title: string
  thumbnailUrl: string
}

interface YouTubeVideoConfig {
  url: string
  title?: string
  description?: string
}

interface YouTubePreviewProps {
  videos: YouTubeVideoConfig[]
  titleMaxLength?: number
  descriptionMaxLength?: number
  className?: string
}

const extractVideoId = (url: string): string => {
  const match = url.match(
    /(?:youtu\.be\/|youtube\.com(?:\/embed\/|\/v\/|\/watch\?v=|\/.+\?v=))([\w-]{11})/,
  )
  return match?.[1] || ''
}

const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

const YouTubePreview: React.FC<YouTubePreviewProps> = ({
  videos,
  titleMaxLength = 60,
  descriptionMaxLength = 120,
  className = '',
}) => {
  const [videoMetadata, setVideoMetadata] = useState<Record<string, YouTubeVideoMetadata>>({})
  const [activeVideoId, setActiveVideoId] = useState<string | null>(null)

  useEffect(() => {
    const fetchMetadata = async () => {
      const metadata: Record<string, YouTubeVideoMetadata> = {}

      for (const video of videos) {
        const videoId = extractVideoId(video.url)
        if (!videoId) continue

        try {
          const response = await fetch(
            `https://www.youtube.com/oembed?url=${encodeURIComponent(video.url)}&format=json`,
          )
          const data = await response.json()

          metadata[videoId] = {
            title: video.title || data.title || '',
            thumbnailUrl: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
          }
        } catch (error) {
          console.error(`Error fetching metadata for video ${videoId}:`, error)
          metadata[videoId] = {
            title: video.title || '',
            thumbnailUrl: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
          }
        }
      }

      setVideoMetadata(metadata)
    }

    fetchMetadata()
  }, [videos])

  return (
    <div className={`space-y-3 ${className}`}>
      {videos.map((video) => {
        const videoId = extractVideoId(video.url)
        const metadata = videoMetadata[videoId]
        if (!videoId || !metadata) return null

        return (
          <div key={videoId} className='group'>
            <div
              asm-tracking='CLICK_SEO_YOUTUBE:CLICK'
              asm-tracking-p-title={metadata.title}
              asm-tracking-p-url={video.url}
              className='flex cursor-pointer items-start space-x-3 rounded-lg p-2 transition-colors hover:bg-gray-50'
              onClick={() => setActiveVideoId(videoId)}>
              <div className='relative aspect-video w-32 flex-shrink-0 overflow-hidden rounded-md'>
                <img
                  src={metadata.thumbnailUrl}
                  alt={metadata.title}
                  className='h-full w-full object-cover'
                  loading='lazy'
                />
                <div className='bg-black/5 group-hover:bg-black/10 absolute inset-0 flex items-center justify-center transition-colors'>
                  <div className='flex h-8 w-8 items-center justify-center rounded-full bg-red-600/90'>
                    <svg className='h-4 w-4 text-white' fill='currentColor' viewBox='0 0 24 24'>
                      <path d='M8 5v14l11-7z' />
                    </svg>
                  </div>
                </div>
              </div>
              <div className='min-w-0 flex-1'>
                <h3 className='truncate text-sm font-medium text-gray-900'>
                  {truncateText(metadata.title, titleMaxLength)}
                </h3>
                {video.description && (
                  <p className='mt-1 line-clamp-2 text-xs text-gray-500'>
                    {truncateText(video.description, descriptionMaxLength)}
                  </p>
                )}
              </div>
            </div>
            {activeVideoId === videoId && (
              <YouTubeModal
                videoId={videoId}
                isOpen={true}
                onClose={() => setActiveVideoId(null)}
              />
            )}
          </div>
        )
      })}
    </div>
  )
}

export default YouTubePreview
