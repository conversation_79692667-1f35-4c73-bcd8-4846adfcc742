import { SupportedLangs } from '@/i18n'
import { getCookie } from '@/lib/cookie'
import { serverLog } from '@/lib/log'
import { Tracking, TrackingEventType } from '@/utils/tracking/index'
import '@radix-ui/themes/styles.css'
import { AppContext, AppProps } from 'next/app'
import { Roboto } from 'next/font/google'
import Head from 'next/head'
import { useEffect } from 'react'
import '../src/styles/globals.scss'
import Providers from './Providers'
import BaseLayout from '@/components/layouts/BaseLayout'

const roboto = Roboto({
  subsets: ['latin'],
  variable: '--font-roboto',
  weight: ['400', '500', '700', '900'],
})

const SmartiesApp = ({ Component, pageProps, lang }: AppProps & { lang?: SupportedLangs }) => {
  // Initialize tracking systems
  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      // Initialize the auto-tracking system and tracking highlight feature
      Tracking.initAutoTracking()
      // Explicitly initialize the tracking highlight feature to ensure it's available on all pages
      Tracking.initTrackingHighlight()
      Tracking.trackEvent('TEST_VISIT_DOMAIN_PAGE', TrackingEventType.VIEW)
      console.log('Tracking systems initialized')
    }
  }, [])

  return (
    <>
      <Head>
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
        />
        <title>AI Smarties | Business research assistant</title>
        <link rel='icon' type='image/svg+xml' href='/seo/images/logo.svg' className='flex-col' />
      </Head>
      <main className={roboto.variable}>
        <div suppressHydrationWarning={true}>
          <Providers lang={lang || SupportedLangs.EN}>
            <BaseLayout>
              <Component {...pageProps} />
            </BaseLayout>
          </Providers>
        </div>
        {/*<Toaster />*/}
      </main>
    </>
  )
}

SmartiesApp.getInitialProps = async ({
  Component,
  ctx,
  router,
}: AppContext): Promise<AppProps & { lang?: SupportedLangs }> => {
  let pageProps = {}
  if (Component.getInitialProps) {
    pageProps = await Component.getInitialProps(ctx)
  }
  serverLog('getInitialProps from _app.tsx', {
    asPath: ctx.asPath,
    pathname: ctx.pathname,
  })

  // 从 cookie 中获取 lang
  const langFromCookie = ctx.req ? getCookie('lang', ctx) : getCookie('lang')
  const lang = (langFromCookie as SupportedLangs) || SupportedLangs.EN

  return {
    lang,
    pageProps,
    Component,
    router,
  }
}

export default SmartiesApp
