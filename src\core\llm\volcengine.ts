interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface ChatCompletionResponse {
  id: string
  choices: {
    message: {
      content: string
    }
  }[]
}

const MODEL_MAP: Record<string, string> = {
  deepseekV3: 'ep-20250219161131-x8jnr',
  deepseekR1: 'ep-20250219160724-qkwdq',
  doubao256k: 'ep-20250401064736-6vh9d',
}

export async function ask(
  sys_prompt: string,
  user_query: string,
  model: string = 'deepseekV3',
): Promise<any> {
  const baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
  const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY

  if (!apiKey) {
    throw new Error('DEEPSEEK API key is not configured')
  }

  const messages: ChatMessage[] = [
    {
      role: 'system',
      content: sys_prompt,
    },
    {
      role: 'user',
      content: user_query,
    },
  ]

  try {
    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: MODEL_MAP[model],
        messages,
      }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error?.message || 'Failed to get response from Deepseek')
    }

    const data: ChatCompletionResponse = await response.json()
    const content = data.choices[0]?.message?.content

    if (!content) {
      throw new Error('No content in response')
    }

    try {
      return content
    } catch (e) {
      throw new Error('Failed to parse response as JSON')
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error
    }
    throw new Error('An unexpected error occurred')
  }
}

export async function ask_with_stream(
  sys_prompt: string,
  user_query: string,
  onChunk: (chunk: string) => void,
  model: string = 'deepseekV3',
): Promise<void> {
  const baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
  const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || 'f26f5635-5821-4bdc-a7f8-e9c6a685cedf'

  if (!apiKey) {
    throw new Error('DEEPSEEK API key is not configured')
  }

  const messages: ChatMessage[] = [
    {
      role: 'system',
      content: sys_prompt,
    },
    {
      role: 'user',
      content: user_query,
    },
  ]

  try {
    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: MODEL_MAP[model],
        messages,
        stream: true,
      }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error?.message || 'Failed to get response from Deepseek')
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('Failed to get response reader')
    }

    const decoder = new TextDecoder()
    // eslint-disable-next-line no-constant-condition
    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value)
      const lines = chunk.split('\n').filter((line) => line.trim() !== '')

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6)
          if (data === '[DONE]') {
            continue
          }
          try {
            const parsed = JSON.parse(data)
            const content = parsed.choices[0]?.delta?.content
            if (content) {
              onChunk(content)
            }
          } catch (e) {
            console.error('Failed to parse chunk:', e)
          }
        }
      }
    }
  } catch (error) {
    if (error instanceof Error) {
      throw error
    }
    throw new Error('An unexpected error occurred')
  }
}
