import React from 'react';
import TrackingExample from '@/components/examples/TrackingExample';
import CommonLayout from '@/components/seo/CommonLayout';

/**
 * Demo page to showcase the declarative tracking functionality
 */
const TrackingDemoPage: React.FC = () => {
  return (
    <CommonLayout>
      <div className="max-w-4xl mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-6">Declarative Tracking Demo</h1>
        
        <div className="mb-8">
          <p className="text-lg mb-4">
            This page demonstrates how to use the new declarative tracking system. 
            Elements with <code className="bg-gray-100 px-1 rounded">asm-tracking</code> attributes 
            will automatically send tracking events based on user interactions.
          </p>
          
          <div className="bg-blue-50 p-4 rounded-md border-l-4 border-blue-500">
            <h3 className="font-semibold text-blue-800">Developer Note</h3>
            <p className="text-sm text-blue-700">
              Open your browser&#39;s console to see tracking events being logged. In a production environment,
              these events would be sent to Mixpanel or other analytics platforms.
            </p>
          </div>
        </div>
        
        <TrackingExample />

        <button>Login</button>
        
        <div className="mt-12 p-6 bg-gray-50 rounded-lg">
          <h2 className="text-xl font-bold mb-4">Implementation Details</h2>
          <p className="mb-4">
            The declarative tracking system works by:
          </p>
          <ol className="list-decimal pl-5 space-y-2">
            <li>Scanning the DOM for elements with <code className="bg-gray-100 px-1 rounded">asm-tracking</code> attributes</li>
            <li>Attaching appropriate event listeners based on the event type</li>
            <li>Collecting common properties and custom parameters</li>
            <li>Sending the tracking data to the analytics platform</li>
          </ol>
          
          <div className="mt-6">
            <h3 className="font-semibold mb-2">Supported Event Types:</h3>
            <ul className="list-disc pl-5 grid grid-cols-2 gap-2 text-sm">
              <li>VIEW (on render)</li>
              <li>CLICK (on click)</li>
              <li>HOVER (on mouseenter)</li>
              <li>FOCUS (on focus)</li>
              <li>BLUR (on blur)</li>
              <li>SUBMIT (on form submit)</li>
              <li>CHANGE (on input change)</li>
              <li>KEYPRESS (on key press)</li>
            </ul>
          </div>
        </div>
      </div>
    </CommonLayout>
  );
};

export default TrackingDemoPage;
