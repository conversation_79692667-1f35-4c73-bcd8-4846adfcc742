export interface CitationEntity {
  meta: {
    url: string
    title: string
    page_id: string
  }
  text: string
  id: string
}

export interface Citation {
  page_id?: string
  url: string
  title: string
  text?: string
}

export interface Reference {
  url: string
  title: string
}

export interface Citations {
  [key: string]: Citation
}

export interface LocalizedContent {
  title: string
  keywords: string
  description: string
  content: string
  domain: string
  region: string
  relates: Reference[]
}

export type Language = 'zh-CN' | 'zh-TW' | 'en' | 'ja' | 'de' | 'fr'

export interface MarketData {
  marketDomain: string
  marketRegion: string
  marketYear: number
  relatedDomains: string[]
  updatedAt: string
  localizedData: Record<Language, LocalizedContent>
  citations?: Citations
  references?: Reference[]
}
