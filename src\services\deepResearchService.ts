import { API_CONFIG } from './config'

/**
 * Types for the API responses
 */
export interface AnalysisStep {
  步骤: string
  'google query': string[]
}

export interface AnswerFramework {
  标题: string
  分析prompt: string
  向量检索query: string[]
}

export interface ResearchData {
  分析步骤: Array<{
    'google query': string[]
    步骤: Array<{
      步骤: string
    }>
  }>
  合规判定?: boolean
  原始问题?: string
  问题确认?: string
  回答框架?: Array<AnswerFramework>
}

export interface ApiResponse {
  code: number
  message: string
  success: boolean
  data?: ResearchData
}

export type InitRequirementResponse = ApiResponse
export type ConfirmRequirementResponse = ApiResponse

/**
 * Deep Research API service
 */
export class DeepResearchService {
  /**
   * Initialize a research requirement
   * @param question The user's research question
   * @returns Promise with the API response
   */
  static async initRequirement(question: string): Promise<InitRequirementResponse> {
    try {
      const response = await fetch(`${API_CONFIG.host}/deep-research/init_requirement`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ question }),
      })

      const data = await response.json()
      return data
    } catch (error) {
      // Return a standardized error response
      return {
        code: 500,
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false,
      }
    }
  }

  /**
   * Confirm a research requirement
   * @param question The original user's research question
   * @param confirmation The confirmation text
   * @returns Promise with the API response
   */
  static async confirmRequirement(
    question: string,
    confirmation: string,
  ): Promise<ConfirmRequirementResponse> {
    try {
      const response = await fetch(`${API_CONFIG.host}/deep-research/confirm_requirement`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ question, confirmation }),
      })

      const data = await response.json()
      return data
    } catch (error) {
      // Return a standardized error response
      return {
        code: 500,
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        success: false,
      }
    }
  }
}
