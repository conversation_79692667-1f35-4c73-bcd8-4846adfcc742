import React, { useCallback, useEffect, useState } from 'react'
import { debounce } from 'lodash'

interface Heading {
  id: string
  text: string
  level: number
  element: HTMLElement
}

interface TableOfContentsProps {
  containerRef: React.RefObject<HTMLElement>
}

const TableOfContents: React.FC<TableOfContentsProps> = ({ containerRef }) => {
  const [headings, setHeadings] = useState<Heading[]>([])
  const [activeId, setActiveId] = useState<string>('')

  // 初始化标题列表
  useEffect(() => {
    if (!containerRef.current) return

    const initHeadings = () => {
      const headingElements = containerRef.current?.querySelectorAll('h2, h3, h4')
      if (!headingElements) return

      const headingsList: Heading[] = Array.from(headingElements).map((el, index) => {
        const id = `toc-heading-${index}`
        el.id = id
        return {
          id,
          text: el.textContent || '',
          level: parseInt(el.tagName[1]),
          element: el as HTMLElement,
        }
      })

      setHeadings(headingsList)
    }

    const observerCallback = () => {
      initHeadings()
    }

    const observer = new MutationObserver(observerCallback)
    observer.observe(containerRef.current, {
      childList: true,
      subtree: true,
      characterData: true,
    })

    // 立即执行一次初始化
    observerCallback()

    return () => observer.disconnect()
  }, [containerRef])

  // 处理滚动，更新活动标题
  const handleScroll = useCallback(
    debounce(() => {
      if (headings.length === 0) return

      const scrollPosition = window.scrollY + 100 // 添加偏移量以提前激活标题

      // 找到当前可见的标题
      for (const heading of headings) {
        const { element, id } = heading
        if (element.offsetTop > scrollPosition) {
          // 如果找到第一个在视口下方的标题，使用它的前一个标题
          const currentIndex = headings.findIndex((h) => h.id === id)
          if (currentIndex > 0) {
            setActiveId(headings[currentIndex - 1].id)
          }
          return
        }
      }

      // 如果所有标题都在视口上方，激活最后一个标题
      setActiveId(headings[headings.length - 1].id)
    }, 100),
    [headings],
  )

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    handleScroll()
    return () => window.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  const scrollToHeading = (id: string) => {
    const heading = document.getElementById(id)
    if (heading) {
      const offset = heading.offsetTop - 80 // 考虑固定头部的高度
      window.scrollTo({
        top: offset,
        behavior: 'smooth',
      })
      setActiveId(id)
    }
  }

  if (headings.length === 0) return null

  return (
    <div className='rounded-md border border-gray-100 bg-white p-4 shadow-sm'>
      {/*<h3 className='text-lg font-semibold text-gray-800 mb-3'>Section Navigation</h3>*/}
      <ul className='space-y-2.5 text-sm'>
        {headings.map(({ id, text, level }) => (
          <li key={id} style={{ paddingLeft: `${(level - 2) * 12}px` }} className='truncate'>
            <button
              onClick={() => scrollToHeading(id)}
              className={`w-full truncate text-left transition-colors duration-200 hover:text-blue-600 ${
                activeId === id ? 'font-medium text-blue-600' : 'text-gray-600'
              }`}
              title={text}>
              {text}
            </button>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default TableOfContents
