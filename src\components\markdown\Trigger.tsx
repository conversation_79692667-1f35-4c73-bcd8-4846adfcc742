import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  Toolt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from '@/components/common/Tooltip'
import { memo, useCallback, useEffect, useRef, useState } from 'react'

const isValidUrl = (string: string | URL) => {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}
const Trigger = memo(({ index, url }: { index: number; url: string }) => {
  const triggerRef = useRef<HTMLDivElement>(null)
  const [side, setSide] = useState<'start' | 'end'>('start')
  const openUrl = useCallback(() => {
    if (isValidUrl(url)) {
      window.open(url, '_blank')
    }
  }, [url])

  useEffect(() => {
    const trigger = triggerRef.current
    if (!trigger) {
      return
    }

    const parent = document.querySelector('.ai-smarties-md-text') as HTMLElement | null
    if (!parent) {
      return
    }

    const triggerRect = trigger.getBoundingClientRect()
    const parentRect = parent.getBoundingClientRect()

    const spaceRight = parentRect.right - triggerRect.right

    if (spaceRight > Math.min(400, url.length * 8) - 130) {
      setSide('start')
    } else {
      setSide('end')
    }
  }, [])

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger>
          <div
            onClick={openUrl}
            ref={triggerRef}
            aria-label={`Open link ${url}`}
            className='flex-center cursor-pointer text-primary'>
            [{index + 1}]
          </div>
        </TooltipTrigger>
        <TooltipContent
          side='bottom'
          align={side}
          sideOffset={0}
          avoidCollisions={false}
          className='z-[1000] rounded-sm'>
          <div className='max-w-[400px] overflow-hidden text-ellipsis whitespace-nowrap text-left underline'>
            {url}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
})
Trigger.displayName = 'Trigger'
export default Trigger
