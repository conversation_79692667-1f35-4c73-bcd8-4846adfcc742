import { initReactI18next } from 'react-i18next'
import type { Resource } from 'i18next'
import i18n from 'i18next'
import zh from './locales/zh'
import en from './locales/en'

export enum Namespace {
  GLOBAL = 'global',
  HOME = 'home',
  PRICING = 'pricing',
  TOC = 'toc',
  SHARE = 'share',
  ABOUT = 'about',
}
export enum SupportedLangs {
  EN = 'en',
  ZH = 'zh',
}
const resources: Resource = {
  en,
  zh,
}
const ns = Object.values(Namespace)

export function getI18n(lng: SupportedLangs) {
  i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      resources,
      fallbackLng: SupportedLangs.EN,
      ns,
      defaultNS: Namespace.GLOBAL,
      interpolation: {
        escapeValue: false,
      },
      returnNull: false,
      lng,
    })

  return i18n
}
