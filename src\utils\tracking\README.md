# 追踪系统工具

## 概述

这个模块提供了网站追踪和埋点相关的工具函数，包括自动追踪、调试高亮等功能。

## 📁 文件结构

- `auto-tracking.ts` - 自动追踪配置和事件定义
- `common.ts` - 追踪相关的通用工具函数
- `debug.ts` - 调试工具
- `tracking-highlight.ts` - 追踪元素高亮功能（开发调试用）
- `types.ts` - TypeScript 类型定义
- `index.ts` - 模块导出

## 🐛 最近修复的问题

### TypeError: Cannot read properties of undefined (reading 'toLowerCase')

**问题描述：**
在 `tracking-highlight.ts` 文件中，当处理键盘事件时可能遇到 `event.key` 为 `undefined` 的情况，导致调用 `toLowerCase()` 方法时出错。

**修复内容：**

1. ✅ 添加了安全检查：确保 `event` 和 `event.key` 存在且为字符串类型
2. ✅ 添加了 try-catch 错误处理，避免程序崩溃
3. ✅ 增加了开发环境下的调试日志
4. ✅ 添加了 `cleanup()` 方法，防止内存泄漏
5. ✅ 改进了类型检查和注释

**修复后的代码：**

```typescript
handleKeyDown(event: KeyboardEvent) {
  try {
    // 安全检查，确保event和event.key存在
    if (!event || typeof event.key !== 'string') {
      if (process.env.NODE_ENV !== 'production') {
        console.warn('[Tracking-Highlight] Invalid event object:', event)
      }
      return
    }

    if (event.key.toLowerCase() === 't' && event.ctrlKey) {
      if (!this.isHighlighting) {
        this.startHighlighting()
      }
    }
  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.error('[Tracking-Highlight] Error in handleKeyDown:', error)
    }
  }
}
```

## 🚀 使用方法

### 追踪高亮功能 (开发环境)

用于可视化页面上的追踪元素，方便开发调试。

**初始化：**

```typescript
import { trackingHighlight } from '@/utils/tracking/tracking-highlight'

// 初始化（通常在应用启动时调用）
trackingHighlight.init()
```

**使用快捷键：**

- `Ctrl + T`：开始/停止追踪元素高亮
- 高亮模式下会显示所有带有 `asm-tracking` 属性的元素
- 鼠标悬停可查看元素的追踪信息

**清理：**

```typescript
// 组件卸载时清理（防止内存泄漏）
trackingHighlight.cleanup()
```

### 自动追踪

**添加追踪属性：**

```jsx
// 基本用法
<button asm-tracking="BUTTON_CLICK:CLICK">点击按钮</button>

// 带参数
<div
  asm-tracking="PRODUCT_VIEW:VIEW"
  asm-tracking-p-product_id="123"
  asm-tracking-p-category="electronics"
>
  产品详情
</div>
```

**追踪事件类型：**

- `VIEW` - 页面/组件浏览事件
- `CLICK` - 点击事件
- 其他自定义事件类型

## 🔧 开发调试

### 启用调试模式

在开发环境中，追踪高亮功能会自动启用。你可以：

1. 按 `Ctrl + T` 开启高亮模式
2. 页面上所有追踪元素会被高亮显示
3. 鼠标悬停查看详细信息
4. 再次按 `Ctrl + T` 关闭高亮

### 调试日志

开发环境下会输出调试信息：

```
[Tracking-Highlight] Initialized
[Tracking-Highlight] Started highlighting
[Tracking-Highlight] Stopped highlighting
```

## ⚠️ 注意事项

1. **性能影响**：追踪高亮功能仅在开发环境启用，生产环境会自动禁用
2. **浏览器兼容性**：使用了现代浏览器 API，建议在最新版浏览器中使用
3. **内存管理**：记得在合适的时机调用 `cleanup()` 方法清理资源

## 🐛 故障排除

### 常见问题

1. **高亮功能不工作**

   - 检查是否在开发环境 (`NODE_ENV !== 'production'`)
   - 确认已调用 `trackingHighlight.init()`
   - 检查控制台是否有错误信息

2. **键盘快捷键无响应**

   - 确保页面获得了焦点
   - 检查是否有其他组件拦截了键盘事件
   - 查看控制台调试日志

3. **内存泄漏问题**
   - 确保在组件卸载时调用 `cleanup()` 方法
   - 检查事件监听器是否正确移除

## 📝 更新日志

### v1.1.0 (最新)

- 🐛 修复 `event.key` 为 undefined 导致的 TypeError
- ✨ 添加完整的错误处理和调试日志
- ✨ 新增 `cleanup()` 方法，防止内存泄漏
- 📝 改进代码注释和类型定义

### v1.0.0

- 🎉 初始版本
- ✨ 基础追踪高亮功能
- ✨ 键盘快捷键支持
