import React, { useEffect, useRef, useState } from 'react'

interface Position {
  x: number
  y: number
}

interface YouTubeModalProps {
  videoId: string
  isOpen: boolean
  onClose: () => void
  draggable?: boolean // 控制小窗是否可拖动，默认为 false
}

const YouTubeModal: React.FC<YouTubeModalProps> = ({
  videoId,
  isOpen,
  onClose,
  draggable = false,
}) => {
  const [isPiP, setIsPiP] = useState(false)
  const [position, setPosition] = useState<Position>(() => {
    if (typeof window === 'undefined') return { x: 20, y: 20 }
    return {
      x: window.innerWidth - 340, // 320px width + 20px margin
      y: window.innerHeight - 200, // approximate height + margin
    }
  })
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState<Position>({ x: 0, y: 0 })
  const modalRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && !isPiP) {
        onClose()
      }
    }

    if (isOpen && !isPiP) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      if (!isPiP) document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose, isPiP])

  // 监听窗口大小变化
  useEffect(() => {
    if (!isPiP) return

    const handleResize = () => {
      const maxX = window.innerWidth - 320
      const maxY = window.innerHeight - 180

      setPosition((prev) => ({
        x: Math.min(prev.x, maxX),
        y: Math.min(prev.y, maxY),
      }))
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [isPiP])

  const handleDragStart = (e: React.MouseEvent) => {
    if (!draggable) return
    e.preventDefault() // 防止文本选择
    if (!modalRef.current) return

    const rect = modalRef.current.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    })
    setIsDragging(true)
    console.debug(isDragging)

    // 添加全局事件监听
    const handleGlobalMouseMove = (e: MouseEvent) => {
      const maxX = window.innerWidth - 320
      const maxY = window.innerHeight - 180

      setPosition({
        x: Math.min(Math.max(0, e.clientX - dragOffset.x), maxX),
        y: Math.min(Math.max(0, e.clientY - dragOffset.y), maxY),
      })
    }

    const handleGlobalMouseUp = () => {
      setIsDragging(false)
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }

    document.addEventListener('mousemove', handleGlobalMouseMove)
    document.addEventListener('mouseup', handleGlobalMouseUp)
  }

  const togglePiP = () => {
    setIsPiP(!isPiP)
    if (!isPiP) {
      document.body.style.overflow = 'unset'
    }
  }

  if (!isOpen) return null

  if (isPiP) {
    return (
      <div
        ref={modalRef}
        className='fixed z-[100] rounded-lg bg-white shadow-xl transition-all'
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: '320px',
        }}>
        {draggable && (
          <>
            {/* Top drag handle */}
            <div
              className='absolute -top-2 left-0 right-0 h-4 cursor-move rounded-t hover:bg-gray-200/50'
              onMouseDown={handleDragStart}
            />
            {/* Right drag handle */}
            <div
              className='absolute -right-2 bottom-0 top-0 w-4 cursor-move hover:bg-gray-200/50'
              onMouseDown={handleDragStart}
            />
            {/* Bottom drag handle */}
            <div
              className='absolute -bottom-2 left-0 right-0 h-4 cursor-move rounded-b hover:bg-gray-200/50'
              onMouseDown={handleDragStart}
            />
            {/* Left drag handle */}
            <div
              className='absolute -left-2 bottom-0 top-0 w-4 cursor-move hover:bg-gray-200/50'
              onMouseDown={handleDragStart}
            />
            {/* Corner drag handles */}
            <div
              className='absolute -left-2 -top-2 h-6 w-6 cursor-move rounded-tl hover:bg-gray-200/50'
              onMouseDown={handleDragStart}
            />
            <div
              className='absolute -right-2 -top-2 h-6 w-6 cursor-move rounded-tr hover:bg-gray-200/50'
              onMouseDown={handleDragStart}
            />
            <div
              className='absolute -bottom-2 -left-2 h-6 w-6 cursor-move rounded-bl hover:bg-gray-200/50'
              onMouseDown={handleDragStart}
            />
            <div
              className='absolute -bottom-2 -right-2 h-6 w-6 cursor-move rounded-br hover:bg-gray-200/50'
              onMouseDown={handleDragStart}
            />
          </>
        )}
        <div className='relative'>
          <div className='absolute left-2 top-2 z-10 flex space-x-2'>
            <button
              onClick={togglePiP}
              className='bg-black/50 hover:bg-black/70 rounded-full p-1.5 text-white'
              title='Expand'>
              <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M4 8V4m0 0h4M4 4l5 5m11-5h-4m4 0v4m0-4l-5 5M4 16v4m0-4l5-5m11 5l-5-5m5 5v-4'
                />
              </svg>
            </button>
            <button
              onClick={onClose}
              className='bg-black/50 hover:bg-black/70 rounded-full p-1.5 text-white'
              title='Close'>
              <svg className='h-4 w-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M6 18L18 6M6 6l12 12'
                />
              </svg>
            </button>
          </div>
          <div className='relative aspect-video w-full'>
            <iframe
              className='absolute inset-0 h-full w-full'
              src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
              allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
              allowFullScreen
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='fixed inset-0 z-[100] overflow-y-auto'>
      <div className='flex min-h-screen items-center justify-center px-4 pb-20 pt-20 text-center sm:block sm:p-0'>
        <div
          className='fixed inset-0 bg-white bg-opacity-60 backdrop-blur-sm transition-opacity'
          onClick={onClose}
        />

        <div className='relative top-20 inline-block w-full max-w-4xl transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:align-middle'>
          <div className='relative'>
            {/* Close button */}
            <div className='absolute right-2 top-5 z-10 flex space-x-2'>
              <button
                onClick={togglePiP}
                className='bg-black/50 hover:bg-black/70 rounded-full p-2 text-white'
                title='Picture in Picture'>
                <svg className='h-6 w-6' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h12a2 2 0 012 2v2M8 16v-2a2 2 0 012-2h8a2 2 0 012 2v8a2 2 0 01-2 2h-8a2 2 0 01-2-2z'
                  />
                </svg>
              </button>
              <button
                onClick={onClose}
                className='bg-black/50 hover:bg-black/70 rounded-full p-2 text-white'
                title='Close'>
                <svg className='h-6 w-6' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M6 18L18 6M6 6l12 12'
                  />
                </svg>
              </button>
            </div>

            {/* YouTube iframe */}
            <div className='relative aspect-video w-full'>
              <iframe
                className='absolute inset-0 h-full w-full'
                src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
                allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
                allowFullScreen
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default YouTubeModal
