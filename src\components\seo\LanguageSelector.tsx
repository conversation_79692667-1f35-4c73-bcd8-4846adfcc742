import React from 'react'
import { Language } from '@/types/market'

interface LanguageSelectorProps {
  currentLang: Language
  availableLanguages: Language[]
  onLanguageChange: (newLang: Language) => void
}

const languageNames: Record<Language, string> = {
  'zh-CN': '中',
  'zh-TW': '繁',
  en: 'En',
  ja: '日',
  de: 'Deutsch',
  fr: 'Français',
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  currentLang,
  availableLanguages,
  onLanguageChange,
}) => {
  // 确保只显示在 availableLanguages 中的语言
  const displayLanguages = availableLanguages.filter((lang) =>
    Object.keys(languageNames).includes(lang),
  )

  return (
    <div className='flex items-center'>
      <span className='mr-2 h-3 w-3 rounded-full bg-purple-100 md:h-4 md:w-4'></span>
      <span className='mr-2 font-medium text-gray-700'>Lang:</span>
      <div className='flex space-x-1'>
        {displayLanguages.map((code) => (
          <button
            key={code}
            onClick={() => onLanguageChange(code)}
            className={`rounded px-2 py-0.5 text-xs font-medium transition-colors duration-200 ${
              currentLang === code
                ? 'bg-purple-100 text-purple-700'
                : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
            }`}>
            {languageNames[code]}
          </button>
        ))}
      </div>
    </div>
  )
}

export default LanguageSelector
