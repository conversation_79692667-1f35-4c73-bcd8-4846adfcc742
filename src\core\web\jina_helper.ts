interface JinaSearchResponse {
  data: Array<{
    title: string
    url: string
    favicon?: string
    description?: string
  }>
  meta: {
    total: number
    took: number
  }
}

interface JinaSearchParams {
  q: string
  num?: number
}

export class JinaHelper {
  private static readonly BASE_URL = 'https://s.jina.ai'
  private static readonly DEFAULT_NUM = 10
  private static readonly TIMEOUT = 10

  /**
   * 执行 Jina AI 搜索
   * @param params 搜索参数
   * @returns Promise<JinaSearchResponse>
   */
  static async search(params: JinaSearchParams): Promise<JinaSearchResponse> {
    const { q, num = this.DEFAULT_NUM } = params
    const apiKey = process.env.NEXT_PUBLIC_JINA_API_KEY

    if (!apiKey) {
      throw new Error('Jina API key is not configured')
    }

    try {
      const response = await fetch(this.BASE_URL, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`,
          'X-Engine': 'direct',
          'X-Timeout': this.TIMEOUT.toString(),
          'X-With-Favicons': 'true',
        },
        body: JSON.stringify({
          q,
          num,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to get response from Jina')
      }

      return await response.json()
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Jina search failed: ${error.message}`)
      }
      throw new Error('An unexpected error occurred during Jina search')
    }
  }
}
