import React from 'react'
import { Citations, Reference } from '@/types/market'

interface ReferencesProps {
  citations?: Citations
  references?: Reference[]
}

export const References: React.FC<ReferencesProps> = ({ citations = {}, references = [] }) => {
  let uniqueCitations: { [key: string]: { url: string; title: string } } = {}

  if (references) {
    references.map((ref, index) => {
      uniqueCitations[index.toString()] = {
        url: ref.url.replaceAll('"', ''),
        title: ref.title,
      }
    })
  } else {
    // 根据 URL 去重并保留第一个出现的引用
    uniqueCitations = Object.entries(citations).reduce<
      Record<string, (typeof citations)[keyof typeof citations]>
    >((acc, [id, citation]) => {
      const url = citation.url
      if (!Object.values(acc).some((c) => c.url === url)) {
        acc[id] = citation
      }
      return acc
    }, {})
  }

  return (
    <div className='mt-8'>
      <h2 className='mb-4 text-2xl font-semibold'>References</h2>
      <div className='space-y-2'>
        {Object.entries(uniqueCitations).map(([id, citation], index) => (
          <div key={id || citation.url} className='flex items-baseline gap-2'>
            <span className='text-sm'>{index + 1}.</span>
            <a
              href={citation.url}
              target='_blank'
              className='text-primary hover:underline'
              rel='noreferrer'>
              {citation.title}
            </a>
          </div>
        ))}
      </div>
    </div>
  )
}
