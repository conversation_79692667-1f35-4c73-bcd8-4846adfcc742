import React, { useState, useRef, useEffect } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

// 卡片数据接口定义
interface CardData {
  title: string
  icon: string
  metric: string
  metric_desc: string
  insight: string
}

// 卡片图标映射 - 使用Phosphor图标
const cardIcons = [
  'ph-rocket-launch',
  'ph-castle-turret',
  'ph-coffee',
  'ph-map-trifold',
  'ph-warning-octagon',
]

// Phosphor图标组件
const PhosphorIcon: React.FC<{ iconName: string; className?: string }> = ({
  iconName,
  className = '',
}) => {
  return <i className={`ph-bold ${iconName} ${className}`} />
}

// FormSubmit配置 - 请将下面的邮箱替换为您的实际邮箱地址
// 使用说明：
// 1. 将 '<EMAIL>' 替换为您的真实邮箱地址
// 2. 首次使用时，FormSubmit会发送一封确认邮件到您的邮箱
// 3. 点击确认邮件中的链接激活服务
// 4. 激活后，所有表单提交都会发送到您的邮箱
// 5. 用户也会收到自动回复邮件确认提交成功
const FORMSUBMIT_EMAIL = '<EMAIL>' // 替换为您的邮箱地址

// 检查是否为默认邮箱配置
const isDefaultEmail = FORMSUBMIT_EMAIL.includes('<EMAIL>') || FORMSUBMIT_EMAIL.length === 32

// 开发者提示: 检查FormSubmit配置
if (typeof window !== 'undefined' && isDefaultEmail) {
  console.warn('⚠️  FormSubmit未配置: 请将 FORMSUBMIT_EMAIL 设置为您的真实邮箱地址')
} else if (typeof window !== 'undefined') {
  console.log('✅ FormSubmit已配置为:', FORMSUBMIT_EMAIL)
}

const IntelDossierPage: React.FC = () => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const [cards, setCards] = useState<number[]>([])
  const [activeCardIndex, setActiveCardIndex] = useState<number | null>(null)
  const [flippedCards, setFlippedCards] = useState<Set<number>>(new Set())
  const [removingCards, setRemovingCards] = useState<Set<number>>(new Set()) // 正在移除的卡片
  const [showFinalReward, setShowFinalReward] = useState(false)
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [needsActivation, setNeedsActivation] = useState(false)

  // 卡牌操作计数器 - 实现累计追踪功能
  const [cardClickCount, setCardClickCount] = useState(0) // 点击计数器
  const [cardSwipeCount, setCardSwipeCount] = useState(0) // 滑动计数器

  // 重置计数器函数 - 用于调试和管理
  const resetCardCounters = () => {
    setCardClickCount(0)
    setCardSwipeCount(0)
    if (process.env.NODE_ENV !== 'production') {
      console.log('🔄 卡牌计数器已重置')
    }
  }

  // 拖拽相关状态 - 优化性能
  const [isDragging, setIsDragging] = useState(false)
  const dragStateRef = useRef({ startX: 0, currentX: 0, isDragging: false, startTime: 0 })
  const touchDebounceRef = useRef<NodeJS.Timeout | null>(null)
  const isProcessingTouchRef = useRef(false)

  const cardStackRef = useRef<HTMLDivElement>(null)
  const rewardContentRef = useRef<HTMLDivElement>(null)
  const activeCardElementRef = useRef<HTMLElement | null>(null)

  const clickThreshold = 35 // 增加移动端点击阈值，适应手指触摸

  // 动态生成卡片数据 - 从国际化文件中读取
  const cardData: CardData[] = Array.from({ length: 5 }, (_, i) => {
    const cardKey = `card${i + 1}`
    return {
      title: t(`intelDossier.cards.${cardKey}.title`),
      icon: cardIcons[i],
      metric: t(`intelDossier.cards.${cardKey}.metric`),
      metric_desc: t(`intelDossier.cards.${cardKey}.metricDesc`),
      insight: t(`intelDossier.cards.${cardKey}.insight`),
    }
  })

  // 初始化卡片 - 让card1先显示
  useEffect(() => {
    const cardIndexes = Array.from({ length: cardData.length }, (_, i) => i)
    setCards(cardIndexes)
    setActiveCardIndex(0) // 设置第一张卡片为活跃卡片

    // 注意：页面访问埋点已通过div元素的asm-tracking自动发送
    // 包含了total_cards、page_url、user_agent等信息

    // 开发环境下暴露调试函数到全局
    if (process.env.NODE_ENV !== 'production' && typeof window !== 'undefined') {
      ;(window as any).resetCardCounters = resetCardCounters

      // 添加埋点诊断工具
      ;(window as any).diagnoseMixpanel = () => {
        console.log('🔍 Mixpanel 诊断信息:')
        console.log(
          '1. Mixpanel 对象:',
          typeof (window as any).mixpanel !== 'undefined' ? '✅ 已加载' : '❌ 未加载',
        )
        console.log(
          '2. Mixpanel Token:',
          process.env.NEXT_PUBLIC_MIXPANEL_TOKEN ? '✅ 已配置' : '❌ 未配置',
        )
        console.log('3. 当前环境:', process.env.NODE_ENV)

        if ((window as any).mixpanel) {
          console.log('4. Mixpanel 配置:', (window as any).mixpanel.config)
          console.log('5. 测试发送埋点...')
          ;(window as any).mixpanel.track('TEST_DIAGNOSTIC_EVENT', {
            timestamp: new Date().toISOString(),
            source: 'diagnostic_tool',
          })
          console.log('✅ 测试埋点已发送，请检查Network面板')
        } else {
          console.log('❌ 无法发送测试埋点：Mixpanel未加载')
        }

        return {
          mixpanelLoaded: typeof (window as any).mixpanel !== 'undefined',
          tokenConfigured: !!process.env.NEXT_PUBLIC_MIXPANEL_TOKEN,
          environment: process.env.NODE_ENV,
        }
      }

      console.log('🔧 调试函数已注册: window.resetCardCounters(), window.diagnoseMixpanel()')
    }
  }, [])

  // 开发环境下更新全局调试函数 - 确保能获取最新计数值
  useEffect(() => {
    if (process.env.NODE_ENV !== 'production' && typeof window !== 'undefined') {
      ;(window as any).getCardCounters = () => ({
        clicks: cardClickCount,
        swipes: cardSwipeCount,
        total: cardClickCount + cardSwipeCount,
        activeCard: activeCardIndex !== null ? activeCardIndex + 1 : null,
      })
    }
  }, [cardClickCount, cardSwipeCount, activeCardIndex])

  // 检查URL参数，如果有success=true，显示成功消息
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('success') === 'true') {
      // 如果是从FormSubmit重定向回来的，显示最终奖励屏幕
      setShowFinalReward(true)
      setIsSubmitted(true)
      // 触发庆祝动画
      setTimeout(() => {
        celebrateSubmission()
      }, 500)
      // 清除URL参数
      window.history.replaceState({}, '', window.location.pathname)
    }
  }, [])

  // 清理事件监听器
  useEffect(() => {
    return () => {
      // 组件卸载时清理事件监听器
      document.removeEventListener('mousemove', handleNativePointerMove)
      document.removeEventListener('mouseup', handleNativePointerUp)
      document.removeEventListener('touchmove', handleNativePointerMove)
      document.removeEventListener('touchend', handleNativePointerUp)

      // 清理定时器和状态
      if (touchDebounceRef.current) {
        clearTimeout(touchDebounceRef.current)
      }
      isProcessingTouchRef.current = false
    }
  }, [])

  // 动态加载confetti库
  useEffect(() => {
    const loadConfetti = () => {
      if (typeof window !== 'undefined' && !(window as any).confetti) {
        const script = document.createElement('script')
        script.src =
          'https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js'
        script.async = true
        document.head.appendChild(script)
      }
    }
    loadConfetti()
  }, [])

  // 显示最终奖励屏幕时的动画
  useEffect(() => {
    if (showFinalReward && rewardContentRef.current) {
      setTimeout(() => {
        if (rewardContentRef.current) {
          rewardContentRef.current.style.transform = 'scale(1)'
        }
      }, 100)
    }
  }, [showFinalReward])

  // 原生事件处理函数 - 优化流畅度
  const handleNativePointerMove = (e: MouseEvent | TouchEvent) => {
    if (!dragStateRef.current.isDragging || activeCardIndex === null) return

    // 防止默认行为，提高流畅度
    e.preventDefault()

    // 改进触摸坐标获取，处理移动端兼容性
    const clientX = 'touches' in e ? e.touches[0]?.clientX : e.clientX

    // 如果无法获取正确的坐标，则忽略这次移动
    if (typeof clientX !== 'number') return

    dragStateRef.current.currentX = clientX
    const diffX = clientX - dragStateRef.current.startX

    if (flippedCards.has(activeCardIndex)) {
      const cardElement = activeCardElementRef.current
      if (cardElement) {
        // 优化拖拽变换，增加约束和平滑度
        const maxDrag = 150 // 减少最大拖拽距离，提高响应速度
        const constrainedDiffX = Math.max(-maxDrag, Math.min(maxDrag, diffX))
        const rotation = constrainedDiffX / 20 // 减少旋转灵敏度，让效果更稳定
        const scale = 1 - Math.abs(constrainedDiffX) / 1200 // 减少缩放效果，提高流畅度

        // 使用transform3d强制GPU加速
        cardElement.style.transform = `translate3d(${constrainedDiffX}px, 0, 0) rotate(${rotation}deg) scale(${scale})`
      }
    }
  }

  const handleNativePointerUp = () => {
    // 移除事件监听器
    document.removeEventListener('mousemove', handleNativePointerMove)
    document.removeEventListener('mouseup', handleNativePointerUp)
    document.removeEventListener('touchmove', handleNativePointerMove)
    document.removeEventListener('touchend', handleNativePointerUp)

    if (!dragStateRef.current.isDragging || activeCardIndex === null) return

    // 重置拖拽状态
    dragStateRef.current.isDragging = false
    setIsDragging(false)

    // 确保坐标有效
    const startX = dragStateRef.current.startX || 0
    const currentX = dragStateRef.current.currentX || 0
    const diffX = currentX - startX
    const touchDuration = Date.now() - (dragStateRef.current.startTime || 0)
    const cardElement = activeCardElementRef.current

    // 更严格的点击判断：距离小且时间短
    const isClick = Math.abs(diffX) < clickThreshold && touchDuration < 500 // 500ms内的快速触摸

    if (isClick) {
      // 点击事件 - 添加防抖处理
      if (isProcessingTouchRef.current) return

      isProcessingTouchRef.current = true

      if (!flippedCards.has(activeCardIndex)) {
        // 翻转卡片 - 在翻转时就触发点击埋点
        setFlippedCards((prev) => new Set(prev).add(activeCardIndex))

        // 翻转时立即触发点击埋点
        const newClickCount = cardClickCount + 1
        setCardClickCount(newClickCount)

        // 生成累计点击计数的value值（card1, card2, card3, card4, card5）
        const clickValue = `card${newClickCount}`

        // 发送点击埋点
        if (typeof window !== 'undefined' && (window as any).mixpanel) {
          ;(window as any).mixpanel.track('TEST_CLICK_INTEL_CARD', {
            value: clickValue,
            card_title: cardData[activeCardIndex].title,
            click_count: newClickCount,
            current_card_index: activeCardIndex + 1,
            action: 'flip', // 标记为翻转动作
          })

          // 开发环境下输出埋点信息
          if (process.env.NODE_ENV !== 'production') {
            console.log('🖱️ 卡牌翻转埋点:', {
              event: 'TEST_CLICK_INTEL_CARD',
              value: clickValue,
              click_count: newClickCount,
              card_title: cardData[activeCardIndex].title,
              action: 'flip',
            })
          }
        }

        // 设置过渡效果并重置变换 - 只在翻转时重置，优化流畅度
        if (cardElement) {
          cardElement.style.transition = 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)' // 更快的翻转重置
          cardElement.style.transform = ''
        }

        // 翻转后重置处理状态
        setTimeout(() => {
          isProcessingTouchRef.current = false
        }, 500)
      } else {
        // 移除卡片 - 不重置transform，让removeActiveCard处理动画
        removeActiveCard(0)

        // 移除后重置处理状态
        setTimeout(() => {
          isProcessingTouchRef.current = false
        }, 600)
      }
      return
    }

    if (flippedCards.has(activeCardIndex)) {
      // 滑动事件 - 增加滑动阈值，避免误触
      if (Math.abs(diffX) > 80) {
        if (!isProcessingTouchRef.current) {
          isProcessingTouchRef.current = true
          const direction = diffX > 0 ? 1 : -1
          removeActiveCard(direction)

          // 滑动后重置处理状态
          setTimeout(() => {
            isProcessingTouchRef.current = false
          }, 600)
        }
      } else {
        // 回弹 - 优化流畅度
        if (cardElement) {
          cardElement.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)' // 更快更自然的回弹
          cardElement.style.transform = '' // 回到原位
        }

        // 回弹后重置处理状态
        setTimeout(() => {
          isProcessingTouchRef.current = false
        }, 400)
      }
    } else {
      // 如果没有其他操作，重置处理状态
      setTimeout(() => {
        isProcessingTouchRef.current = false
      }, 100)
    }
  }

  // 处理指针按下事件
  const handlePointerDown = (e: React.MouseEvent | React.TouchEvent) => {
    if (activeCardIndex === null || isProcessingTouchRef.current) return

    // 防抖处理，避免快速连续触摸
    if (touchDebounceRef.current) {
      clearTimeout(touchDebounceRef.current)
    }

    // 改进触摸坐标获取，处理移动端兼容性
    const clientX = 'touches' in e ? e.touches[0]?.clientX : e.clientX

    // 如果无法获取正确的坐标，则忽略这次事件
    if (typeof clientX !== 'number') return

    // 使用ref存储拖拽状态，避免频繁的状态更新
    dragStateRef.current.startX = clientX
    dragStateRef.current.currentX = clientX
    dragStateRef.current.isDragging = true
    dragStateRef.current.startTime = Date.now() // 记录开始时间
    setIsDragging(true)

    // 缓存活动卡片元素引用，避免重复查询DOM
    const cardElement = document.querySelector(
      `[data-card-index="${activeCardIndex}"]`,
    ) as HTMLElement
    activeCardElementRef.current = cardElement

    if (cardElement) {
      cardElement.style.transition = 'none' // 立即禁用过渡，确保拖拽跟手
      cardElement.style.willChange = 'transform' // 优化性能
    }

    // 添加原生事件监听器 - 与HTML文件一致
    document.addEventListener('mousemove', handleNativePointerMove)
    document.addEventListener('mouseup', handleNativePointerUp)
    document.addEventListener('touchmove', handleNativePointerMove, { passive: false })
    document.addEventListener('touchend', handleNativePointerUp)
  }

  // 移除活动卡片 - 点击向上消失，滑动左右丢弃
  const removeActiveCard = (direction: number) => {
    if (activeCardIndex === null) return

    // 根据direction区分动画效果并发送对应埋点
    if (direction === 0) {
      // 点击：向上消失 - 不发送点击埋点（已在翻转时发送）
      // 只在开发环境输出调试信息
      if (process.env.NODE_ENV !== 'production') {
        console.log('🗑️ 卡牌点击移除:', {
          card_title: cardData[activeCardIndex].title,
          current_card_index: activeCardIndex + 1,
          action: 'remove',
          note: '埋点已在翻转时发送',
        })
      }
    } else {
      // 滑动：左右丢弃 - 发送滑动埋点
      const newSwipeCount = cardSwipeCount + 1
      setCardSwipeCount(newSwipeCount)

      // 生成累计滑动计数的value值（card1, card2, card3, card4, card5）
      const swipeValue = `card${newSwipeCount}`

      // 发送滑动埋点
      if (typeof window !== 'undefined' && (window as any).mixpanel) {
        ;(window as any).mixpanel.track('TEST_SWIPE_INTEL_CARD', {
          value: swipeValue,
          card_title: cardData[activeCardIndex].title,
          swipe_count: newSwipeCount,
          current_card_index: activeCardIndex + 1,
          direction: direction > 0 ? 'right' : 'left',
        })

        // 开发环境下输出埋点信息
        if (process.env.NODE_ENV !== 'production') {
          console.log('📱 卡牌滑动埋点:', {
            event: 'TEST_SWIPE_INTEL_CARD',
            value: swipeValue,
            swipe_count: newSwipeCount,
            card_title: cardData[activeCardIndex].title,
            direction: direction > 0 ? 'right' : 'left',
          })
        }
      }
    }

    // 立即标记为正在移除，防止进一步交互
    setRemovingCards((prev) => new Set(prev).add(activeCardIndex))

    // 使用缓存的卡片元素引用，避免重复查询DOM
    const cardElement = activeCardElementRef.current
    if (cardElement) {
      // 移除事件监听器，防止在动画过程中继续触发
      cardElement.style.pointerEvents = 'none'

      // 设置过渡效果 - 与HTML保持一致
      cardElement.style.transition = 'transform 0.5s ease-out, opacity 0.5s ease-out'

      // 根据direction区分动画效果
      if (direction === 0) {
        // 点击：向上消失
        cardElement.style.transform = `translate3d(0, -150%, 0) rotate(15deg) scale(0.8)`
      } else {
        // 滑动：左右丢弃
        cardElement.style.transform = `translate3d(${direction * 150}%, 0, 0) rotate(${direction * 45}deg) scale(0.8)`
      }
      cardElement.style.opacity = '0'

      // 添加important标记，确保样式不被覆盖
      cardElement.style.setProperty('transform', cardElement.style.transform, 'important')
      cardElement.style.setProperty('opacity', '0', 'important')
    }

    // 找到下一个活跃卡片
    const currentCardIndex = cards.findIndex((cardIdx) => cardIdx === activeCardIndex)
    const newCards = cards.filter((_, index) => index !== currentCardIndex)

    if (newCards.length > 0) {
      // 设置下一个卡片为活跃卡片
      setActiveCardIndex(newCards[0])
      // 清空当前卡片元素引用
      activeCardElementRef.current = null
    } else {
      setActiveCardIndex(null)
      activeCardElementRef.current = null
      setTimeout(() => setShowFinalReward(true), 300) // 与HTML文件保持一致，300ms延迟
    }

    // 延迟移除卡片，让动画完成
    setTimeout(() => {
      setCards(newCards)
      setRemovingCards((prev) => {
        const updated = new Set(prev)
        updated.delete(activeCardIndex)
        return updated
      })
    }, 500) // 与消失动画时间一致
  }

  // 检查FormSubmit状态的函数 (用于激活检查)
  const checkFormSubmitStatus = async () => {
    try {
      console.log('🔍 检查FormSubmit状态...')
      // 简单的状态检查
      window.open(`https://formsubmit.co/${FORMSUBMIT_EMAIL}`, '_blank')

      setTimeout(() => {
        setSubmitError(null)
        setNeedsActivation(false)
      }, 1000)

      return true
    } catch (error) {
      console.error('❌ 无法检查FormSubmit状态:', error)
      return false
    }
  }

  // 处理表单提交 - 使用JavaScript Ajax提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault() // 阻止默认的表单提交行为
    setIsSubmitting(true)
    setSubmitError(null)

    // 注意：邮箱提交埋点已通过按钮上的asm-tracking自动发送
    // 包含了value、total_clicks、total_swipes、total_interactions等信息

    try {
      // 构建表单数据
      const formData = new FormData()
      formData.append('email', email)
      formData.append('name', '用户')
      formData.append('subject', '新的Intel Dossier报告请求')
      formData.append(
        'message',
        `用户请求Intel Dossier报告：美国无糖饮料市场\n\n用户邮箱：${email}\n请求时间：${new Date().toLocaleString('zh-CN')}\n来源：AI Smarties - Intel Dossier页面`,
      )

      // FormSubmit配置
      formData.append('_subject', 'Intel Dossier报告请求 - AI Smarties')
      formData.append(
        '_autoresponse',
        '感谢您的Intel Dossier报告请求！我们将在24小时内发送完整报告到您的邮箱。',
      )
      formData.append('_template', 'table')
      formData.append('_captcha', 'false')

      console.log('🚀 正在提交到FormSubmit:', `https://formsubmit.co/${FORMSUBMIT_EMAIL}`)

      // 发送请求
      const response = await fetch(`https://formsubmit.co/${FORMSUBMIT_EMAIL}`, {
        method: 'POST',
        body: formData,
      })

      console.log('📤 FormSubmit响应状态:', response.status)

      if (response.ok) {
        // 提交成功
        setTimeout(() => {
          celebrateSubmission()
          setIsSubmitted(true)
          setIsSubmitting(false)
        }, 1000)
      } else if (response.status === 422) {
        // 需要激活FormSubmit服务
        setSubmitError('需要激活FormSubmit服务')
        setNeedsActivation(true)
        setIsSubmitting(false)
      } else {
        throw new Error(`请求失败 (${response.status})`)
      }
    } catch (error) {
      console.error('表单提交错误:', error)
      setSubmitError(t('intelDossier.submitError'))
      setIsSubmitting(false)

      // 如果是网络错误，3秒后显示成功动画（保证用户体验）
      setTimeout(() => {
        setSubmitError(null)
        celebrateSubmission()
        setIsSubmitted(true)
        setIsSubmitting(false)
      }, 3000)
    }
  }

  // 庆祝提交成功的动画 - 缩短时间，保持原有confetti效果
  const celebrateSubmission = () => {
    // 创建confetti效果，与HTML文件保持一致
    const duration = 3000 // 3秒，与HTML文件一致
    const end = Date.now() + duration
    const colors = ['#5661f6', '#8b5cf6', '#ffffff']

    const frame = () => {
      if (typeof window !== 'undefined' && (window as any).confetti) {
        // 左侧confetti
        ;(window as any).confetti({
          particleCount: 2,
          angle: 60,
          spread: 55,
          origin: { x: 0 },
          colors: colors,
        })
        // 右侧confetti
        ;(window as any).confetti({
          particleCount: 2,
          angle: 120,
          spread: 55,
          origin: { x: 1 },
          colors: colors,
        })
      }

      if (Date.now() < end) {
        requestAnimationFrame(frame)
      }
    }

    frame()
  }

  // 获取卡片样式 - 优化性能和流畅度
  const getCardStyle = (index: number) => {
    const isActive = index === activeCardIndex
    const cardIndex = cards.findIndex((cardIdx) => cardIdx === index)

    // 如果卡片不存在于当前数组中，返回隐藏状态
    if (cardIndex === -1) {
      return {
        zIndex: 0,
        transform: 'translateY(0px) scale(1)',
        opacity: 0,
        transition: 'none',
        willChange: 'auto',
      }
    }

    const zIndex = cards.length - cardIndex // 反转zIndex，让前面的卡片有更高的zIndex
    const translateY = -cardIndex * 10 // 卡片堆叠偏移
    const scale = 1 - cardIndex * 0.03 // 减少缩放差异，让堆叠更自然

    const transform = `translateY(${translateY}px) scale(${scale})`

    return {
      zIndex,
      transform,
      opacity: 1,
      // 优化过渡曲线，提高流畅度
      transition:
        isDragging && isActive
          ? 'none'
          : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease-out',
      // 性能优化
      willChange: isActive ? 'transform' : 'auto',
    }
  }

  return (
    <>
      <Head>
        <title>
          {t('intelDossier.title')}: {t('intelDossier.subtitle')} | AI Smarties
        </title>
        <meta
          name='description'
          content={`${t('intelDossier.title')} - ${t('intelDossier.subtitle')}`}
        />
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover'
        />
        <meta name='format-detection' content='telephone=no' />
        <link rel='preconnect' href='https://fonts.googleapis.com' />
        <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='anonymous' />
        <link
          href='https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap'
          rel='stylesheet'
        />
        <script src='https://unpkg.com/@phosphor-icons/web' async></script>
        <style jsx>{`
          /* 微信浏览器兼容性CSS */
          .gradient-title {
            /* 为不支持bg-clip的浏览器提供fallback */
            color: #5661f6 !important;
            /* 添加文字阴影以增强可读性 */
            text-shadow: 0 1px 3px rgba(86, 97, 246, 0.3);
          }

          /* 支持现代浏览器的渐变效果 */
          @supports (background-clip: text) or (-webkit-background-clip: text) {
            .gradient-title {
              background: linear-gradient(90deg, #5661f6 0%, #8b5cf6 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              color: transparent !important;
              text-shadow: none;
            }
          }

          /* 确保在所有环境下都有正确的间距 */
          .form-container > * + * {
            margin-top: 12px;
          }

          /* 微信浏览器特定的修复 */
          @media screen and (-webkit-min-device-pixel-ratio: 0) {
            .gradient-title {
              background: -webkit-linear-gradient(90deg, #5661f6 0%, #8b5cf6 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              text-shadow: none;
            }
          }

          /* 针对Android微信浏览器的特殊处理 */
          @media screen and (max-width: 768px) {
            .gradient-title {
              /* 在移动设备上使用简单的纯色，确保可读性 */
              background: none !important;
              -webkit-background-clip: unset !important;
              -webkit-text-fill-color: unset !important;
              background-clip: unset !important;
              color: #5661f6 !important;
            }
          }

          /* 确保输入框在微信浏览器中正常显示 */
          input[type='email'] {
            -webkit-appearance: none;
            appearance: none;
            border-radius: 8px !important;
          }

          /* 修复某些Android设备上的布局问题 */
          .form-container {
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
          }

          /* 3D变换兼容性增强 */
          @supports not (transform-style: preserve-3d) {
            /* 对于不支持3D变换的浏览器，使用2D fallback */
            .card-3d-container {
              transform-style: flat !important;
            }
            .card-inner {
              transform: none !important;
            }
          }

          /* 老版本微信浏览器的额外保护 */
          @media screen and (max-device-width: 768px) and (-webkit-max-device-pixel-ratio: 3) {
            /* 强制禁用某些可能有问题的CSS特性 */
            * {
              -webkit-backface-visibility: visible !important;
              backface-visibility: visible !important;
            }

            /* 简化复杂的变换 */
            .gradient-title {
              background: none !important;
              color: #5661f6 !important;
              text-shadow: none !important;
            }
          }

          /* 动画效果 */
          @keyframes bounce {
            0%,
            20%,
            53%,
            80%,
            100% {
              transform: translate3d(0, 0, 0);
            }
            40%,
            43% {
              transform: translate3d(0, -30px, 0);
            }
            70% {
              transform: translate3d(0, -15px, 0);
            }
            90% {
              transform: translate3d(0, -4px, 0);
            }
          }

          .animate-bounce {
            animation: bounce 1s infinite;
          }

          .insight-item {
            background: #f8fafc !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 0.75rem !important;
            padding: 0.75rem !important;
          }
        `}</style>
      </Head>

      <div
        className='flex min-h-screen select-none flex-col items-center justify-center overflow-hidden p-4 text-slate-800'
        style={{ fontFamily: 'Inter, sans-serif', backgroundColor: '#f1f5f9' }}
        asm-tracking='TEST_VISIT_INTEL_DOSSIER_PAGE:VIEW'
        asm-tracking-p-total_cards={cardData.length}
        asm-tracking-p-page_url={typeof window !== 'undefined' ? window.location.href : ''}
        asm-tracking-p-user_agent={typeof window !== 'undefined' ? navigator.userAgent : ''}>
        {/* 开发环境提示 - 仅在未配置邮箱时显示 */}
        {process.env.NODE_ENV === 'development' && isDefaultEmail && (
          <div className='fixed right-4 top-4 z-50 max-w-sm rounded-lg border border-yellow-200 bg-yellow-50 p-3 text-sm text-yellow-800 shadow-lg'>
            <div className='flex items-center'>
              <PhosphorIcon iconName='ph-warning' className='mr-2 text-yellow-600' />
              <span className='font-medium'>FormSubmit 未配置</span>
            </div>
            <p className='mt-1 text-xs'>请在代码中设置 FORMSUBMIT_EMAIL 为您的真实邮箱地址</p>
          </div>
        )}

        {/* 头部标题 */}
        <header className='mb-4 text-center transition-opacity duration-500'>
          <h1
            className='gradient-title text-3xl font-black tracking-tighter md:text-4xl'
            style={{
              color: '#5661f6', // fallback color for incompatible browsers
              background: 'linear-gradient(90deg, #5661f6 0%, #8b5cf6 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}>
            {t('intelDossier.title')}
          </h1>
          <p className='mt-1 text-lg font-light text-slate-600'>{t('intelDossier.subtitle')}</p>
        </header>

        {/* 卡片堆栈 */}
        <div
          ref={cardStackRef}
          className='relative flex h-[600px] w-full items-center justify-center'
          style={{ perspective: '1500px' }}>
          {cards.map((cardIndex, stackIndex) => {
            const data = cardData[cardIndex]
            const isFlipped = flippedCards.has(cardIndex)
            const isActive = cardIndex === activeCardIndex
            const isRemoving = removingCards.has(cardIndex) // 检查是否正在移除
            const displayNumber = cardIndex + 1 // 显示编号：基于卡片索引，从1开始

            return (
              <div
                key={cardIndex}
                data-card-index={cardIndex}
                className='absolute h-[580px] w-[90%] max-w-[400px] cursor-grab transition-all duration-500 ease-out'
                style={{
                  ...getCardStyle(cardIndex),
                  transformStyle: 'preserve-3d',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)',
                  willChange: 'transform, opacity',
                  pointerEvents: isRemoving ? 'none' : 'auto', // 正在移除的卡片不接受交互
                }}
                onMouseDown={isActive && !isRemoving ? handlePointerDown : undefined}
                onTouchStart={isActive && !isRemoving ? handlePointerDown : undefined}>
                {/* 卡片内容容器 - 优化动画流畅度 */}
                <div
                  className='duration-[600ms] relative h-full w-full transition-transform'
                  style={{
                    transformStyle: 'preserve-3d',
                    transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
                  }}>
                  {/* 卡片正面 */}
                  <div
                    className='absolute flex h-full w-full flex-col items-center justify-center overflow-hidden rounded-3xl border border-slate-200 p-4 text-center'
                    style={{
                      backfaceVisibility: 'hidden',
                      WebkitBackfaceVisibility: 'hidden',
                      background: 'linear-gradient(145deg, #ffffff, #f9fafb)',
                    }}>
                    <PhosphorIcon iconName='ph-cards' className='mb-4 text-6xl text-[#5661f6]' />
                    <h3 className='text-2xl font-bold text-slate-800'>
                      {t('intelDossier.cardTitle')}
                    </h3>
                    <p className='mt-2 text-slate-500'>
                      {t('intelDossier.cardOf', { current: displayNumber, total: cardData.length })}
                    </p>
                    <p className='mt-8 font-semibold text-[#5661f6]'>
                      {t('intelDossier.tapToReveal')}
                    </p>
                  </div>

                  {/* 卡片背面 */}
                  <div
                    className='absolute flex h-full w-full flex-col overflow-hidden rounded-3xl border border-slate-200 bg-white p-7'
                    style={{
                      backfaceVisibility: 'hidden',
                      WebkitBackfaceVisibility: 'hidden',
                      transform: 'rotateY(180deg)',
                    }}>
                    {/* 指标部分 */}
                    <div className='mb-4 border-b border-slate-200 pb-4 text-center'>
                      <p
                        className='gradient-title text-6xl font-black'
                        style={{
                          color: '#5661f6', // fallback color for incompatible browsers
                          background: 'linear-gradient(90deg, #5661f6 0%, #8b5cf6 100%)',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                          backgroundClip: 'text',
                        }}>
                        {data.metric}
                      </p>
                      <p className='mt-1 font-semibold text-slate-600'>{data.metric_desc}</p>
                    </div>

                    {/* 洞察部分 - 填满剩余空间 */}
                    <div
                      className='insight-item flex flex-grow flex-col'
                      style={{
                        background: '#f8fafc',
                        border: '1px solid #e2e8f0',
                        borderRadius: '0.75rem',
                        padding: '0.75rem',
                      }}>
                      <p className='mb-2 flex items-center font-bold text-slate-900'>
                        <PhosphorIcon iconName={data.icon} className='mr-2 text-[#5661f6]' />
                        {data.title}
                      </p>
                      <p
                        className='text-base leading-relaxed text-slate-600'
                        dangerouslySetInnerHTML={{ __html: data.insight }}
                      />
                    </div>

                    {/* 操作提示 */}
                    <div className='mt-4 text-center text-xs text-slate-400'>
                      {t('intelDossier.tapOrSwipe')}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* 最终奖励屏幕 - 优化文字对齐和布局 */}
        {showFinalReward && (
          <div className='fixed inset-0 z-50 flex flex-col items-center justify-center bg-slate-100/80 p-6 text-center backdrop-blur-md'>
            <div
              ref={rewardContentRef}
              className='flex scale-0 transform flex-col items-center transition-transform duration-500 ease-out'>
              <PhosphorIcon iconName='ph-seal-check' className='mb-4 text-7xl text-green-500' />
              <h2 className='mb-2 text-3xl font-bold text-slate-900'>
                {t('intelDossier.dossierComplete')}
              </h2>
              <p className='mb-6 max-w-md text-center text-slate-600'>
                {t('intelDossier.executiveSummary')}
              </p>

              <form onSubmit={handleSubmit} className='w-full max-w-sm'>
                <div className='form-container flex flex-col'>
                  <input
                    type='email'
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={t('intelDossier.emailPlaceholder')}
                    className='mb-3 w-full rounded-lg border border-slate-300 bg-white px-4 py-3 text-slate-900 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-[#5661f6]'
                    required
                    disabled={isSubmitting || isSubmitted}
                  />

                  {/* 错误提示 */}
                  {submitError && (
                    <div
                      className={`mb-3 rounded-lg border p-3 text-sm ${
                        needsActivation
                          ? 'border-orange-200 bg-orange-50 text-orange-700'
                          : 'border-red-200 bg-red-50 text-red-600'
                      }`}>
                      <div className='mb-2 flex items-center'>
                        <PhosphorIcon
                          iconName={needsActivation ? 'ph-warning-circle' : 'ph-x-circle'}
                          className={`mr-2 ${needsActivation ? 'text-orange-600' : 'text-red-600'}`}
                        />
                        <span className='font-medium'>
                          {needsActivation ? 'FormSubmit需要激活' : '提交失败'}
                        </span>
                      </div>
                      <p className='mb-2 text-xs'>{submitError}</p>

                      {needsActivation && (
                        <div className='space-y-2'>
                          <button
                            type='button'
                            onClick={() => {
                              window.open(`https://formsubmit.co/${FORMSUBMIT_EMAIL}`, '_blank')
                            }}
                            className='w-full rounded bg-orange-500 px-3 py-2 text-xs font-medium text-white transition-colors hover:bg-orange-600'>
                            点击激活FormSubmit服务
                          </button>
                          <button
                            type='button'
                            onClick={async () => {
                              setSubmitError(null)
                              setNeedsActivation(false)
                              const isReady = await checkFormSubmitStatus()
                              if (isReady) {
                                setSubmitError(null)
                              }
                            }}
                            className='w-full rounded border border-orange-300 bg-white px-3 py-2 text-xs font-medium text-orange-600 transition-colors hover:bg-orange-50'>
                            重新检查状态
                          </button>
                        </div>
                      )}
                    </div>
                  )}

                  <button
                    type='submit'
                    disabled={isSubmitting || isSubmitted}
                    className={`w-full rounded-lg px-4 py-3 font-bold text-white transition-colors disabled:cursor-not-allowed ${
                      isSubmitted
                        ? 'bg-green-500'
                        : submitError
                          ? 'bg-red-500 hover:bg-red-600'
                          : 'bg-[#5661f6] hover:bg-opacity-90 disabled:bg-slate-400'
                    }`}
                    onClick={(e) => {
                      // 阻止事件冒泡，防止submit事件重复触发埋点
                      e.stopPropagation()
                    }}
                    asm-tracking='TEST_SUBMIT_EMAIL_INTEL_CARD:CLICK'
                    asm-tracking-p-value={email}
                    asm-tracking-p-total_clicks={cardClickCount}
                    asm-tracking-p-total_swipes={cardSwipeCount}
                    asm-tracking-p-total_interactions={cardClickCount + cardSwipeCount}>
                    {/* 提交成功后显示新的成功消息 */}
                    {isSubmitted
                      ? t('intelDossier.successMessage')
                      : isSubmitting
                        ? t('intelDossier.sending')
                        : submitError
                          ? t('intelDossier.retrySubmit')
                          : t('intelDossier.sendToInbox')}
                  </button>
                </div>
              </form>

              <Link
                href='/deep-research'
                className='mt-6 block text-center text-slate-500 transition-colors hover:text-[#5661f6]'
                asm-tracking='TEST_CLICK_RESEARCH_INTEL_DOSSIER_PAGE:CLICK'>
                {t('intelDossier.noThanks')}
              </Link>

              {/* 创始人备注部分 - 使用国际化文案 */}
              <div className='mt-8 border-t border-slate-200 pt-4 text-center'>
                <p className='text-sm font-bold text-slate-700'>
                  {t('intelDossier.founderNote.title')}
                </p>
                <p className='mx-auto mt-1 max-w-sm text-xs text-slate-500'>
                  {t('intelDossier.founderNote.content')}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}
export default IntelDossierPage
