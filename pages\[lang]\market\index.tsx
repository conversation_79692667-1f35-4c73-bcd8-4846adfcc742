import React from 'react'
import { GetStaticPaths, GetStaticProps } from 'next'
import Link from 'next/link'
import CommonLayout from '@/components/seo/CommonLayout'
import { Language } from '@/types/market'

const SUPPORTED_LANGUAGES: Language[] = ['zh-CN', 'en', 'ja', 'de']

const MarketIndexPage = ({ lang }: { lang: Language }) => {
  return (
    <CommonLayout>
      <div className='rounded-lg bg-white p-6 shadow-sm'>
        <h1 className='mb-6 text-3xl font-bold'>Market Analysis</h1>
        <div className='space-y-4'>
          <Link
            href={`/${lang}/market/ai-market`}
            className='block rounded-lg border border-gray-200 p-4 transition-colors duration-200 hover:border-indigo-300'>
            <h2 className='text-xl font-semibold text-gray-800'>AI Market Analysis</h2>
            <p className='mt-2 text-gray-600'>
              Comprehensive analysis of the artificial intelligence market trends and opportunities.
            </p>
          </Link>
          <Link
            href={`/${lang}/market/web3-market`}
            className='block rounded-lg border border-gray-200 p-4 transition-colors duration-200 hover:border-indigo-300'>
            <h2 className='text-xl font-semibold text-gray-800'>Web3 Market Analysis</h2>
            <p className='mt-2 text-gray-600'>
              In-depth exploration of the Web3 ecosystem and market developments.
            </p>
          </Link>
        </div>
      </div>
    </CommonLayout>
  )
}

export const getStaticPaths: GetStaticPaths = async () => {
  const paths = SUPPORTED_LANGUAGES.map((lang) => ({
    params: { lang },
  }))

  return {
    paths,
    fallback: false,
  }
}

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const lang = params?.lang as Language

  if (!SUPPORTED_LANGUAGES.includes(lang)) {
    return {
      notFound: true,
    }
  }

  return {
    props: {
      lang,
    },
  }
}

export default MarketIndexPage
