import React from 'react'

interface RegisterGuideProps {
  className?: string
}

const RegisterGuide: React.FC<RegisterGuideProps> = ({ className = '' }) => {
  return (
    <div
      suppressHydrationWarning
      className={`my-6 rounded-lg bg-gradient-to-r from-indigo-50 to-blue-50 p-4 shadow-sm ${className}`}>
      <div className='flex items-center justify-between'>
        <div className='flex-1'>
          <h3 className='text-lg font-semibold text-indigo-900'>Unlock Deep Market Insights</h3>
          <p className='mt-1 text-sm text-indigo-700'>
            Create your custom competitor analysis and market research with AI
          </p>
        </div>
        {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
        <a
          asm-tracking='CLICK_SEO_SIGNUP:CLICK'
          asm-tracking-p-view='DESKTOP'
          asm-tracking-p-position='Register Guide'
          href='https://ai-smarties.com/deep-research'
          className='ml-4 rounded-md bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'>
          Start Deep Research Now
        </a>
      </div>
    </div>
  )
}

export default RegisterGuide
