import Segmented from '@/components/common/Segmented'
import BaseLayout from '@/components/layouts/BaseLayout'
import { motion } from 'framer-motion'
import Head from 'next/head'
import Link from 'next/link'
import Image from 'next/image'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

// 价格功能项组件
interface PriceFeatureProps {
  text: string
  available: boolean
}

const PriceFeature: React.FC<PriceFeatureProps> = ({ text, available }) => {
  return (
    <div className='mb-3 flex items-start space-x-3'>
      <div className={`mt-0.5 flex-shrink-0 ${available ? 'text-primary' : 'text-gray-400'}`}>
        <svg
          className='h-5 w-5'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
          xmlns='http://www.w3.org/2000/svg'>
          <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d={'M5 13l4 4L19 7'} />
        </svg>
      </div>
      <p className={`text-sm ${available ? 'text-gray-700' : 'text-gray-400'}`}>{text}</p>
    </div>
  )
}

// 价格卡片组件
interface PriceCardProps {
  id: string
  title: string
  price: number | null
  period: string
  features: { subTitle: string; items: { text: string; available: boolean }[] }[]
  buttonText: string
  buttonLink: string
  highlight?: boolean
}

const PriceCard: React.FC<PriceCardProps> = ({
  id,
  title,
  price,
  period,
  features,
  buttonText,
  buttonLink,
  highlight = false,
}) => {
  const { t } = useTranslation(Namespace.PRICING)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      className={`rounded-xl border bg-white ${
        highlight ? 'border-primary/30 shadow-md' : 'border-gray-200 shadow-sm'
      } relative flex h-full flex-col p-8 transition-all duration-300 hover:shadow-lg`}>
      {id !== 'free' && id !== 'report' && period === 'year' && (
        <div className='absolute -top-3 right-8 rounded-full bg-primary px-3 py-1 text-xs font-medium text-white shadow-sm'>
          {`${t('card.save')} 20%`}
        </div>
      )}
      {/* 标题区域 */}
      <h3 className='mb-4 text-start text-xl font-bold text-gray-900'>{title}</h3>

      {/* 价格区域 */}
      <div className='mb-6'>
        <div className='flex items-end'>
          {id === 'report' ? (
            <span className='mb-1 mr-1 text-base font-normal text-gray-500'>
              {t('card.report.startingAt')}
            </span>
          ) : null}
          <span className='text-4xl font-bold text-primary'>
            {price ? `$${price}` : t('card.free.title')}
          </span>
          <span className='mb-1 ml-1 text-gray-500'>
            {`${
              id === 'report'
                ? `/${t('card.unit.report')}`
                : price
                  ? `/${period === 'month' ? t('card.unit.month') : t('card.unit.year')}`
                  : ''
            }`}
          </span>
        </div>
      </div>

      {/* 按钮区域 */}
      <Link
        href={buttonLink}
        className={`block w-full rounded-lg bg-primary px-4 py-3 text-center font-medium text-white transition-all duration-300 hover:bg-primary-dark hover:shadow`}>
        {buttonText}
      </Link>

      {/* 分隔线 */}
      <div className={`h-px w-full ${'bg-gray-200'} my-6`}></div>

      {/* 功能清单区域 */}
      <div className='mb-8 text-start'>
        {features.map((feature) => (
          <div key={feature.subTitle}>
            <h2 className='mb-2 font-bold'>{feature.subTitle}</h2>
            {feature.items.map((item, index) => (
              <PriceFeature
                key={item.text.slice(0, 10) + index}
                text={item.text}
                available={item.available}
              />
            ))}
          </div>
        ))}
      </div>
    </motion.div>
  )
}

const PricingPage: React.FC = () => {
  const [selectedType, setSelectedType] = useState<string>('Month')
  const { t } = useTranslation(Namespace.PRICING)

  const priceData = [
    {
      id: 'free',
      title: t('card.free.title'),
      price: 0,
      features: [
        {
          subTitle: t('card.free.feature1.subTitle1'),
          items: [{ text: t('card.free.feature1.item1'), available: true }],
        },
        {
          subTitle: t('card.free.feature2.subTitle2'),
          items: [
            { text: t('card.free.feature2.item1'), available: true },
            { text: t('card.free.feature2.item2'), available: true },
            { text: t('card.free.feature2.item3'), available: true },
            { text: t('card.free.feature2.item4'), available: true },
          ],
        },
        {
          subTitle: t('card.free.feature3.subTitle3'),
          items: [
            { text: t('card.free.feature3.item1'), available: true },
            { text: t('card.free.feature3.item2'), available: true },
          ],
        },
      ],
      buttonText: t('card.buttonText'),
      buttonLink: 'https://www.portal.ai-smarties.com/signup',
    },
    {
      id: 'basic',
      title: t('card.basic.title'),
      price: 18,
      features: [
        {
          subTitle: t('card.basic.feature1.subTitle1'),
          items: [
            selectedType === 'Month'
              ? { text: t('card.basic.feature1.item2'), available: true }
              : { text: t('card.basic.feature1.item1'), available: true },
          ],
        },
        {
          subTitle: t('card.basic.feature2.subTitle2'),
          items: [
            { text: t('card.basic.feature2.item1'), available: true },
            { text: t('card.basic.feature2.item2'), available: true },
            { text: t('card.basic.feature2.item3'), available: true },
            { text: t('card.basic.feature2.item4'), available: true },
          ],
        },
        {
          subTitle: t('card.basic.feature3.subTitle3'),
          items: [
            { text: t('card.basic.feature3.item1'), available: true },
            { text: t('card.basic.feature3.item2'), available: true },
          ],
        },
      ],
      buttonText: t('card.buttonText'),
      buttonLink: 'https://www.portal.ai-smarties.com/signup',
    },
    {
      id: 'professional',
      title: t('card.professional.title'),
      price: 35,
      features: [
        {
          subTitle: t('card.professional.feature1.subTitle1'),
          items: [{ text: t('card.professional.feature1.item1'), available: true }],
        },
        {
          subTitle: t('card.professional.feature2.subTitle2'),
          items: [
            { text: t('card.professional.feature2.item1'), available: true },
            { text: t('card.professional.feature2.item2'), available: true },
            { text: t('card.professional.feature2.item3'), available: true },
            { text: t('card.professional.feature2.item4'), available: true },
          ],
        },
        {
          subTitle: t('card.professional.feature3.subTitle3'),
          items: [
            { text: t('card.professional.feature3.item1'), available: true },
            { text: t('card.professional.feature3.item2'), available: true },
            { text: t('card.professional.feature3.item3'), available: false },
          ],
        },
        {
          subTitle: t('card.professional.feature4.subTitle4'),
          items: [{ text: t('card.professional.feature4.item1'), available: true }],
        },
      ],
      buttonText: t('card.buttonText'),
      buttonLink: 'https://www.portal.ai-smarties.com/signup',
    },
    {
      id: 'report',
      title: t('card.report.title'),
      price: 75,
      features: [
        {
          subTitle: t('card.report.feature1.subTitle1'),
          items: [
            { text: t('card.report.feature1.item1'), available: true },
            { text: t('card.report.feature1.item2'), available: true },
            { text: t('card.report.feature1.item3'), available: true },
            { text: t('card.report.feature1.item4'), available: true },
            { text: t('card.report.feature1.item5'), available: true },
          ],
        },
      ],
      buttonText: t('card.buttonText'),
      buttonLink: 'https://www.portal.ai-smarties.com/signup',
    },
  ]

  return (
    <>
      <Head>
        <title>Pricing | AI-Smarties</title>
        <meta
          name='description'
          content='Flexible pricing plans for AI-Smarties, your AI-powered market research assistant. Choose from monthly, annual, or custom business reports.'
        />
        <meta property='og:title' content='Pricing | AI-Smarties' />
        <meta
          property='og:description'
          content='Find the perfect plan for your business needs with our flexible pricing options.'
        />
        <meta property='og:type' content='website' />
      </Head>

      {/* Hero Section */}
      <section className='bg-white py-16'>
        <div className='mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8'>
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className='mb-4 text-4xl font-bold text-gray-900'>
            {t('title')}
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className='mb-12 text-lg text-primary'>
            {t('aiSmartiesPricing')}
          </motion.p>

          <div className='relative'>
            <Segmented
              options={[
                { name: t('switchBtnText.month'), value: 'Month' },
                { name: t('switchBtnText.year'), value: 'Year' },
              ]}
              onChange={(value) => {
                setSelectedType(value)
              }}
            />
            <Image
              src='/images/save.svg'
              alt='no data'
              width={134}
              height={65}
              className={`absolute bottom-[8px] left-1/2 translate-x-8 md:translate-x-12 ${selectedType === 'Month' ? 'visible' : 'invisible'}`}
            />
          </div>

          {/* Pricing Cards */}
          <div className='mx-auto mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-4'>
            {priceData.map((item) => (
              <PriceCard
                key={item.id}
                id={item.id}
                title={item.title}
                price={
                  item.id === 'free'
                    ? null
                    : selectedType === 'Month' || item.id === 'report'
                      ? item.price
                      : item.price * 10
                }
                period={
                  item.id === 'report'
                    ? 'report'
                    : item.price > 0
                      ? selectedType === 'Month'
                        ? 'month'
                        : 'year'
                      : ''
                }
                features={item.features}
                buttonText={item.buttonText}
                buttonLink={item.buttonLink}
              />
            ))}
          </div>
        </div>
      </section>
    </>
  )
}

export default PricingPage
