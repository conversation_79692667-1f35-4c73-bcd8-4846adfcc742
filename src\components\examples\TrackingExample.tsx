/* eslint-disable react/no-unknown-property */
import React, { useState } from 'react'

/**
 * This component demonstrates how to use the declarative tracking system
 * with various tracking attributes.
 */
const TrackingExample: React.FC = () => {
  const [inputValue, setInputValue] = useState('')

  // Example of a custom tracking method that can be referenced by asm-tracking-method
  // This function needs to be attached to the window object to be accessible
  React.useEffect(() => {
    // Define a custom tracking method that can be referenced by asm-tracking-method
    ;(window as any).customTrackingMethod = (defaultProps: any) => {
      // Extend or override default properties
      return {
        ...defaultProps,
        custom_property: 'custom value',
        timestamp: new Date().toISOString(),
      }
    }
  }, [])

  return (
    <div className='rounded-lg bg-white p-6 shadow-md'>
      <h2 className='mb-4 text-2xl font-bold'>Tracking Examples</h2>

      {/* Example of VIEW tracking */}

      <div
        className='mb-6 rounded-md bg-indigo-50 p-4'
        asm-tracking='DEMO_COMPONENT_VIEW:VIEW'
        asm-tracking-p-component='TrackingExample'
        asm-tracking-p-section='Demo'>
        <p className='text-sm text-gray-600'>
          This div has VIEW tracking. It will trigger a tracking event when rendered.
        </p>
      </div>

      {/* Example of CLICK tracking */}
      <button
        className='mb-4 rounded-md bg-primary px-4 py-2 text-white hover:bg-indigo-700'
        asm-tracking='DEMO_BUTTON_CLICK:CLICK'
        asm-tracking-p-button_name='primary_cta'
        asm-tracking-p-button_text='Click Me'>
        Click Me (tracked)
      </button>

      {/* Example of HOVER tracking */}
      <div
        className='mb-6 rounded-md bg-yellow-50 p-4 transition-colors hover:bg-yellow-100'
        asm-tracking='DEMO_ELEMENT_HOVER:HOVER'
        asm-tracking-p-element_type='info_box'>
        <p className='text-sm text-gray-600'>
          Hover over this element to trigger a tracking event.
        </p>
      </div>

      {/* Example of FOCUS and BLUR tracking */}
      <div className='mb-6'>
        <label className='mb-1 block text-sm font-medium text-gray-700'>
          Input with focus/blur tracking
        </label>
        <input
          type='text'
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          className='w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500'
          placeholder='Focus on this input'
          asm-tracking='DEMO_INPUT_FOCUS:FOCUS'
          asm-tracking-p-input_name='demo_input'
        />
      </div>

      {/* Example of SUBMIT tracking */}
      <form
        className='mb-6 rounded-md bg-green-50 p-4'
        asm-tracking='DEMO_FORM_SUBMIT:SUBMIT'
        asm-tracking-p-form_name='demo_form'
        onSubmit={(e) => {
          e.preventDefault()
          alert('Form submitted!')
        }}>
        <div className='mb-4'>
          <label className='mb-1 block text-sm font-medium text-gray-700'>Name</label>
          <input
            type='text'
            className='w-full rounded-md border border-gray-300 px-3 py-2'
            placeholder='Your name'
          />
        </div>
        <button
          type='submit'
          className='rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700'>
          Submit Form (tracked)
        </button>
      </form>

      {/* Example of custom method tracking */}
      <button
        className='rounded-md bg-purple-600 px-4 py-2 text-white hover:bg-purple-700'
        asm-tracking='DEMO_CUSTOM_METHOD:CLICK'
        asm-tracking-p-button_name='custom_method_button'
        asm-tracking-method='customTrackingMethod'>
        Custom Method Tracking
      </button>

      <div className='mt-8 rounded-md border border-gray-200 bg-gray-50 p-4'>
        <h3 className='mb-2 text-lg font-semibold'>How to Use Tracking Attributes</h3>
        <ul className='list-disc space-y-2 pl-5 text-sm'>
          {/*<li><code className="bg-gray-100 px-1 rounded">asm-tracking="EVENT_NAME:EVENT_TYPE"</code> - Define the event name and type</li>*/}
          {/*<li><code className="bg-gray-100 px-1 rounded">asm-tracking-p-param_name="value"</code> - Add custom parameters</li>*/}
          {/*<li><code className="bg-gray-100 px-1 rounded">asm-tracking-method="functionName"</code> - Use a custom method to override parameters</li>*/}
        </ul>
      </div>
    </div>
  )
}

export default TrackingExample
