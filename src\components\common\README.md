# Markdown 渲染组件

这个组件提供了一种统一的方式来渲染 Markdown 内容，包括普通渲染和流式渲染。

## 组件结构

系统包含三个主要组件：

1. **MarkdownRenderer** - 基础渲染组件
2. **MarkdownComponents** - 统一的标签样式定义
3. **SeoMarkdownRenderer** 和 **ResearchMarkdownRenderer** - 特定用途的渲染器

## 如何使用

### 基本用法

最简单的方式是使用特定用途的渲染器：

```tsx
// SEO页面
import SeoMarkdownRenderer from '@/components/seo/SeoMarkdownRenderer'

<SeoMarkdownRenderer 
  content={markdownContent} 
  citations={citations} 
  guideConfig={['1', '3']} 
/>

// 研究结果页面
import ResearchMarkdownRenderer from '@/components/common/ResearchMarkdownRenderer'

<ResearchMarkdownRenderer 
  content={markdownContent} 
  role="researcher" 
  onCopy={handleCopy} 
/>
```

### 自定义用法

如果需要更多定制，可以直接使用基础组件和统一的标签定义：

```tsx
import MarkdownRenderer from '@/components/common/MarkdownRenderer'
import createMarkdownComponents from '@/components/common/MarkdownComponents'

// 创建自定义组件
const customComponents = createMarkdownComponents({
  citations: myCitations,
  trackingPage: 'MY_CUSTOM_PAGE',
  guideRegisterFn: (num) => {
    // 自定义引导注册逻辑
    return showGuideForSection(num) ? <MyCustomGuide /> : null
  }
})

// 使用
<MarkdownRenderer
  content={markdownContent}
  isStreaming={isStreamMode}
  className="my-custom-class"
  components={customComponents}
/>
```

### 流式渲染

对于流式内容（如AI生成的实时内容），设置 `isStreaming` 为 `true`：

```tsx
<MarkdownRenderer
  content={streamingContent}
  isStreaming={true}
  components={components}
/>
```

## 统一样式维护

所有Markdown标签的样式定义都集中在 `MarkdownComponents.tsx` 文件中。如果需要修改样式，只需更改这个文件，所有使用该组件的地方都会自动更新。

## 类型定义

系统提供了完整的TypeScript类型定义：

- `ComponentProps` - 标签组件的属性类型
- `Citations` - 引用数据的类型
- `MarkdownRendererProps` - 渲染器属性的类型

## 推荐实践

1. 尽量使用已有的特定用途渲染器
2. 需要自定义时，使用 `createMarkdownComponents` 创建组件
3. 对样式的修改应该集中在 `MarkdownComponents.tsx` 文件中

## Mermaid 图表支持

Markdown 组件系统现在支持 Mermaid 图表的渲染。您可以在 Markdown 内容中使用 Mermaid 语法创建各种图表，如流程图、时序图、甘特图和饼图等。

### 使用方法

在 Markdown 内容中使用 "```mermaid" 代码块来定义 Mermaid 图表：

````markdown
```mermaid
pie
    title 行业收入占比
    "金融" : 52
    "零售" : 24
    "制造" : 15
    "其他" : 9
```
````

### 支持的图表类型

* 流程图 (Flowchart)
* 时序图 (Sequence Diagram)
* 类图 (Class Diagram)
* 状态图 (State Diagram)
* 甘特图 (Gantt Chart)
* 饼图 (Pie Chart)
* 关系图 (Entity Relationship Diagram)
* 用户旅程图 (User Journey)

### 示例

**饼图示例**:

````markdown
```mermaid
pie
    title 行业收入占比
    "金融" : 52
    "零售" : 24
    "制造" : 15
    "其他" : 9
```
````

**流程图示例**:

````markdown
```mermaid
graph TD
    A[开始] --> B{是否有库存?}
    B -->|是| C[发货]
    B -->|否| D[补货]
    D --> B
    C --> E[结束]
```
````

**时序图示例**:

````markdown
```mermaid
sequenceDiagram
    participant 客户
    participant 系统
    participant 客服

    客户->>系统: 提交订单
    系统->>系统: 验证订单
    系统->>客户: 确认订单
    客户->>系统: 支付
    系统->>客服: 通知发货
    客服->>客户: 发送物流信息
```
````

### 注意事项

* Mermaid 图表在客户端浏览器中渲染
* 过于复杂的图表可能会影响页面加载性能
* 请确保 Mermaid 语法正确，错误的语法将导致图表渲染失败

更多关于 Mermaid 语法的信息，请参考 [Mermaid 官方文档](https://mermaid.js.org/intro/) 

# Markdown 组件系统与 Mermaid 图表支持

本文档详细介绍了我们的 Markdown 组件系统如何支持渲染 Mermaid 图表，包括架构设计、组件关系、集成方法等。

## 1. 组件架构与关系

我们的 Markdown 渲染系统由以下几个核心组件组成：

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│ MarkdownRenderer│────▶│ MarkdownComponents│────▶│ MermaidPrerender│
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                       │
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  SeoMarkdownRenderer│     │  ResearchMarkdownRenderer│     │  MermaidTest  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### 各组件职责

1. **MarkdownRenderer**：基础渲染组件，负责将 Markdown 文本转换为 HTML
2. **MarkdownComponents**：提供统一的 Markdown 标签样式定义和自定义组件
3. **MermaidPrerender**：专门处理 Mermaid 图表的渲染
4. **SeoMarkdownRenderer** 和 **ResearchMarkdownRenderer**：特定用途的渲染器，扩展基础功能
5. **MermaidTest**：用于测试 Mermaid 图表渲染功能的组件

## 2. 架构中各部分的侧重点和解决的问题

### MarkdownRenderer

- **侧重点**：提供基础的 Markdown 渲染功能
- **解决的问题**：
  - 将 Markdown 文本转换为 HTML
  - 支持流式渲染（用于实时内容）
  - 集成 rehype 和 remark 插件

### MarkdownComponents

- **侧重点**：提供统一的样式和自定义组件
- **解决的问题**：
  - 统一所有 Markdown 标签的样式
  - 提供自定义组件（如引用编号、注册引导等）
  - 识别并处理 Mermaid 代码块

### MermaidPrerender

- **侧重点**：专门处理 Mermaid 图表的渲染
- **解决的问题**：
  - 初始化 Mermaid 库
  - 预处理图表代码
  - 处理渲染错误
  - 提供错误回退机制

### 特定用途渲染器

- **侧重点**：针对特定场景扩展功能
- **解决的问题**：
  - SEO 页面：添加引用编号、注册引导等
  - 研究结果页面：支持流式渲染、基于角色的样式

## 3. 组件之间协作的关键代码

### MarkdownRenderer 与 MarkdownComponents 的协作

```tsx
// MarkdownRenderer 使用 MarkdownComponents 提供的组件
<ReactMarkdown
  className={className}
  rehypePlugins={rehypePlugins}
  remarkPlugins={remarkPlugins}
  components={components}>
  {displayContent}
</ReactMarkdown>
```

### MarkdownComponents 与 MermaidPrerender 的协作

```tsx
// 在 MarkdownComponents 中检测 Mermaid 代码块
if (language === 'mermaid') {
  // 使用 MermaidPrerender 组件进行渲染
  return (
    <MermaidPrerender
      id={Math.random().toString(36).substring(2, 11)}
      chart={code}
      onError={(error) => {
        console.error('Mermaid 图表渲染错误:', error)
      }}
    />
  )
}
```

## 4. Mermaid 组件的设计思路与关键逻辑

### 设计思路

1. **分离关注点**：将 Mermaid 渲染逻辑从 Markdown 渲染中分离出来
2. **错误处理**：提供完善的错误处理和回退机制
3. **预处理**：对图表代码进行预处理，提高渲染成功率
4. **全局初始化**：确保 Mermaid 库只初始化一次

### 关键逻辑

#### 初始化 Mermaid

```tsx
// 确保 mermaid 初始化只执行一次
if (!window.mermaidInitialized) {
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    securityLevel: 'loose',
    fontFamily: 'sans-serif',
    logLevel: 'error',
    // 各种图表类型的特定配置
    gantt: { /* ... */ },
    er: { /* ... */ },
    state: { /* ... */ },
  })
  window.mermaidInitialized = true
}
```

#### 图表类型检测

```tsx
// 检测图表类型的函数
const detectChartType = (code: string): string => {
  const cleanCode = code.trim().toLowerCase()
  if (cleanCode.startsWith('graph ') || cleanCode.startsWith('flowchart ')) return 'flowchart'
  if (cleanCode.startsWith('sequencediagram')) return 'sequence'
  if (cleanCode.startsWith('pie')) return 'pie'
  // ... 其他图表类型
  return 'unknown'
}
```

#### 错误处理与回退

```tsx
// 渲染失败时显示代码块
if (renderFailed) {
  return (
    <div className='mermaid-fallback'>
      <div className='mb-2 rounded border border-yellow-300 bg-yellow-50 p-4 text-sm text-yellow-700'>
        <p className='font-bold'>Mermaid 图表无法渲染</p>
        <p>{error}</p>
      </div>
      <pre className='overflow-auto rounded bg-gray-100 p-4'>
        <code className='text-sm'>{chartCode}</code>
      </pre>
    </div>
  )
}
```

## 5. 如何集成 Mermaid 组件

### 基本集成步骤

1. 安装依赖：
   ```bash
   npm install mermaid react-markdown remark-gfm rehype-raw
   ```

2. 导入组件：
   ```tsx
   import MarkdownRenderer from '@/components/common/MarkdownRenderer'
   import createMarkdownComponents from '@/components/common/MarkdownComponents'
   import MermaidPrerender from '@/components/common/MermaidPrerender'
   ```

3. 创建 Markdown 组件：
   ```tsx
   const components = createMarkdownComponents({
     disableMermaid: false, // 是否禁用 Mermaid 图表渲染
   })
   ```

4. 使用 MarkdownRenderer：
   ```tsx
   <MarkdownRenderer
     content={markdownContent}
     className='prose max-w-none'
     rehypePlugins={[rehypeRaw]}
     remarkPlugins={[remarkGfm]}
     components={components}
   />
   ```

### 参数说明

#### MarkdownRenderer 参数

- `content`: Markdown 内容字符串
- `isStreaming`: 是否为流式渲染（默认 false）
- `className`: 自定义样式类名
- `rehypePlugins`: rehype 插件列表
- `remarkPlugins`: remark 插件列表
- `components`: 自定义组件配置
- `streamDelay`: 流式渲染的延迟时间(ms)

#### createMarkdownComponents 参数

- `citations`: 引用数据
- `guideRegisterFn`: 引导注册函数
- `trackingPage`: 跟踪页面名称
- `disableMermaid`: 是否禁用 Mermaid 图表渲染

#### MermaidPrerender 参数

- `id`: 图表唯一标识符
- `chart`: Mermaid 图表代码
- `onError`: 错误处理回调函数

## 6. Mermaid 组件的渲染机制

1. **初始化阶段**：
   - 检查 Mermaid 是否已初始化
   - 配置 Mermaid 全局设置
   - 加载所有必要的 Mermaid 模块

2. **预处理阶段**：
   - 检测图表类型
   - 根据图表类型进行特定处理
   - 清理和规范化图表代码

3. **渲染阶段**：
   - 调用 Mermaid 的 render 方法生成 SVG
   - 处理渲染错误
   - 提供错误回退机制

4. **清理阶段**：
   - 移除错误提示
   - 清理 DOM 中的临时元素

## 7. markdown-test.tsx 的作用和使用方法

`markdown-test.tsx` 是一个测试页面，用于展示和测试 Markdown 渲染功能，特别是 Mermaid 图表支持。

### 主要功能

1. 展示市场分析报告示例
2. 展示各种类型的 Mermaid 图表
3. 提供内容切换功能

### 使用方法

1. 访问 `/markdown-test` 路由
2. 使用页面顶部的切换按钮在"市场分析报告"和"Mermaid 图表测试"之间切换
3. 查看渲染结果

### 代码示例

```tsx
// 使用统一的 Markdown 组件
const components = createMarkdownComponents()

// 渲染内容
<MarkdownRenderer
  content={contentType === 'market' ? marketAnalysisContent : mermaidTestContent}
  className='prose max-w-none'
  rehypePlugins={[rehypeRaw]}
  remarkPlugins={[remarkGfm]}
  components={components}
/>
```

## 8. 参考资料和其他建议

### 参考资料

- [Mermaid 官方文档](https://mermaid-js.github.io/mermaid/)
- [React Markdown 文档](https://github.com/remarkjs/react-markdown)
- [Remark GFM 插件](https://github.com/remarkjs/remark-gfm)
- [Rehype Raw 插件](https://github.com/rehypejs/rehype-raw)

### 其他建议

1. **性能优化**：
   - 考虑使用 React.memo 包装 MermaidPrerender 组件
   - 对于复杂的图表，考虑使用懒加载

2. **错误处理**：
   - 添加更详细的错误日志
   - 实现重试机制

3. **扩展功能**：
   - 支持更多图表类型
   - 添加图表交互功能
   - 支持图表主题切换

4. **测试**：
   - 添加单元测试和集成测试
   - 创建更多测试用例覆盖各种图表类型

5. **文档**：
   - 为每种图表类型提供示例
   - 创建故障排除指南

## 总结

我们的 Markdown 组件系统通过模块化设计和关注点分离，实现了对 Mermaid 图表的无缝支持。系统由基础渲染组件、统一样式定义、专用 Mermaid 渲染器和特定用途渲染器组成，各组件协同工作，提供了灵活、可扩展的 Markdown 渲染解决方案。

通过使用 `createMarkdownComponents` 函数，可以轻松创建自定义组件，并通过 `disableMermaid` 参数控制是否启用 Mermaid 图表渲染。对于需要更多定制的场景，可以直接使用 `MermaidPrerender` 组件进行渲染。 