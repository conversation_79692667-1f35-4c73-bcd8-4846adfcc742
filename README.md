# AISmarties Web SEO

AISmarties Web SEO 是一个基于 Next.js 构建的现代化 Web 应用，专注于 SEO 优化和用户行为分析。

## 项目功能

- **SEO 优化**：内置多种 SEO 优化策略，包括元标签管理、结构化数据、站点地图等
- **Markdown 渲染**：支持 Markdown 内容渲染，包括 Mermaid 图表
- **声明式埋点**：通过 HTML 属性实现自动用户行为追踪
- **多语言支持**：内置国际化支持
- **响应式设计**：基于 Tailwind CSS 的响应式 UI

## 技术栈

- **前端框架**：Next.js 14
- **UI 库**：React 18
- **样式解决方案**：Tailwind CSS
- **状态管理**：React Context
- **国际化**：react-i18next
- **分析工具**：Mixpanel
- **Markdown 处理**：react-markdown, mermaid
- **代码质量**：TypeScript, ESLint, Prettier

## 快速开始

### 环境要求

- Node.js 18+
- npm 或 pnpm

### 安装

```bash
# 克隆仓库
git clone https://github.com/JieAISmarties/AISmarties-web-seo.git

# 进入项目目录
cd AISmarties-web-seo

# 安装依赖
npm install
# 或
pnpm install
```

### 配置

1. 复制 `.env.example` 到 `.env` 并设置必要的环境变量
2. 配置 Mixpanel 令牌（用于用户行为分析）

### 开发

```bash
# 启动开发服务器
npm run dev
# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 项目结构

```
AISmarties-web-seo/
├── components/          # 组件目录
│   ├── common/          # 通用组件
│   ├── layouts/         # 布局组件
│   ├── seo/             # SEO 相关组件
│   ├── auth/            # 认证相关组件
│   └── examples/        # 示例组件
├── pages/               # 页面目录
├── public/              # 静态资源
├── src/                 # 源代码
│   ├── components/      # 组件源码
│   ├── utils/           # 工具函数
│   │   └── tracking/    # 埋点系统
│   ├── types/           # TypeScript 类型定义
│   ├── services/        # 服务层
│   ├── core/            # 核心功能
│   ├── constants/       # 常量定义
│   ├── hooks/           # React Hooks
│   ├── store/           # 状态管理
│   ├── lib/             # 库函数
│   ├── i18n/            # 国际化
│   ├── eventbus/        # 事件总线
│   └── styles/          # 样式文件
├── styles/              # 全局样式
└── package.json         # 项目配置
```

## 关键功能

### 声明式埋点系统

项目实现了一个强大的声明式埋点系统，允许通过 HTML 属性自动追踪用户行为。

#### 基本用法

```jsx
<button 
  asm-tracking="CLICK_SEO_BUTTON:CLICK" 
  asm-tracking-p-button_name="signup_button"
>
  注册
</button>
```

#### 支持的事件类型

- `VIEW`：元素渲染时触发
- `CLICK`：点击时触发
- `HOVER`：鼠标悬停时触发
- `FOCUS`：获得焦点时触发
- `BLUR`：失去焦点时触发
- `SUBMIT`：表单提交时触发
- `CHANGE`：输入值变化时触发
- `KEYPRESS`：按键时触发

#### 开发工具

- 按住 `Control + T` 可激活埋点高亮模式
- 在控制台使用 `window.debugTracking` 进行调试

详细文档请参考 [埋点系统文档](src/utils/tracking/README.md)

## 子文档导航

- [埋点系统文档](src/utils/tracking/README.md)
- [Markdown 渲染器文档](src/components/common/README.md)

# get_user_usability API 复刻项目

基于 `tmp_app.js` 文件分析，复刻 `/actions/get_user_usability` 接口的访问逻辑。

## 📋 项目概述

这个项目使用 Python 3.12+ 复刻了 JavaScript 代码中的用户可用性查询接口访问逻辑。原始代码位于 `tmp_app.js` 文件的第 11810-11825 行。

## 🔍 原始接口分析

### JavaScript 原始代码
```javascript
function u(t, n) {
    return a.ZP.get("/clients/".concat(t, "/actions/get_user_usability"), 
                   { params: { identity_code: n } });
}
```

### 接口特征
- **HTTP方法**: GET
- **URL模式**: `/clients/{client_id}/actions/get_user_usability`
- **请求参数**: 
  - `client_id`: 路径参数，客户端标识
  - `identity_code`: 查询参数，用户身份标识码

## 🚀 快速开始

### 环境要求
- Python 3.12+
- 支持异步编程的环境

### 安装依赖
```bash
pip install -r requirements.txt
```

### 基本使用
```python
import asyncio
from get_user_usability_api import GetUserUsabilityAPI

async def example():
    # 初始化API客户端
    api = GetUserUsabilityAPI(base_url="https://your-api-domain.com")
    
    # 查询用户可用性
    result = await api.get_user_usability(
        client_id="your_client_id",
        identity_code="user_identity_code"
    )
    
    print(result)

# 运行示例
asyncio.run(example())
```

## 📖 API 文档

### GetUserUsabilityAPI 类

#### 初始化参数
- `base_url`: API基础URL地址
- `timeout`: 请求超时时间（默认30秒）

#### 核心方法

##### `get_user_usability(client_id, identity_code)`
查询单个用户的可用性信息

**参数:**
- `client_id` (str): 客户端ID
- `identity_code` (str): 用户身份标识码

**返回:**
- `Dict[str, Any]`: API响应数据

##### `batch_get_user_usability(requests)`
批量查询多个用户的可用性信息

**参数:**
- `requests` (List[Tuple[str, str]]): 请求参数列表，每个元素为 (client_id, identity_code) 元组

**返回:**
- `List[Dict[str, Any]]`: 响应数据列表

## 💡 特性

### ✨ 高性能异步设计
- 使用 `asyncio` 和 `aiohttp` 实现异步请求
- 支持并发批量查询，提升处理效率
- 避免阻塞式I/O操作

### 🛡️ 健壮的错误处理
- 自动处理HTTP状态码异常
- 优雅处理JSON解析错误  
- 批量请求中的异常隔离

### 📝 详细的中文注释
- 完整的函数和类文档字符串
- 代码逻辑的中文解释
- 与原始JavaScript代码的对应关系说明

## 🔧 配置选项

### 环境变量支持
创建 `.env` 文件：
```env
API_BASE_URL=https://your-api-domain.com
REQUEST_TIMEOUT=30
```

### 配置文件支持
创建 `config.yaml` 文件：
```yaml
api:
  base_url: https://your-api-domain.com
  timeout: 30
  retry_attempts: 3
```

## 📊 性能对比

| 场景 | JavaScript 原版 | Python 复刻版 | 性能提升 |
|------|----------------|--------------|----------|
| 单个请求 | 同步阻塞 | 异步非阻塞 | ⚡ 响应更快 |
| 批量请求 | 串行执行 | 并发执行 | 🚀 显著提升 |
| 错误处理 | 基础处理 | 增强处理 | 🛡️ 更稳定 |

## 🏗️ 项目结构

```
.
├── get_user_usability_api.py    # 核心API客户端实现
├── requirements.txt             # Python依赖管理
├── README.md                   # 项目说明文档
├── config.yaml                 # 配置文件示例
└── .env.example               # 环境变量示例
```

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/your-feature`)
3. 提交更改 (`git commit -am 'Add your feature'`)
4. 推送到分支 (`git push origin feature/your-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目基于原始JavaScript代码的逻辑进行复刻，仅用于学习和技术交流目的。

## 🔗 相关链接

- [aiohttp 官方文档](https://docs.aiohttp.org/)
- [Python asyncio 文档](https://docs.python.org/3/library/asyncio.html)
- [Python 类型提示文档](https://docs.python.org/3/library/typing.html)


