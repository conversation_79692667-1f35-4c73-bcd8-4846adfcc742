import { NextResponse } from 'next/server';

export function middleware() {
    // console.log(req)

    // 定义你想跳转的维护页面路径
    // const maintenancePage = '/maintenance';

    // // 检查当前请求的 URL
    // const url = req.nextUrl.clone();

    // // 排除静态资源、NextAuth API 请求等
    // if (
    //     url.pathname.startsWith('/_next') ||
    //     url.pathname.startsWith('/static') ||
    //     url.pathname.startsWith('/favicon.ico') ||
    //     url.pathname.startsWith('/api/auth') // 排除 next-auth 的 API 路径
    // ) {
    //     return NextResponse.next();
    // }

    // // 如果请求的路径不是维护页面，则重定向到维护页面
    // if (url.pathname !== maintenancePage) {
    //     url.pathname = maintenancePage;
    //     return NextResponse.redirect(url);
    // }

    // 如果已经在维护页面上，则不做任何操作
    return NextResponse.next();
}

