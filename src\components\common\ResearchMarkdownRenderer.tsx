import React from 'react'
import Mark<PERSON><PERSON>ender<PERSON> from './MarkdownRenderer'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'
import createMarkdownComponents from './MarkdownComponents'

interface ResearchMarkdownRendererProps {
  content: string
  isStreaming?: boolean
  className?: string
  isLoading?: boolean
  role?: string // 角色类型，如planner, researcher, reporter等
  onCopy?: (content: string) => void // 复制内容回调
  disableMermaid?: boolean // 是否禁用 mermaid 图表渲染
}

/**
 * 研究结果页面专用的Markdown渲染器
 * 支持流式渲染和基于角色的渲染样式
 */
const ResearchMarkdownRenderer: React.FC<ResearchMarkdownRendererProps> = ({
  content,
  isStreaming = false,
  className = '',
  isLoading = false,
  role = '',
  onCopy,
  disableMermaid = false,
}) => {
  // 基于角色设置不同的样式
  const getRoleBasedClass = () => {
    const baseClass = 'prose max-w-none'

    switch (role.toLowerCase()) {
      case 'planner':
        return `${baseClass} prose-sm text-gray-700`
      case 'researcher':
        return `${baseClass} prose-sm text-gray-700`
      case 'reporter':
        return `${baseClass} prose-base text-gray-800 break-words`
      default:
        return `${baseClass} prose-sm text-gray-700`
    }
  }

  // 处理复制功能
  const handleCopy = () => {
    if (onCopy && content) {
      onCopy(content)
    }
  }

  // 如果正在加载中，显示加载指示器
  if (isLoading) {
    return (
      <div className='flex flex-col items-center justify-center p-6'>
        <div className='h-10 w-10 animate-spin rounded-full border-b-2 border-t-2 border-indigo-500'></div>
        <p className='mt-4 text-center text-sm text-gray-500'>
          {role === 'planner' ? '正在分析您的需求，生成研究计划...' : '正在处理内容...'}
        </p>
      </div>
    )
  }

  // 使用统一的Markdown组件，在流式渲染时自动禁用mermaid图表
  const components = createMarkdownComponents({
    trackingPage: 'RESEARCH_PAGE',
    disableMermaid: isStreaming || disableMermaid,
  })

  // 根据内容是否为空决定是否显示复制按钮
  const showCopyButton = content && content.trim().length > 0 && onCopy

  return (
    <div className='relative'>
      <MarkdownRenderer
        content={content}
        isStreaming={isStreaming}
        className={className || getRoleBasedClass()}
        rehypePlugins={[rehypeRaw]}
        remarkPlugins={[remarkGfm]}
        components={components}
      />

      {showCopyButton && (
        <button
          onClick={handleCopy}
          className='absolute bottom-3 right-3 text-gray-400 transition-colors duration-200 hover:text-indigo-600'
          title='复制内容'>
          <svg className='h-5 w-5' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3'
            />
          </svg>
        </button>
      )}
    </div>
  )
}

export default ResearchMarkdownRenderer
