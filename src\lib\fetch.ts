// import { clearCookies } from './auth'
// import { getCookie } from './cookie'
// import { isServer } from './environments'
//
// export const fetchWithAuth = async <T>(
//   input: RequestInfo | URL,
//   options?: RequestInit,
// ): Promise<T> => {
//   const token = `Bearer ${getCookie('smartToken')!}`
//
//   const response = await fetch(input, {
//     ...options,
//     headers: {
//       authorization: token,
//       ...options?.headers,
//     },
//   })
//
//   if (!response.ok) {
//     if (response.status === 403) {
//       if (isServer()) {
//         return Promise.reject({
//           status: 403,
//           message: 'Forbidden: Redirecting to login...',
//           redirectUrl: '/logout',
//         })
//       } else {
//         clearCookies()
//         window.location.href = '/login'
//         return Promise.reject(new Error('Forbidden: Redirecting to logout...'))
//       }
//     }
//
//     // eslint-disable-next-line @typescript-eslint/no-explicit-any
//     const error = new Error(`HTTP error! Status: ${response.status}`) as any
//     error.status = response.status
//     throw error
//   }
//
//   return (await response.json()) as T
// }
//
// export const fetchInstance = async <T>(
//   input: RequestInfo | URL,
//   options?: RequestInit,
// ): Promise<T> => {
//   const response = await fetch(input, {
//     ...options,
//     headers: {
//       ...options?.headers,
//     },
//   })
//
//   if (!response.ok) {
//     // eslint-disable-next-line @typescript-eslint/no-explicit-any
//     const error = new Error(`HTTP error! Status: ${response.status}`) as any
//     error.status = response.status
//     throw error
//   }
//   return (await response.json()) as T
// }
