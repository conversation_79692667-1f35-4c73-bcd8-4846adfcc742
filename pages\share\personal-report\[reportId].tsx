import { getReportById } from '@/api/getReportById'
import Loading from '@/components/common/Loading'
import BaseLayout from '@/components/layouts/BaseLayout'
import MarkdownParser from '@/components/markdown/MarkdownParser'
import CallToAction from '@/components/seo/CallToAction'
import FreeTrialPromo from '@/components/seo/FreeTrialPromo'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'

const ReportPreviewPage = () => {
  const handleLoginClick = (e: React.MouseEvent) => {
    e.preventDefault()
    window.location.href = 'https://ai-smarties.com/deep-research'
  }

  const router = useRouter()
  const { reportId, title } = router.query
  const [report, setReport] = useState({ content: '', referenceList: [] })
  const [loading, setLoading] = useState(false)

  const getReport = async () => {
    try {
      setLoading(true)
      const res = await getReportById({ reportId: reportId as string })
      setReport(res)
      setLoading(false)
    } catch (e) {
      console.error(e)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (reportId) {
      getReport()
    }
  }, [reportId])

  return (
    <>
      <BaseLayout>
        {/* Main Content */}
        <main className='py-2 md:py-4'>
          <div className='mx-auto max-w-7xl px-2 sm:px-4'>
            <div className='flex flex-col lg:flex-row lg:gap-6'>
              {/* Left Content Area */}
              <div className='w-full flex-1 lg:max-w-5xl'>
                <div className='mt-8 px-12'>
                  {loading ? (
                    <Loading />
                  ) : (
                    <>
                      <h1 className='text-4xl font-bold'>{title}</h1>
                      <MarkdownParser content={report.content} list={report.referenceList} />
                    </>
                  )}
                </div>
                <div className='py-12'>
                  <CallToAction />
                </div>
              </div>

              {/* Right Sidebar */}
              <div className='mt-4 w-full lg:mt-0 lg:w-80'>
                <div className='lg:sticky lg:top-4'>
                  {/* 免费试用引导 */}
                  <div className='mb-4 mt-8'>
                    <FreeTrialPromo
                      asm-tracking='CLICK_SEO_SIGNUP:CLICK'
                      asm-tracking-p-view='Desktop'
                      asm-tracking-p-position='FreeTrialPromo'
                      onButtonClick={handleLoginClick}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </BaseLayout>
    </>
  )
}

export default ReportPreviewPage
