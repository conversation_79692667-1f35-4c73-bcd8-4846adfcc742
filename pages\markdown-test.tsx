import React from 'react'
import Head from 'next/head'
import SimpleLayout from '@/components/layouts/SimpleLayout'
import MarkdownRenderer from '@/components/common/MarkdownRenderer'
import createMarkdownComponents from '@/components/common/MarkdownComponents'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'

const MarkdownTest: React.FC = () => {
  // 使用统一的 Markdown 组件
  const components = createMarkdownComponents()

  // 第四范式分析报告 Markdown 内容
  const marketAnalysisContent = `# 第四范式（4Paradigm）综合市场分析报告

## 摘要

第四范式作为中国领先的企业级人工智能服务商，通过先知AI平台、行业解决方案及AutoML技术构建核心壁垒，在金融/零售/制造领域服务超70%国有银行及世界500强企业。其2023年36.4%的营收增长印证商业化能力，但面临BAT云厂商在标准化产品领域的激烈竞争。

## 关键发现

1. **主营业务**：形成"平台+解决方案+开发工具"三位一体架构，先知AI平台贡献近60%营收
2. **客户分布**：金融行业占比超50%，深度绑定工商银行等头部机构，零售/制造领域增速超40%
3. **竞争格局**：机器学习平台市场份额连续4年第一（IDC数据），但云厂商在中小客户市场加速渗透
4. **技术壁垒**：AutoML支持万亿级特征处理，行业知识库积累超10万业务场景特征工程模板

---

## 详细分析

### 一、主营业务架构

| 业务板块         | 核心技术                 | 典型应用场景                     | 2023收入占比 |
|------------------|--------------------------|----------------------------------|-------------|
| 先知AI平台       | AutoML/迁移学习          | 金融风控模型训练                 | 59.6%       |
| SHIFT解决方案    | 行业大模型               | 零售商品销量预测                 | 30.5%       |
| 式说AIGS         | 生成式AI                 | 制造业设备运维代码生成           | 9.9%        |

*数据来源：2023年业绩公告*

技术特点：

- 先知AI平台支持72小时完成从数据清洗到模型部署的全流程
- SHIFT解决方案预置300+行业特征工程模板
- 式说AIGS实现代码生成准确率92.3%（内部测试数据）

---

### 二、核心客户画像

**行业分布：**

\`\`\`mermaid
pie
    title 行业收入占比
    "金融" : 52
    "零售" : 24
    "制造" : 15
    "其他" : 9
\`\`\`

**标杆案例：**

- 工商银行：反欺诈模型将误报率降低83%
- 永辉超市：销量预测准确率提升37%，库存周转天数减少12天
- 宁德时代：设备故障预测准确率达98.7%，年节省运维成本2.3亿元

*注：数据来自客户公开案例库*

---

### 三、竞争格局对比

| 维度           | 第四范式                  | 百度智能云              | 阿里云                |
|----------------|--------------------------|-------------------------|----------------------|
| 核心技术       | AutoML+行业大模型        | 文心大模型              | 通义千问             |
| 部署方式       | 私有化部署为主           | 公有云+混合云           | 公有云主导           |
| 客单价         | 500万+                   | 100-300万               | 50-200万             |
| 交付周期       | 3-6个月                  | 1-3个月                 | 2-4周                |
| 核心优势       | 行业know-how积累         | 通用模型成熟度          | 云基础设施优势       |

*数据来源：各厂商官网及行业调研报告*

---

### 四、技术壁垒拆解

**1. AutoML技术栈**

\`\`\`mermaid
graph LR
    A[特征工程] --> B[模型选择]
    B --> C[超参优化]
    C --> D[模型压缩]
    D --> E[持续学习]
\`\`\`

- 支持30+算法自动组合优化
- 模型推理速度达5万QPS（行业平均1.2万QPS）

**2. 行业知识库**

- 金融领域：积累8.7万风险规则特征
- 零售领域：构建2000+商品关联图谱
- 制造领域：沉淀5.3万设备故障特征

---

## 结论与建议

**投资价值判断：**

- ✅ 优势：行业纵深能力构筑护城河，头部客户续约率91%
- ⚠️ 风险：研发费用率38.7%高于行业均值，云厂商价格战压力

**战略建议：**

1. 对投资者：关注其在能源/医疗等新兴行业的拓展进度
2. 对企业客户：优先考虑复杂业务场景的定制化需求
3. 对竞争者：需突破其行业特征工程的专利壁垒（已获62项相关专利）

*数据可靠性说明：财务数据来自港交所公告，技术参数源自官方白皮书，市场份额引用IDC 2023Q4报告。部分客户案例因保密协议未披露细节。*`

  // Mermaid 图表测试内容
  const mermaidTestContent = `# Mermaid 图表类型测试

本页面展示了所有 Mermaid 图表类型的示例。

## 1. 饼图 (Pie Chart)

\`\`\`mermaid
pie
    title 今年销售额占比
    "第一季度" : 25
    "第二季度" : 30
    "第三季度" : 28
    "第四季度" : 17
\`\`\`

## 2. 流程图 (Flowchart)

\`\`\`mermaid
graph TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主页]
    B -->|否| D[显示登录页]
    C --> E[用户操作]
    D --> F[登录流程]
    F --> C
    E --> G[结束]
\`\`\`

## 3. 时序图 (Sequence Diagram)

\`\`\`mermaid
sequenceDiagram
    participant 用户
    participant 前端
    participant 后端
    participant 数据库
    
    用户->>前端: 点击登录按钮
    前端->>后端: 发送登录请求
    后端->>数据库: 验证用户信息
    数据库-->>后端: 返回验证结果
    后端-->>前端: 返回登录结果
    前端-->>用户: 显示登录成功/失败
\`\`\`

## 4. 类图 (Class Diagram)

\`\`\`mermaid
classDiagram
    class User {
        +String username
        +String password
        +login()
        +logout()
    }
    class Admin {
        +Array permissions
        +manageUsers()
    }
    class Guest {
        +browseContent()
    }
    User <|-- Admin
    User <|-- Guest
\`\`\`

## 5. 状态图 (State Diagram)

\`\`\`mermaid
---
title: Simple sample
---
stateDiagram-v2
    [*] --> Still
    Still --> [*]

    Still --> Moving
    Moving --> Still
    Moving --> Crash
    Crash --> [*]

\`\`\`

## 6. 甘特图 (Gantt Chart)

\`\`\`mermaid
gantt
    dateFormat  YYYY-MM-DD
    title       Adding GANTT diagram functionality to mermaid
    excludes    weekends
    %% (\`excludes\` accepts specific dates in YYYY-MM-DD format, days of the week ("sunday") or "weekends", but not the word "weekdays".)

    section A section
    Completed task            :done,    des1, 2014-01-06,2014-01-08
    Active task               :active,  des2, 2014-01-09, 3d
    Future task               :         des3, after des2, 5d
    Future task2              :         des4, after des3, 5d

    section Critical tasks
    Completed task in the critical line :crit, done, 2014-01-06,24h
    Implement parser and jison          :crit, done, after des1, 2d
    Create tests for parser             :crit, active, 3d
    Future task in critical line        :crit, 5d
    Create tests for renderer           :2d
    Add to mermaid                      :until isadded
    Functionality added                 :milestone, isadded, 2014-01-25, 0d

    section Documentation
    Describe gantt syntax               :active, a1, after des1, 3d
    Add gantt diagram to demo page      :after a1  , 20h
    Add another diagram to demo page    :doc1, after a1  , 48h

    section Last section
    Describe gantt syntax               :after doc1, 3d
    Add gantt diagram to demo page      :20h
    Add another diagram to demo page    :48h

\`\`\`

## 7. 实体关系图 (Entity Relationship Diagram)

\`\`\`mermaid
---
title: Order example
---
erDiagram
    CUSTOMER ||--o{ ORDER : places
    ORDER ||--|{ LINE-ITEM : contains
    CUSTOMER }|..|{ DELIVERY-ADDRESS : uses

\`\`\`

## 8. 用户旅程图 (User Journey)

\`\`\`mermaid
journey
    title My working day
    section Go to work
      Make tea: 5: Me
      Go upstairs: 3: Me
      Do work: 1: Me, Cat
    section Go home
      Go downstairs: 5: Me
      Sit down: 5: Me

\`\`\`

## 9. XY 图表 (XY Chart)

\`\`\`mermaid
xychart-beta
    title "Sales Revenue"
    x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]
    y-axis "Revenue (in $)" 4000 --> 11000
    bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]
    line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]

\`\`\`

## 10. 时间线图 (Timeline)

\`\`\`mermaid
timeline
        title MermaidChart 2023 Timeline
        section 2023 Q1 <br> Release Personal Tier
          Bullet 1 : sub-point 1a : sub-point 1b
               : sub-point 1c
          Bullet 2 : sub-point 2a : sub-point 2b
        section 2023 Q2 <br> Release XYZ Tier
          Bullet 3 : sub-point <br> 3a : sub-point 3b
               : sub-point 3c
          Bullet 4 : sub-point 4a : sub-point 4b

\`\`\`

## 11. 桑基图 (Sankey Diagram)

\`\`\`mermaid
---
config:
  sankey:
    showValues: false
---
sankey-beta

Agricultural 'waste',Bio-conversion,124.729
Bio-conversion,Liquid,0.597
Bio-conversion,Losses,26.862
Bio-conversion,Solid,280.322
Bio-conversion,Gas,81.144
Biofuel imports,Liquid,35
Biomass imports,Solid,35
Coal imports,Coal,11.606
Coal reserves,Coal,63.965
Coal,Solid,75.571
District heating,Industry,10.639
District heating,Heating and cooling - commercial,22.505
District heating,Heating and cooling - homes,46.184
Electricity grid,Over generation / exports,104.453
Electricity grid,Heating and cooling - homes,113.726
Electricity grid,H2 conversion,27.14
Electricity grid,Industry,342.165
Electricity grid,Road transport,37.797
Electricity grid,Agriculture,4.412
Electricity grid,Heating and cooling - commercial,40.858
Electricity grid,Losses,56.691
Electricity grid,Rail transport,7.863
Electricity grid,Lighting & appliances - commercial,90.008
Electricity grid,Lighting & appliances - homes,93.494
Gas imports,Ngas,40.719
Gas reserves,Ngas,82.233
Gas,Heating and cooling - commercial,0.129
Gas,Losses,1.401
Gas,Thermal generation,151.891
Gas,Agriculture,2.096
Gas,Industry,48.58
Geothermal,Electricity grid,7.013
H2 conversion,H2,20.897
H2 conversion,Losses,6.242
H2,Road transport,20.897
Hydro,Electricity grid,6.995
Liquid,Industry,121.066
Liquid,International shipping,128.69
Liquid,Road transport,135.835
Liquid,Domestic aviation,14.458
Liquid,International aviation,206.267
Liquid,Agriculture,3.64
Liquid,National navigation,33.218
Liquid,Rail transport,4.413
Marine algae,Bio-conversion,4.375
Ngas,Gas,122.952
Nuclear,Thermal generation,839.978
Oil imports,Oil,504.287
Oil reserves,Oil,107.703
Oil,Liquid,611.99
Other waste,Solid,56.587
Other waste,Bio-conversion,77.81
Pumped heat,Heating and cooling - homes,193.026
Pumped heat,Heating and cooling - commercial,70.672
Solar PV,Electricity grid,59.901
Solar Thermal,Heating and cooling - homes,19.263
Solar,Solar Thermal,19.263
Solar,Solar PV,59.901
Solid,Agriculture,0.882
Solid,Thermal generation,400.12
Solid,Industry,46.477
Thermal generation,Electricity grid,525.531
Thermal generation,Losses,787.129
Thermal generation,District heating,79.329
Tidal,Electricity grid,9.452
UK land based bioenergy,Bio-conversion,182.01
Wave,Electricity grid,19.013
Wind,Electricity grid,289.366

\`\`\`

## 12. 象限图 (Quadrant Chart)

\`\`\`mermaid
quadrantChart
    title Reach and engagement of campaigns
    x-axis Low Reach --> High Reach
    y-axis Low Engagement --> High Engagement
    quadrant-1 We should expand
    quadrant-2 Need to promote
    quadrant-3 Re-evaluate
    quadrant-4 May be improved
    Campaign A: [0.3, 0.6]
    Campaign B: [0.45, 0.23]
    Campaign C: [0.57, 0.69]
    Campaign D: [0.78, 0.34]
    Campaign E: [0.40, 0.34]
    Campaign F: [0.35, 0.78]

\`\`\`

## 13. Git 图 (Git Graph)

\`\`\`mermaid
%%{init: { 'logLevel': 'debug', 'theme': 'base', 'gitGraph': {'showBranches': false}} }%%
      gitGraph
        commit
        branch hotfix
        checkout hotfix
        commit
        branch develop
        checkout develop
        commit id:"ash" tag:"abc"
        branch featureB
        checkout featureB
        commit type:HIGHLIGHT
        checkout main
        checkout hotfix
        commit type:NORMAL
        checkout develop
        commit type:REVERSE
        checkout featureB
        commit
        checkout main
        merge hotfix
        checkout featureB
        commit
        checkout develop
        branch featureA
        commit
        checkout develop
        merge hotfix
        checkout featureA
        commit
        checkout featureB
        commit
        checkout develop
        merge featureA
        branch release
        checkout release
        commit
        checkout main
        commit
        checkout release
        merge main
        checkout develop
        merge release

\`\`\`

## 14. 看板图 (Kanban)

\`\`\`mermaid
---
displayMode: compact
---

---
config:
  kanban:
    ticketBaseUrl: 'https://mermaidchart.atlassian.net/browse/#TICKET#'
---
kanban
  Todo
    [Create Documentation]
    docs[Create Blog about the new diagram]
  [In progress]
    id6[Create renderer so that it works in all cases. We also add som extra text here for testing purposes. And some more just for the extra flare.]
  id9[Ready for deploy]
    id8[Design grammar]@{ assigned: 'knsv' }
  id10[Ready for test]
    id4[Create parsing tests]@{ ticket: MC-2038, assigned: 'K.Sveidqvist', priority: 'High' }
    id66[last item]@{ priority: 'Very Low', assigned: 'knsv' }
  id11[Done]
    id5[define getData]
    id2[Title of diagram is more than 100 chars when user duplicates diagram with 100 char]@{ ticket: MC-2036, priority: 'Very High'}
    id3[Update DB function]@{ ticket: MC-2037, assigned: knsv, priority: 'High' }

  id12[Can't reproduce]
    id3[Weird flickering in Firefox]

\`\`\`

## 15. 带错误的 Mermaid 图表（测试错误处理）

\`\`\`mermaid
piechart
    title 这是一个错误的图表
    "A" : 50
    "B" : 50
\`\`\`
`

  // 当前展示的内容类型
  const [contentType, setContentType] = React.useState<'market' | 'mermaid'>('market')

  return (
    <>
      <Head>
        <title>Markdown 测试页面 | AI Smarties</title>
        <meta name='description' content='测试 Markdown 渲染功能，包括 Mermaid 图表支持' />
      </Head>

      <SimpleLayout>
        <div className='container mx-auto px-4 py-8'>
          <h1 className='mb-6 text-3xl font-bold text-gray-900'>Markdown 渲染测试</h1>

          {/* 内容切换按钮 */}
          <div className='mb-6 flex space-x-4'>
            <button
              className={`rounded-md px-4 py-2 transition-colors ${
                contentType === 'market'
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => setContentType('market')}>
              市场分析报告
            </button>
            <button
              className={`rounded-md px-4 py-2 transition-colors ${
                contentType === 'mermaid'
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => setContentType('mermaid')}>
              Mermaid 图表测试
            </button>
          </div>

          {/* 内容显示区域 */}
          <div className='mb-8 rounded-lg bg-white p-6 shadow-lg'>
            <MarkdownRenderer
              content={contentType === 'market' ? marketAnalysisContent : mermaidTestContent}
              className='prose max-w-none'
              rehypePlugins={[rehypeRaw]}
              remarkPlugins={[remarkGfm]}
              components={components}
            />
          </div>

          <div className='rounded-lg bg-gray-50 p-4'>
            <h2 className='mb-4 text-xl font-semibold'>说明</h2>
            <p className='text-gray-700'>
              本页面用于测试 Markdown 渲染功能，特别是 Mermaid
              图表支持。您可以在上面的内容中看到各种 Markdown 元素，
              包括标题、列表、表格、强调文本和 Mermaid 图表（饼图、流程图等）。
            </p>
            <p className='mt-2 text-gray-700'>
              通过切换按钮，您可以查看市场分析报告示例或测试所有类型的 Mermaid
              图表。如果某些图表无法渲染， 系统会自动以代码形式显示，确保内容始终可见。
            </p>
          </div>
        </div>
      </SimpleLayout>
    </>
  )
}

export default MarkdownTest
