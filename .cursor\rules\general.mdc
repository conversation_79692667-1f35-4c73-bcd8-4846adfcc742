---
description: 
globs: 
alwaysApply: true
---
# 规则目标
以下规则，在任何时候都生效，请务必遵循

# 规则详情
* 永远使用中文回复
* 所有修改过的代码，必须增加详细的中文注释
* 国际化：所有涉及到的文案，需要遵循我们的标准的国际化规范，将中英文文案拆解到 [global.json](mdc:public/locales/en/global.json) 和 [global.json](mdc:public/locales/zh/global.json) 中
* 信息埋点： 遵循 [auto-tracking.ts](mdc:src/utils/tracking/auto-tracking.ts) 中定义的埋点协议，为页面自动添加 asm-tracking='DEMO_COMPONENT_VIEW:VIEW' VIEW 事件，为按钮自动添加 asm-tracking='DEMO_BUTTON_CLICK:CLICK' CLICK 事件，并添加 asm-tracking-p-attribute_name='xxx' 来增加有助于区分的埋点属性
* 代码变更：变更代码时，自动检查上下文引用和被引用，确保改动影响范围最小，且链路完成对变更的适配
* 交互设计：样式设计需要尽可能轻量级，UI 简约现代化，伴有良好的视觉和交互效果
* 性能十分重要，要避免前端页面快速进行大文本的重复渲染

