// 事件类型枚举
export enum EventType {
  START_OF_LLM = 'start_of_llm',
  END_OF_LLM = 'end_of_llm',
  MESSAGE = 'message',
  TOOL_CALL = 'tool_call',
  TOOL_CALL_RESULT = 'tool_call_result',
}

// 语言枚举
export enum Language {
  ZH = 'zh',
  EN = 'en',
  JA = 'ja',
  KO = 'ko',
}

// 本地化内容接口
export interface LocalizedContent {
  title: string
  content: string
  summary?: string
  keywords?: string[]
}

// 重要代理枚举 - 只处理这三种代理的消息
export enum ImportantAgent {
  PLANNER = 'planner',
  RESEARCHER = 'researcher',
  REPORTER = 'reporter',
}

// 研究步骤状态类型
export type StepStatus = 'waiting' | 'processing' | 'completed'

// 研究步骤接口
export interface ResearchStep {
  id: string
  name: string
  agent: string
  status: StepStatus
  isActive: boolean
  description?: string
}

// 研究计划接口
export interface ResearchPlan {
  thought?: string
  title?: string
  steps?: Array<{
    title: string
    description?: string
    note?: string
    agent_name?: string
  }>
}

// 流式响应内容类型
export interface StreamContent {
  id: string
  type: EventType
  agent_name?: string
  delta?: {
    content: string
  }
  tool_name?: string
  tool_input?: string
  content?: string
  collapsed?: boolean
  sequence?: number
  isLoading?: boolean
  fullContent?: string
}

// 复制提示状态类型
export interface CopyToastState {
  show: boolean
  message: string
}

// 研究数据接口
export interface ResearchData {
  原始问题: string
  问题确认?: string
  [key: string]: any
}
