import { Language, LocalizedContent, ResearchData } from '@/types/research'

function removeFirstH1(content: string): string {
  if (content != undefined) {
    // 移除开头的 # 标题（包括可能的空行）
    return content.replace(/^#\s+[^\n]+\n+/, '')
  }
  return content
}

export async function getCompanyData(param: string): Promise<ResearchData | null> {
  try {
    const url = `https://digismarties-public.s3.ap-northeast-1.amazonaws.com/seo/data/company/${param}.json`
    console.log('url: ', url)
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const marketData = (await response.json()) as ResearchData

    // 处理每种语言的内容，创建新对象而不是直接修改
    const processedMarketData: ResearchData = {
      ...marketData,
      localizedData: Object.fromEntries(
        Object.entries(marketData.localizedData || {}).map(([lang, data]) => [
          lang as Language,
          {
            title: (data as LocalizedContent)?.title || '',
            content: removeFirstH1((data as LocalizedContent)?.content || ''),
            summary:
              (data as LocalizedContent)?.summary === undefined
                ? null
                : (data as LocalizedContent)?.summary,
            keywords: (data as LocalizedContent)?.keywords,
          } as LocalizedContent,
        ]),
      ) as Record<Language, LocalizedContent>,
    }

    return processedMarketData
  } catch (error) {
    console.error(`Failed to load market data for ${param}:`, error)
    return null
  }
}
