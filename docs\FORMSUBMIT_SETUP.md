# FormSubmit.co 集成设置指南

## 概述

`pages/intel-dossier.tsx` 页面已集成 FormSubmit.co 服务，使用原生HTML POST表单提交，用于处理表单提交和邮件发送功能。这种方式更可靠，避免了CORS问题，具有更好的兼容性。

## 原生POST提交的优势

### ✅ 可靠性

- 不依赖JavaScript，表单提交更稳定
- 避免网络请求被浏览器拦截
- 不受CORS政策限制

### ✅ 兼容性

- 支持所有浏览器，包括禁用JavaScript的环境
- 移动端兼容性更好
- 对网络环境要求更低

### ✅ 用户体验

- FormSubmit自动处理重定向
- 原生表单验证支持
- 更快的响应时间

## 设置步骤

### 1. 配置邮箱地址

在 `pages/intel-dossier.tsx` 文件中，找到以下代码：

```typescript
const FORMSUBMIT_EMAIL = '<EMAIL>' // 替换为您的邮箱地址
```

将 `<EMAIL>` 替换为您的真实邮箱地址。

### 2. 激活 FormSubmit 服务

1. 首次使用时，FormSubmit 会发送一封确认邮件到您的邮箱
2. 点击确认邮件中的链接激活服务
3. 激活后，所有表单提交都会发送到您的邮箱

### 3. 配置选项说明

当前配置包含以下 FormSubmit 选项：

- `_next`: 提交后重定向的页面
- `_subject`: 邮件主题
- `_captcha`: 禁用验证码
- `_template`: 使用表格格式
- `_replyto`: 回复到用户邮箱
- `_autoresponse`: 自动回复消息
- `_honey`: 防垃圾邮件蜜罐字段

### 4. 测试功能

1. 部署应用后，访问 Intel Dossier 页面
2. 完成所有卡片操作
3. 在最终屏幕输入邮箱地址
4. 点击提交按钮
5. 检查您的邮箱是否收到通知

## 功能特性

### 用户体验

- 实时提交状态反馈
- 错误处理和重试机制
- 成功提交后的庆祝动画
- 自动回复确认邮件

### 数据收集

表单提交会收集以下信息：

- 用户邮箱
- 提交时间
- 用户代理信息
- 来源页面信息

### 安全性

- 防垃圾邮件蜜罐字段
- 客户端验证
- 错误处理

## 故障排除

### 常见问题

1. **邮件未收到**

   - 检查垃圾邮件文件夹
   - 确认邮箱地址配置正确
   - 验证 FormSubmit 服务已激活

2. **提交失败**

   - 检查网络连接
   - 确认 FormSubmit 服务状态
   - 查看浏览器控制台错误信息

3. **重定向问题**
   - 检查 `_next` 参数配置
   - 确认域名设置正确

### 开发者调试

- 打开浏览器控制台查看调试信息
- 检查网络请求状态
- 验证表单数据格式

## 进阶配置

### 自定义回复邮件

修改 `_autoresponse` 字段来自定义自动回复内容：

```typescript
formData.append('_autoresponse', '您的自定义回复内容')
```

### 添加抄送

配置 `_cc` 字段来添加抄送邮箱：

```typescript
formData.append('_cc', '<EMAIL>')
```

### 自定义邮件模板

FormSubmit 支持多种邮件模板：

- `basic`: 基础模板
- `table`: 表格模板（当前使用）
- `box`: 框格模板

## 更多资源

- [FormSubmit.co 官方文档](https://formsubmit.co/)
- [FormSubmit API 参考](https://formsubmit.co/documentation)
