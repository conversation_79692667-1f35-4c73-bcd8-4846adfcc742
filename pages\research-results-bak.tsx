import React, { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import SimpleLayout from '@/components/layouts/SimpleLayout'
import { motion } from 'framer-motion'
import ResearchMarkdownRenderer from '@/components/common/ResearchMarkdownRenderer'
import { useTranslation } from 'react-i18next'

// 在文件开头添加调试用的日志函数
const debugLog = (msg: string, data: any = null) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[DEBUG] ${msg}`, data || '')
  }
}

// 从环境变量中获取API基础URL，如果不存在则使用默认值
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

// 事件类型枚举
enum EventType {
  MESSAGE = 'message',
  END_OF_LLM = 'end_of_llm',
  TOOL_CALL = 'tool_call',
  START_OF_LLM = 'start_of_llm',
}

// 角色类型枚举
enum RoleType {
  PLANNER = 'planner',
  RESEARCHER = 'researcher',
  REPORTER = 'reporter',
}

// 步骤类型枚举
enum StepType {
  ON_CHAT_MODEL_STREAM = 'on_chat_model_stream',
}

// 响应内容类型
type StreamContent = {
  id: string
  type: EventType
  role: RoleType
  step: StepType
  delta?: {
    content: string
  }
  content?: string
  collapsed?: boolean
  isLoading?: boolean
  fullContent?: string // 用于PLANNER的完整内容缓存
  isCompleted?: boolean // 标记内容是否已完成，用于控制 mermaid 渲染
  isStreaming?: boolean // 标记为流式内容
}

// 研究步骤状态类型
type StepStatus = 'waiting' | 'processing' | 'completed'

// 研究步骤接口
interface ResearchStep {
  id: string
  name: string
  agent: string
  status: StepStatus
  isActive: boolean
  description?: string
}

// 研究计划接口
interface ResearchPlan {
  thought?: string
  title?: string
  steps?: any[]
}

// 全局请求标记，防止重复请求
let globalRequestInProgress = false

const ResearchResultsPage: React.FC = () => {
  const { t } = useTranslation('global')
  const router = useRouter()
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [question, setQuestion] = useState<string>('')
  const [researchData, setResearchData] = useState<any | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [contents, setContents] = useState<StreamContent[]>([])
  const [activeTask, setActiveTask] = useState<string>('')
  const [researchPlan, setResearchPlan] = useState<ResearchPlan | null>(null)
  const [isCompleted, setIsCompleted] = useState<boolean>(false)
  const outputRef = useRef<HTMLDivElement>(null)
  const researchStartTime = useRef<number | null>(null)
  const [copyToast, setCopyToast] = useState<{ show: boolean; message: string }>({
    show: false,
    message: '',
  })

  // 跟踪请求是否已发送，避免重复请求
  const requestSentRef = useRef<boolean>(false)

  // 研究步骤状态
  const [researchSteps, setResearchSteps] = useState<ResearchStep[]>([
    {
      id: '1',
      name: t('researchResults.requirementAnalysis'),
      agent: RoleType.PLANNER,
      status: 'waiting',
      isActive: false,
    },
    {
      id: '2',
      name: t('researchResults.initialResearch'),
      agent: RoleType.RESEARCHER,
      status: 'waiting',
      isActive: false,
    },
    {
      id: '3',
      name: t('researchResults.additionalAnalysis'),
      agent: 'browser',
      status: 'waiting',
      isActive: false,
    },
    {
      id: '4',
      name: t('researchResults.reportWriting'),
      agent: RoleType.REPORTER,
      status: 'waiting',
      isActive: false,
    },
  ])

  // 在状态声明部分添加新的状态
  const [collapsedSections, setCollapsedSections] = useState<{
    planner: boolean
    researcher: boolean
  }>({
    planner: false,
    researcher: false,
  })

  // 从路由参数中解析研究数据
  useEffect(() => {
    if (!router.isReady) return

    try {
      const id = router.query.id as string
      if (!id) return

      // 从后端API获取任务数据
      const fetchTaskData = async () => {
        try {
          console.log('从后端获取任务数据，task_id:', id)
          setActiveTask(t('researchResults.loadingTaskData'))

          const response = await fetch(`${API_BASE_URL}/api/task/${id}`)

          if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`)
          }

          const result = await response.json()

          if (!result.success || !result.data) {
            throw new Error('无效的API响应')
          }

          console.log('获取任务数据成功:', result.data)

          // 设置研究数据
          const taskData = result.data
          setResearchData({
            task_id: taskData.task_id,
            原始问题: taskData.question,
            问题确认: taskData.requirement || '',
          })

          // 设置问题和开始请求
          const questionText = `原始问题：${taskData.question} \n和用户确认的内容：${taskData.requirement}`
          setQuestion(questionText)

          // 使用组件内部的ref来跟踪请求状态
          if (!requestSentRef.current && questionText) {
            // 标记请求已发送
            requestSentRef.current = true
            // 自动开始流式请求
            handleStreamRequest(questionText)
          }
        } catch (error) {
          console.error('获取任务数据失败:', error)
          // 创建一个默认的研究数据对象，包含必要的字段
          // setResearchData({
          //   task_id: id,
          //   原始问题: t('researchResults.loadingError'),
          //   问题确认: t('researchResults.loadingErrorDesc')
          // })
          setActiveTask(t('researchResults.errorOccurred'))
        }
      }

      fetchTaskData()
    } catch (error) {
      console.error('解析研究数据失败:', error)
    }
  }, [router.isReady, router.query])

  // 当内容更新时自动滚动到底部
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight
    }
  }, [contents])

  // 处理从流中接收到的数据
  const processStreamData = (data: any) => {
    try {
      // 解析JSON数据
      const eventData = JSON.parse(data)

      // 更新研究进度状态
      updateResearchSteps(eventData)

      // 根据角色和类型处理数据
      switch (eventData.ROLE) {
        case RoleType.PLANNER:
          handlePlannerData(eventData)
          break
        case RoleType.RESEARCHER:
          handleResearcherData(eventData)
          break
        case RoleType.REPORTER:
          handleReporterData(eventData)
          break
        default:
          console.log('未知的角色类型:', eventData.ROLE)
      }

      // 处理工具调用
      if (eventData.TYPE === EventType.TOOL_CALL) {
        handleToolCall(eventData)
      }
    } catch (e) {
      console.error('解析事件数据失败:', e, data)
    }
  }

  // 更新研究步骤状态
  const updateResearchSteps = (data: any) => {
    const { TYPE, ROLE } = data

    // 根据事件类型和角色更新步骤状态
    if (TYPE === EventType.START_OF_LLM) {
      if (ROLE === RoleType.PLANNER) {
        // 拆解需求设置为处理中
        setResearchSteps((prev) =>
          prev.map((step) =>
            step.agent === RoleType.PLANNER
              ? { ...step, status: 'processing' as StepStatus, isActive: true }
              : step,
          ),
        )
      } else if (ROLE === RoleType.RESEARCHER) {
        // 补充分析设置为处理中
        setResearchSteps((prev) =>
          prev.map((step) =>
            step.agent === 'browser'
              ? { ...step, status: 'processing' as StepStatus, isActive: true }
              : step,
          ),
        )
      } else if (ROLE === RoleType.REPORTER) {
        // 撰写结论设置为处理中，之前所有节点设置为已完成
        setResearchSteps((prev) =>
          prev.map((step) => {
            if (step.agent === RoleType.REPORTER) {
              return { ...step, status: 'processing' as StepStatus, isActive: true }
            } else {
              return { ...step, status: 'completed' as StepStatus, isActive: false }
            }
          }),
        )
        setActiveTask(t('researchResults.reportWriting'))

        // 自动折叠前面的内容
        setCollapsedSections({
          planner: true,
          researcher: true,
        })
      }
    } else if (TYPE === EventType.END_OF_LLM) {
      if (ROLE === RoleType.PLANNER) {
        // 拆解需求设置为已完成，初步研究设置为处理中
        setResearchSteps((prev) =>
          prev.map((step) => {
            if (step.agent === RoleType.PLANNER) {
              return { ...step, status: 'completed' as StepStatus, isActive: false }
            } else if (step.agent === RoleType.RESEARCHER) {
              return { ...step, status: 'processing' as StepStatus, isActive: true }
            }
            return step
          }),
        )
      } else if (ROLE === RoleType.REPORTER) {
        // 撰写结论设置为已完成，展示研究已全部完成
        setResearchSteps((prev) =>
          prev.map((step) =>
            step.agent === RoleType.REPORTER
              ? { ...step, status: 'completed' as StepStatus, isActive: false }
              : step,
          ),
        )
        setIsCompleted(true)
        setActiveTask(t('researchResults.allCompleted'))
      }
    }
  }

  // 处理规划者数据
  const handlePlannerData = (data: any) => {
    switch (data.TYPE) {
      case EventType.MESSAGE:
        // 累积规划者的消息内容
        setContents((prev) => {
          const plannerContent = prev.find((item) => item.role === RoleType.PLANNER)
          if (!plannerContent) {
            // 如果没有规划者的内容，创建新的
            return [
              ...prev,
              {
                id: Date.now().toString(),
                type: EventType.MESSAGE,
                role: RoleType.PLANNER,
                step: data.STEP,
                delta: data.delta,
                isLoading: true,
                fullContent: data.delta?.content || '',
              },
            ]
          } else {
            // 更新现有规划者内容
            return prev.map((item) => {
              if (item.role === RoleType.PLANNER) {
                return {
                  ...item,
                  isLoading: true, // 确保保持加载状态
                  fullContent: (item.fullContent || '') + (data.delta?.content || ''),
                }
              }
              return item
            })
          }
        })
        break

      case EventType.END_OF_LLM:
        // 规划者完成，尝试解析并渲染计划
        setContents((prev) => {
          const plannerContent = prev.find((item) => item.role === RoleType.PLANNER)
          if (plannerContent?.fullContent) {
            // 只尝试解析计划，但不在这里处理渲染，渲染会在renderContentItem中处理
            tryParseJsonPlan(plannerContent.fullContent)
          }
          return prev.map((item) => {
            if (item.role === RoleType.PLANNER) {
              return {
                ...item,
                isLoading: false,
                content: item.fullContent, // 将完整内容复制到content字段，以便渲染
              }
            }
            return item
          })
        })
        break
    }
  }

  // 处理研究者数据
  const handleResearcherData = (data: any) => {
    if (data.TYPE === EventType.MESSAGE) {
      setContents((prev) => {
        const researcherContent = prev.find((item) => item.role === RoleType.RESEARCHER)
        if (!researcherContent) {
          // 如果没有研究者的内容，创建新的
          return [
            ...prev,
            {
              id: Date.now().toString(),
              type: EventType.MESSAGE,
              role: RoleType.RESEARCHER,
              step: data.STEP,
              delta: data.delta,
              content: data.delta?.content || '',
            },
          ]
        } else {
          // 更新现有研究者内容
          return prev.map((item) => {
            if (item.role === RoleType.RESEARCHER) {
              return {
                ...item,
                content: (item.content || '') + (data.delta?.content || ''),
              }
            }
            return item
          })
        }
      })
    }
  }

  // 处理报告者数据
  const handleReporterData = (data: any) => {
    if (data.TYPE === EventType.MESSAGE) {
      setContents((prev) => {
        const reporterContent = prev.find((item) => item.role === RoleType.REPORTER)
        if (!reporterContent) {
          // 如果没有报告者的内容，创建新的
          return [
            ...prev,
            {
              id: Date.now().toString(),
              type: EventType.MESSAGE,
              role: RoleType.REPORTER,
              step: data.STEP,
              delta: data.delta,
              content: data.delta?.content || '',
              isStreaming: true, // 标记为流式内容
            },
          ]
        } else {
          // 更新现有报告者内容
          return prev.map((item) => {
            if (item.role === RoleType.REPORTER) {
              return {
                ...item,
                content: (item.content || '') + (data.delta?.content || ''),
                isStreaming: true, // 保持流式标记
              }
            }
            return item
          })
        }
      })
    } else if (data.TYPE === EventType.END_OF_LLM) {
      // 当报告者完成时，重新渲染报告区域以支持 mermaid 图表渲染
      setContents((prev) => {
        const reporterContent = prev.find((item) => item.role === RoleType.REPORTER)
        if (reporterContent && reporterContent.content) {
          const fullContent = reporterContent.content

          // 先将原始内容打印到控制台，以便于调试
          console.log('报告完成，完整内容: ', fullContent.substring(0, 100) + '...')

          // 重新使用完整内容渲染，并标记为非流式内容
          return prev.map((item) => {
            if (item.role === RoleType.REPORTER) {
              return {
                ...item,
                content: fullContent,
                isStreaming: false, // 标记为非流式内容，使mermaid图表被渲染
              }
            }
            return item
          })
        }
        return prev
      })
    }
  }

  // 处理工具调用
  const handleToolCall = (data: any) => {
    const toolName = data.tool_name
    const toolInput = data.tool_input

    if (!toolName || !toolInput) return

    try {
      // 解析工具输入
      const input = typeof toolInput === 'string' ? JSON.parse(toolInput) : toolInput

      // 根据工具类型更新活动任务
      if (toolName === 'tavily_search' && input.query) {
        setActiveTask(`${t('researchResults.searching')}: ${input.query}...`)
      } else if (toolName === 'crawl_tool' && input.url) {
        // 裁剪过长的URL，防止溢出
        const url = input.url
        let displayUrl = url
        try {
          const urlObj = new URL(url)
          const domain = urlObj.hostname
          const path = urlObj.pathname
          const shortPath = path.length > 15 ? path.substring(0, 12) + '...' : path
          displayUrl = domain + shortPath
          if (displayUrl.length > 25) {
            displayUrl = displayUrl.substring(0, 22) + '...'
          }
        } catch (e) {
          displayUrl = url.length > 25 ? url.substring(0, 22) + '...' : url
        }
        setActiveTask(`${t('researchResults.visiting')}: ${displayUrl}`)
      } else {
        setActiveTask(t('researchResults.findingInfo'))
      }
    } catch (e) {
      console.error('解析工具输入失败:', e)
      setActiveTask(t('researchResults.findingInfo'))
    }
  }

  // 发起流式请求
  const handleStreamRequest = async (questionText: string) => {
    // if (!questionText.trim()) return

    // 防止重复请求
    if (isLoading || globalRequestInProgress) {
      console.log('请求已在进行中，忽略重复请求')
      return
    }

    // 设置全局标记
    globalRequestInProgress = true

    // 重置所有状态
    setIsLoading(true)
    setContents([])
    setActiveTask('')
    setResearchPlan(null)
    setIsCompleted(false)
    researchStartTime.current = null

    // 重置步骤状态
    setResearchSteps([
      {
        id: '1',
        name: t('researchResults.requirementAnalysis'),
        agent: RoleType.PLANNER,
        status: 'waiting',
        isActive: false,
      },
      {
        id: '2',
        name: t('researchResults.initialResearch'),
        agent: RoleType.RESEARCHER,
        status: 'waiting',
        isActive: false,
      },
      {
        id: '3',
        name: t('researchResults.additionalAnalysis'),
        agent: 'browser',
        status: 'waiting',
        isActive: false,
      },
      {
        id: '4',
        name: t('researchResults.reportWriting'),
        agent: RoleType.REPORTER,
        status: 'waiting',
        isActive: false,
      },
    ])

    try {
      console.log('发起请求:', questionText)
      // 获取当前URL中的id参数
      const taskId = router.query.id as string
      const refresh = router.query.refresh === 'true' ? 'true' : 'false'
      console.log('使用task_id:', taskId)

      // 设置初始加载状态提示
      setActiveTask(t('researchResults.connecting'))

      const response = await fetch(`${API_BASE_URL}/api/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [{ role: 'user', content: questionText }],
          debug: true,
          task_id: taskId, // 添加task_id参数
          refresh: refresh,
        }),
      })

      if (!response.ok || !response.body) {
        throw new Error('网络请求失败')
      }

      console.log('收到响应，开始读取流')
      setActiveTask(t('researchResults.streamStarted'))
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      // 读取流
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          console.log('流读取完成')
          setIsCompleted(true)
          setActiveTask(t('researchResults.allCompleted'))
          setIsLoading(false)
          break
        }

        buffer += decoder.decode(value, { stream: true })
        console.log(
          '收到新的流数据',
          buffer.length > 100 ? buffer.substring(0, 100) + '...' : buffer,
        )

        // 处理缓冲区中的完整事件
        const events = buffer.split('\n\n')
        buffer = events.pop() || ''

        // 处理每个事件
        for (const event of events) {
          if (event.trim() === '') continue

          // 解析事件数据
          const lines = event.split('\n')
          let data = ''

          for (const line of lines) {
            if (line.startsWith('data:')) {
              data = line.slice(5).trim()
            }
          }

          // 处理数据
          if (data) {
            console.log('处理事件数据', data.length > 100 ? data.substring(0, 100) + '...' : data)
            processStreamData(data)
          }
        }
      }
    } catch (error) {
      console.error('请求失败:', error)
      setContents([
        {
          id: Date.now().toString(),
          type: EventType.MESSAGE,
          role: RoleType.REPORTER,
          step: StepType.ON_CHAT_MODEL_STREAM,
          content: `请求失败: ${error instanceof Error ? error.message : String(error)}`,
        },
      ])
      setIsCompleted(true)
      setActiveTask(t('researchResults.errorOccurred'))
    } finally {
      setIsLoading(false)
      globalRequestInProgress = false
    }
  }

  // 尝试解析JSON计划
  const tryParseJsonPlan = (content: string) => {
    try {
      // 确保内容是完整的JSON
      const trimmedContent = content.trim()
      if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
        const planData = JSON.parse(trimmedContent)

        // 验证是否为有效的计划数据
        if (
          planData &&
          typeof planData === 'object' &&
          planData.steps &&
          Array.isArray(planData.steps) &&
          planData.steps.length > 0
        ) {
          console.log('解析到有效的计划数据: ', researchPlan)
          setResearchPlan(planData)
        }
      }
    } catch (e) {
      console.error('解析计划数据失败:', e)
    }
  }

  // 渲染步骤状态图标
  const renderStepStatusIcon = (status: StepStatus) => {
    switch (status) {
      case 'waiting':
        return <div className='h-5 w-5 rounded-full border border-gray-300 bg-white'></div>
      case 'processing':
        return (
          <div className='flex h-5 w-5 items-center justify-center rounded-full bg-blue-500'>
            <div className='h-2 w-2 animate-ping rounded-full bg-white'></div>
          </div>
        )
      case 'completed':
        return (
          <div className='flex h-5 w-5 items-center justify-center rounded-full bg-green-500 text-white'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='h-3 w-3'
              viewBox='0 0 20 20'
              fill='currentColor'>
              <path
                fillRule='evenodd'
                d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                clipRule='evenodd'
              />
            </svg>
          </div>
        )
      default:
        return null
    }
  }

  // 获取步骤状态文字
  const getStepStatusText = (status: StepStatus, isActive: boolean) => {
    if (isActive) {
      return t('researchResults.processing')
    }

    switch (status) {
      case 'waiting':
        return t('researchResults.waiting')
      case 'processing':
        return t('researchResults.processing')
      case 'completed':
        return t('researchResults.completed')
      default:
        return ''
    }
  }

  // 渲染单个内容项
  const renderContentItem = (item: StreamContent) => {
    // 处理消息类型
    if (item.type === EventType.MESSAGE) {
      // 只显示重要代理的消息
      if (!Object.values(RoleType).includes(item.role as RoleType)) {
        return null
      }

      // 处理已折叠的消息，但永不折叠Reporter的消息
      if (item.collapsed && item.role !== RoleType.REPORTER) {
        return (
          <div
            className='mb-4 cursor-pointer rounded-lg border border-gray-100 bg-gray-50 px-4 py-3 italic text-gray-500'
            onClick={() => toggleCollapse(item.id)}>
            <div className='flex items-center'>
              <svg className='mr-2 h-4 w-4' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M19 9l-7 7-7-7'
                />
              </svg>
              <span>{t('researchResults.collapsedContent')}</span>
            </div>
          </div>
        )
      }

      // 检查是否应根据角色折叠内容
      if (
        (item.role === RoleType.PLANNER && collapsedSections.planner) ||
        (item.role === RoleType.RESEARCHER && collapsedSections.researcher)
      ) {
        return (
          <div className='mb-4 border-l-4 border-gray-300 bg-gray-50 px-4 py-3'>
            <div
              className='flex cursor-pointer items-center justify-between'
              onClick={() => toggleSectionCollapse(item.role as 'planner' | 'researcher')}>
              <span className='font-medium text-gray-700'>
                {item.role === RoleType.PLANNER && t('researchResults.plannerTitle')}
                {item.role === RoleType.RESEARCHER && t('researchResults.researcherTitle')}
              </span>
              <svg
                className='h-5 w-5 text-gray-500'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M19 9l-7 7-7-7'
                />
              </svg>
            </div>
          </div>
        )
      }

      // 添加标题组件
      const renderTitle = (role: RoleType) => (
        <div
          className={`mb-4 rounded-lg px-4 py-3 ${
            role === RoleType.REPORTER
              ? 'border-l-4 border-indigo-500 bg-indigo-50'
              : 'border-l-4 border-gray-300 bg-gray-50'
          }`}>
          <div className='flex items-center justify-between'>
            <span className='font-medium text-indigo-700'>
              {role === RoleType.PLANNER && t('researchResults.plannerTitle')}
              {role === RoleType.RESEARCHER && t('researchResults.researcherTitle')}
              {role === RoleType.REPORTER && t('researchResults.reporterTitle')}
            </span>
            {/* 为Planner和Researcher添加折叠/展开按钮 */}
            {(role === RoleType.PLANNER || role === RoleType.RESEARCHER) && (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  toggleSectionCollapse(role as 'planner' | 'researcher')
                }}
                className='text-gray-500 hover:text-indigo-700'>
                <svg className='h-5 w-5' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d={
                      collapsedSections[role === RoleType.PLANNER ? 'planner' : 'researcher']
                        ? 'M19 9l-7 7-7-7' // 向下箭头（展开）
                        : 'M9 5l7 7-7 7' // 向右箭头（折叠）
                    }
                  />
                </svg>
              </button>
            )}
          </div>
        </div>
      )

      // 获取消息内容
      const messageContent = item.content || ''

      // 复制内容到剪贴板的函数
      const copyToClipboard = () => {
        navigator.clipboard
          .writeText(messageContent)
          .then(() => {
            // 显示复制成功提示
            setCopyToast({ show: true, message: t('researchResults.copied') })
            // 3秒后自动隐藏提示
            setTimeout(() => {
              setCopyToast({ show: false, message: '' })
            }, 3000)
          })
          .catch((err) => {
            console.error('复制失败:', err)
            setCopyToast({ show: true, message: t('researchResults.copyFailed') })
            setTimeout(() => {
              setCopyToast({ show: false, message: '' })
            }, 3000)
          })
      }

      // 处理PLANNER加载中状态
      if (item.role === RoleType.PLANNER && item.isLoading) {
        return (
          <>
            {renderTitle(RoleType.PLANNER)}
            <div
              className={`relative mb-6 rounded-lg border border-gray-200 bg-white px-5 py-4 shadow-sm`}
              asm-tracking='VISIT_DEEP_RESEARCH_PAGE_MODEL:VIEW'
              asm-tracking-p-model='plan-loading'>
              <ResearchMarkdownRenderer content='' isLoading={true} role={RoleType.PLANNER} />
            </div>
          </>
        )
      }

      if (!messageContent.trim()) {
        return null
      }

      // 如果是Planner的消息并且加载完成，尝试解析JSON计划
      if (item.role === RoleType.PLANNER && !item.isLoading) {
        try {
          let trimmedContent = messageContent.trim()

          // 处理可能的 Markdown 代码块标记
          if (trimmedContent.startsWith('```json') || trimmedContent.startsWith('```')) {
            // 移除开头的 ```json 或 ```
            trimmedContent = trimmedContent.replace(/^```(?:json)?/, '').trim()
          }

          // 移除结尾的 ```
          if (trimmedContent.endsWith('```')) {
            trimmedContent = trimmedContent.replace(/```$/, '').trim()
          }

          if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
            const planData = JSON.parse(trimmedContent)

            // 验证是否为有效的计划数据
            if (
              planData &&
              typeof planData === 'object' &&
              planData.steps &&
              Array.isArray(planData.steps) &&
              planData.steps.length > 0
            ) {
              return (
                <>
                  {renderTitle(RoleType.PLANNER)}
                  <div
                    className='relative mb-6 rounded-lg border border-gray-200 bg-white px-5 py-4 shadow-sm'
                    asm-tracking='VISIT_DEEP_RESEARCH_PAGE_MODEL:VIEW'
                    asm-tracking-p-model='plan'>
                    <div className='mb-4'>
                      <h3 className='mb-2 text-lg font-medium text-gray-800'>
                        {planData.title || t('researchResults.researchPlan')}
                      </h3>
                      <p className='text-sm text-gray-600'>
                        {planData.thought || '根据您的问题，我将进行以下研究'}
                      </p>
                    </div>

                    <div className='space-y-3'>
                      <h4 className='mb-2 border-b pb-2 text-sm font-medium text-gray-700'>
                        {t('researchResults.researchSteps')}
                      </h4>
                      {planData.steps.map((step: any, index: number) => (
                        <div
                          key={index}
                          className='rounded-lg border border-gray-200 bg-gray-50 p-3'>
                          <h5 className='text-sm font-medium text-indigo-600'>
                            {index + 1}. {step.title}
                          </h5>
                          <p className='mt-1 text-xs text-gray-600'>{step.description}</p>
                          {step.note && (
                            <p className='mt-1 rounded bg-gray-100 p-1 text-xs italic text-gray-500'>
                              {t('researchResults.note')} {step.note}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* 复制按钮 */}
                    <button
                      onClick={copyToClipboard}
                      className='absolute bottom-3 right-3 text-gray-400 transition-colors duration-200 hover:text-indigo-600'
                      title='复制内容'>
                      <svg
                        className='h-5 w-5'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'>
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3'
                        />
                      </svg>
                    </button>
                  </div>
                </>
              )
            }
          }

          // 如果不是有效的JSON或解析失败，使用普通markdown渲染
          throw new Error('Not valid JSON plan')
        } catch (e) {
          console.log('消息不是有效的JSON计划，使用普通markdown渲染')
          // 如果JSON解析失败，显示原始消息内容
          return (
            <>
              {renderTitle(RoleType.PLANNER)}
              <div
                className='relative mb-6 rounded-lg border border-gray-200 bg-white px-5 py-4 shadow-sm'
                asm-tracking='VISIT_DEEP_RESEARCH_PAGE_MODEL:VIEW'
                asm-tracking-p-model='plan-failed'>
                <ResearchMarkdownRenderer
                  content={messageContent}
                  role={item.role}
                  onCopy={copyToClipboard}
                />
              </div>
            </>
          )
        }
      }

      // 渲染普通消息
      const messageClass =
        item.role === RoleType.REPORTER
          ? 'mb-6 px-5 py-4 bg-white border border-gray-200 rounded-lg shadow-sm relative'
          : 'mb-4 px-4 py-3 bg-white border border-gray-100 rounded-lg relative'

      // 在处理 REPORTER 角色渲染的地方添加调试日志
      if (item.role === RoleType.REPORTER) {
        debugLog('渲染报告', {
          content: messageContent.substring(0, 100) + '...',
          isStreaming: item.isStreaming,
          len: messageContent.length,
        })

        return (
          <>
            {/* 添加标题 */}
            {renderTitle(RoleType.REPORTER)}

            {/* 消息内容 */}
            <div
              className={messageClass}
              asm-tracking='VISIT_DEEP_RESEARCH_PAGE_MODEL:VIEW'
              asm-tracking-p-model='report'>
              <ResearchMarkdownRenderer
                content={messageContent}
                role={item.role}
                onCopy={copyToClipboard}
                isStreaming={item.isStreaming} // 传递流式状态给渲染器
              />
            </div>
          </>
        )
      }

      // 为其他角色渲染内容
      return (
        <>
          {/* 添加标题 */}
          {renderTitle(item.role as RoleType)}

          {/* 消息内容 */}
          <div
            className={messageClass}
            asm-tracking='VISIT_DEEP_RESEARCH_PAGE_MODEL:VIEW'
            asm-tracking-p-model='research'>
            <ResearchMarkdownRenderer
              content={messageContent}
              role={item.role}
              onCopy={copyToClipboard}
            />
          </div>
        </>
      )
    }

    // 忽略其他类型
    return null
  }

  // 切换内容的折叠状态
  const toggleCollapse = (id: string) => {
    setContents((prev) =>
      prev.map((item) => {
        // 如果是Reporter的消息，不允许折叠
        if (item.role === RoleType.REPORTER) {
          return item
        }
        return item.id === id ? { ...item, collapsed: !item.collapsed } : item
      }),
    )
  }

  // 添加切换折叠状态的函数
  const toggleSectionCollapse = (section: 'planner' | 'researcher') => {
    setCollapsedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  // 渲染步骤状态图标
  const renderResearchSteps = () => {
    return (
      <div className='space-y-6'>
        {/* 步骤列表 */}
        <div className='relative'>
          {/* 连接线 */}
          <div className='absolute left-[10px] top-[20px] h-[calc(100%-40px)] w-[2px] bg-gray-200'></div>

          {/* 步骤项 */}
          <div className='space-y-8'>
            {researchSteps.map((step) => (
              <div key={step.id} className='flex items-start'>
                <div className='relative z-10 mr-3 flex-shrink-0'>
                  {renderStepStatusIcon(step.status)}
                </div>
                <div className={`flex-1 ${step.isActive ? 'font-medium text-indigo-700' : ''}`}>
                  <p
                    className={`text-sm font-medium ${step.isActive ? 'text-indigo-700' : 'text-gray-700'}`}>
                    {step.name}
                  </p>
                  <p
                    className={`mt-1 text-xs ${step.isActive ? 'text-indigo-500' : 'text-gray-500'}`}>
                    {getStepStatusText(step.status, step.isActive)}
                  </p>
                  {step.description && step.isActive && (
                    <p className='mt-2 line-clamp-2 rounded border border-gray-100 bg-gray-50 p-1.5 text-xs text-gray-600'>
                      {step.description}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 当前活动状态 */}
        <div className='rounded-lg border border-indigo-100 bg-indigo-50 p-4'>
          <p className='mb-2 text-xs font-semibold text-indigo-700'>
            {t('researchResults.currentActivity')}
          </p>
          <div className='flex items-start space-x-3'>
            {isCompleted ? (
              <>
                <div className='mt-0.5 flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full bg-green-500'>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    className='h-3 w-3 text-white'
                    viewBox='0 0 20 20'
                    fill='currentColor'>
                    <path
                      fillRule='evenodd'
                      d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                      clipRule='evenodd'
                    />
                  </svg>
                </div>
                <p className='overflow-wrap-anywhere whitespace-normal break-words break-all text-sm font-medium text-indigo-900'>
                  {t('researchResults.allCompleted')}
                </p>
              </>
            ) : activeTask ? (
              <>
                <div className='mt-0.5 h-4 w-4 flex-shrink-0'>
                  <div className='h-full w-full animate-spin rounded-full border-2 border-blue-400 border-t-transparent'></div>
                </div>
                <p className='overflow-wrap-anywhere whitespace-normal break-words break-all text-sm text-indigo-900'>
                  {activeTask}
                </p>
              </>
            ) : (
              <>
                <div className='mt-0.5 h-4 w-4 flex-shrink-0 rounded-full bg-gray-300'></div>
                <p className='overflow-wrap-anywhere whitespace-normal break-words break-all text-sm text-gray-500'>
                  {t('researchResults.preparing')}
                </p>
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  // 加载状态
  if (!researchData) {
    return (
      <SimpleLayout>
        <div className='flex h-screen items-center justify-center'>
          <div className='text-center'>
            <div className='mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-indigo-500'></div>
            <p className='mt-4 text-lg text-gray-600'>{t('researchResults.loadingResults')}</p>
          </div>
        </div>
      </SimpleLayout>
    )
  }

  return (
    <>
      <Head>
        <title>{`${researchData.原始问题 || t('researchResults.researchQuestion')} | AI Smarties`}</title>
        <meta name='description' content='AI辅助研究结果' />
      </Head>

      <SimpleLayout>
        <div
          className='container mx-auto lg:px-4'
          asm-tracking='VISIT_DEEP_RESEARCH_PAGE:VIEW'
          asm-tracking-p-page='RESULT'>
          {/* 顶部固定区域 */}
          <div className='sticky top-0 z-10 mb-6 bg-white px-2 pb-4 pt-2 shadow-sm'>
            {/* 返回按钮 */}
            <button
              onClick={() => router.back()}
              className='mb-3 inline-flex items-center text-sm font-medium text-indigo-600 transition-colors transition-transform duration-200 hover:scale-105 hover:text-indigo-800'
              aria-label='返回修改'>
              <svg
                className='mr-1 h-4 w-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
                xmlns='http://www.w3.org/2000/svg'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M10 19l-7-7m0 0l7-7m-7 7h18'></path>
              </svg>
              {t('researchResults.goBack')}
            </button>

            {/* 标题区域 */}
            <div className='border-gray-200 p-4'>
              <h1 className='border-b text-2xl font-bold text-gray-900 sm:text-3xl'>
                {researchData.原始问题 || t('researchResults.researchQuestion')}
              </h1>
              {researchData.问题确认 && (
                <p className='mt-2 max-w-3xl text-sm text-gray-600'>{researchData.问题确认}</p>
              )}
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className='flex flex-col gap-6 md:flex-row'>
            {/* 左侧内容区 */}
            <div className='w-full rounded-lg border border-gray-100 bg-white p-6 shadow-lg transition-all duration-300 ease-in-out md:w-3/4'>
              <div className='prose max-w-none'>
                <h2 className='mb-6 border-b border-gray-100 pb-2 text-xl font-semibold text-gray-800'>
                  {t('researchResults.researchContent')}
                </h2>

                <div ref={outputRef} className='overflow-y-auto pr-2'>
                  {contents.length === 0 ? (
                    isLoading ? (
                      <div className='flex min-h-[400px] flex-col items-center justify-center'>
                        <div className='h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-indigo-500'></div>
                        <p className='mt-4 text-gray-500'>{t('researchResults.processingWait')}</p>
                      </div>
                    ) : (
                      <div className='flex min-h-[400px] items-center justify-center italic text-gray-400'>
                        {t('researchResults.startingAnalysis')}
                      </div>
                    )
                  ) : (
                    <div className='space-y-4'>
                      {contents.map((item) => (
                        <motion.div
                          key={item.id}
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3 }}>
                          {renderContentItem(item)}
                        </motion.div>
                      ))}

                      {isLoading && (
                        <div className='mt-4 flex items-center space-x-2 py-2 pl-2 text-gray-400'>
                          <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500'></div>
                          <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500 delay-150'></div>
                          <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500 delay-300'></div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 右侧研究步骤 */}
            <div className='w-full md:w-1/4'>
              <div className='sticky top-28 rounded-lg border border-gray-100 bg-white p-4 shadow-lg transition-all duration-300 ease-in-out'>
                <h2 className='mb-4 border-b border-gray-100 pb-2 text-lg font-semibold text-gray-800'>
                  {t('researchResults.researchProgress')}
                </h2>
                {renderResearchSteps()}
              </div>
            </div>
          </div>

          {/* 复制提示Toast */}
          {copyToast.show && (
            <div className='fixed bottom-5 right-5 z-50 flex items-center space-x-2 rounded-lg bg-primary px-4 py-2 text-white shadow-lg'>
              <svg className='h-5 w-5' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M5 13l4 4L19 7'
                />
              </svg>
              <span>{copyToast.message}</span>
            </div>
          )}
        </div>
      </SimpleLayout>
    </>
  )
}

export default ResearchResultsPage
