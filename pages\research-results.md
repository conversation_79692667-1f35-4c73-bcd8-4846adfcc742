# 研究结果页面流式内容处理逻辑分析

## 一、流片段的数据结构、数据枚举和数据示例

### 1.1 基本数据结构

```typescript
interface ResearchMessage {
  ROLE: string;       // 消息的角色
  TYPE: string;       // 消息的类型
  STEP: string;       // 消息的步骤/状态
  CONTENT: {          // 消息的具体内容
    ACTION?: string;  // 可选，动作描述
    CHAPTER?: string; // 可选，章节编号
    MESSAGE?: string; // 可选，具体消息文本
  }
}
```

### 1.2 数据枚举值

**ROLE 可能的值**:
- `WORKFLOW`: 工作流程状态
- `RESEARCHER`: 研究者角色
- `REPORTER`: 报告撰写者角色

**TYPE 可能的值**:
- `STATUS`: 状态更新
- `THINKING`: 思考过程
- `REPORTING`: 报告内容

**STEP 可能的值**:
- `START`: 开始
- `PLANNING`: 规划阶段
- `RESEARCHING`: 研究阶段
- `DRAFTING`: 草稿阶段
- `REFLECTING`: 反思阶段
- `REPORTING`: 报告阶段
- `RUNNING`: 进行中
- `END`: 结束

### 1.3 数据示例

**工作流状态更新消息**:
```json
{
  "ROLE": "WORKFLOW",
  "TYPE": "STATUS",
  "STEP": "START",
  "CONTENT": {}
}
```

**研究者思考消息**:
```json
{
  "ROLE": "RESEARCHER",
  "TYPE": "THINKING",
  "STEP": "PLANNING",
  "CONTENT": {
    "ACTION": "分析需求",
    "MESSAGE": "理解研究问题的核心是关于电动汽车的未来发展趋势"
  }
}
```

**章节内容消息**:
```json
{
  "ROLE": "RESEARCHER",
  "TYPE": "REPORTING",
  "STEP": "RUNNING",
  "CONTENT": {
    "CHAPTER": "1.1",
    "MESSAGE": "# 电动汽车市场概述\n\n电动汽车市场在过去十年中经历了显著增长..."
  }
}
```

**报告撰写消息**:
```json
{
  "ROLE": "REPORTER",
  "TYPE": "REPORTING",
  "STEP": "RUNNING",
  "CONTENT": {
    "MESSAGE": "# 电动汽车行业研究报告\n\n## 摘要\n\n本报告探讨了电动汽车行业的发展趋势..."
  }
}
```

## 二、页面内容版块与数据关系

### 2.1 左侧大纲区域

**需要的数据**:
- 初始化时从`/api/task/{id}`获取的`outline`数据
- 使用`ROLE=WORKFLOW`且`TYPE=STATUS`的消息来更新研究状态

**渲染逻辑**:
- 根据`outline`结构渲染章节大纲
- 根据最新的研究状态`researchStatus`来显示每个章节的状态指示器
- 当章节对应的消息中有`STEP=END`的消息时，显示完成状态

### 2.2 中间研究过程区

**需要的数据**:
- `ROLE=RESEARCHER`的所有消息
- 特别关注`TYPE=THINKING`的消息来展示研究过程

**渲染逻辑**:
- 按章节索引`CONTENT.CHAPTER`分组显示消息
- 对于`TYPE=THINKING`的消息，以思考过程样式展示
- 根据全局`isGlobalLoading`状态显示加载指示器

### 2.3 右侧研究报告草稿区

**需要的数据**:
- `ROLE=RESEARCHER`且`TYPE=REPORTING`的消息
- 特别关注`CONTENT.MESSAGE`中的Markdown内容

**渲染逻辑**:
- 按章节索引`CONTENT.CHAPTER`分组显示内容
- 使用Markdown渲染器展示`CONTENT.MESSAGE`
- 根据`STEP`不等于`END`的消息判断内容是否正在流式传输

### 2.4 研究状态指示器

**需要的数据**:
- `ROLE=WORKFLOW`且`TYPE=STATUS`的最新消息

**渲染逻辑**:
- 根据`STEP`的值显示不同的状态文本和颜色
- 当`STEP=END`时，关闭加载动画，显示完成状态

## 三、数据处理和渲染逻辑

### 3.1 ROLE 字段处理逻辑

**WORKFLOW**:
- 主要用于更新全局研究状态
- 当`STEP=END`时，关闭全局加载状态并显示完成提示
- 显示工作流开始或结束的提示信息

**RESEARCHER**:
- 根据`TYPE`不同分别处理:
  - `THINKING`: 显示研究者的思考过程
  - `REPORTING`: 处理章节内容的Markdown数据
- 按`CONTENT.CHAPTER`分组，构建章节内容映射

**REPORTER**:
- 主要用于处理最终研究报告内容
- 当有`TYPE=REPORTING`的消息时，合并`CONTENT.MESSAGE`作为报告内容
- 根据`STEP`判断报告是否仍在流式传输中

### 3.2 TYPE 字段处理逻辑

**STATUS**:
- 更新全局研究状态`researchStatus`
- 影响各章节状态指示器的显示

**THINKING**:
- 在研究过程区以思考样式显示
- 特别处理`CONTENT.ACTION`作为标签显示

**REPORTING**:
- 对于`RESEARCHER`角色:
  - 提取`CONTENT.MESSAGE`作为章节Markdown内容
  - 根据`STEP`判断内容是否仍在流式传输
- 对于`REPORTER`角色:
  - 合并`CONTENT.MESSAGE`作为最终报告内容

### 3.3 STEP 字段处理逻辑

**流式处理状态控制**:
- `START`/`PLANNING`/`RESEARCHING`/`DRAFTING`/`REFLECTING`/`REPORTING`/`RUNNING`: 保持全局加载状态
- `END`: 关闭全局加载状态，显示完成提示

**渲染控制**:
- 基于`STEP`值设置不同状态指示器的样式和颜色
- 用于判断章节内容是否仍在流式传输中
- 当消息`STEP=END`时，章节被标记为已完成

### 3.4 CONTENT 字段处理逻辑

**CHAPTER 处理**:
- 用于将消息按章节分组
- 构建`chapterContents`和`chapterContentItems`映射
- 与大纲中的章节索引对应

**MESSAGE 处理**:
- 对于`TYPE=THINKING`的消息，直接显示为思考过程
- 对于`TYPE=REPORTING`的消息，处理为Markdown内容
- 特别处理流式传输的Markdown内容的渲染

**ACTION 处理**:
- 主要用于`TYPE=THINKING`的消息
- 作为标签显示在思考过程中

### 3.5 流式数据合并逻辑

代码中的核心流式处理逻辑位于`fetchResearchStream`函数，主要步骤:

1. 使用`fetch`获取流数据
2. 通过`response.body.getReader()`和`TextDecoder`读取流
3. 将接收到的块添加到缓冲区，并按行分割
4. 解析以`data:`开头的行作为JSON消息
5. 立即处理状态消息(`ROLE=WORKFLOW & TYPE=STATUS`)
6. 将所有消息添加到`researchMessages`状态
7. 通过监听`researchMessages`的变化触发UI更新

后续处理函数如`updateChapterContents`、`updateChapterMarkdownActive`和`updateReportContent`会根据收集到的所有消息进行分组和处理，构建适合UI渲染的数据结构。
