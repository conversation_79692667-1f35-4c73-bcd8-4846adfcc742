import { ElementTrackingInfo, TrackingEventType, TrackingProperties } from './types'

/**
 * 获取通用属性
 * 这些属性将被添加到所有追踪事件中
 */
export const getCommonProperties = (): TrackingProperties => {
  if (typeof window === 'undefined') return {}

  return {
    url: window.location.href,
    path: window.location.pathname,
    language: document.documentElement.lang || navigator.language,
    referrer: document.referrer,
    timestamp: new Date().toISOString(),
    screen_width: window.innerWidth,
    screen_height: window.innerHeight,
    user_agent: navigator.userAgent,
  }
}

/**
 * 解析元素上的追踪参数
 * @param element HTML元素
 * @returns 追踪参数对象
 */
export const parseTrackingParams = (element: HTMLElement): TrackingProperties => {
  const params: TrackingProperties = {}

  // 获取所有以 asm-tracking-p- 开头的属性
  for (let i = 0; i < element.attributes.length; i++) {
    const attr = element.attributes[i]
    if (attr.name.startsWith('asm-tracking-p-')) {
      // 提取参数名（去掉前缀）
      const paramName = attr.name.replace('asm-tracking-p-', '')
      params[paramName] = attr.value
    }
  }

  return params
}

/**
 * 执行自定义方法获取参数
 * @param methodName 方法名
 * @param defaultParams 默认参数
 * @returns 处理后的参数
 */
export const executeCustomMethod = (
  methodName: string,
  defaultParams: TrackingProperties,
): TrackingProperties => {
  if (typeof window === 'undefined') return defaultParams

  try {
    // 获取全局对象上的方法
    const method = (window as any)[methodName]

    if (typeof method === 'function') {
      // 执行方法并返回结果
      return method(defaultParams) || defaultParams
    }
  } catch (error) {
    console.error(`[Tracking Error] Failed to execute custom method "${methodName}":`, error)
  }

  // 如果方法不存在或执行出错，返回默认参数
  return defaultParams
}

/**
 * 解析元素的追踪信息
 * @param element HTML元素
 * @returns 追踪信息或null（如果没有追踪信息）
 */
export const parseElementTrackingInfo = (element: HTMLElement): ElementTrackingInfo | null => {
  // 获取追踪属性
  const trackingAttr = element.getAttribute('asm-tracking')
  if (!trackingAttr) return null

  // 解析事件名和类型
  const [eventName, eventTypeStr] = trackingAttr.split(':')
  if (!eventName || !eventTypeStr) return null

  // 验证事件类型
  const eventType = eventTypeStr as TrackingEventType
  if (!Object.values(TrackingEventType).includes(eventType)) return null

  // 获取自定义方法名（如果有）
  const customMethod = element.getAttribute('asm-tracking-method') || undefined

  // 解析追踪参数
  const params = parseTrackingParams(element)

  return {
    eventName,
    eventType,
    customMethod,
    params,
  }
}
