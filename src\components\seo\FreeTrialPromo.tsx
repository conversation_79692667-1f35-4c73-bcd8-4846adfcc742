import { Namespace } from '@/i18n'
import React from 'react'
import { useTranslation } from 'react-i18next'

interface FreeTrialPromoProps {
  onButtonClick: (e: React.MouseEvent) => void
}

const FreeTrialPromo: React.FC<FreeTrialPromoProps> = ({ onButtonClick }) => {
  const { t } = useTranslation(Namespace.GLOBAL)
  // 处理按钮点击事件
  const handleButtonClick = (e: React.MouseEvent) => {
    // 如果提供了外部处理函数，先调用它
    if (onButtonClick) {
      onButtonClick(e)
    }
    // 然后导航到注册页面
    window.location.href = 'https://ai-smarties.com/deep-research'
  }

  return (
    <div className='relative overflow-hidden rounded-lg bg-primary bg-gradient-to-br p-6 text-white shadow-lg'>
      {/* Decorative elements */}
      <div className='absolute right-0 top-0 -mr-10 -mt-10 h-32 w-32 rounded-full bg-indigo-500 opacity-20'></div>
      <div className='absolute bottom-0 left-0 -mb-8 -ml-8 h-24 w-24 rounded-full bg-indigo-400 opacity-20'></div>

      <div className='relative z-10'>
        <h3 className='mb-2 text-2xl font-bold'>{t('freeTrial.title')}</h3>

        <div className='mb-6'>
          <p className='mb-3 font-semibold text-white'>{t('freeTrial.subTitle')}</p>
          <ul className='space-y-2'>
            <li className='flex items-center'>
              <svg
                className='mr-2 h-5 w-5 text-indigo-200'
                fill='currentColor'
                viewBox='0 0 20 20'
                xmlns='http://www.w3.org/2000/svg'>
                <path
                  fillRule='evenodd'
                  d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                  clipRule='evenodd'></path>
              </svg>
              <span className='text-sm'>{t('freeTrial.listItem1')}</span>
            </li>
            <li className='flex items-center'>
              <svg
                className='mr-2 h-5 w-5 text-indigo-200'
                fill='currentColor'
                viewBox='0 0 20 20'
                xmlns='http://www.w3.org/2000/svg'>
                <path
                  fillRule='evenodd'
                  d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                  clipRule='evenodd'></path>
              </svg>
              <span className='text-sm'>{t('freeTrial.listItem2')}</span>
            </li>
          </ul>
        </div>

        <button
          onClick={handleButtonClick}
          className='group relative w-full overflow-hidden rounded-md bg-white px-4 py-3 text-center font-semibold text-indigo-600 shadow-sm transition-all duration-300 hover:bg-indigo-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50'>
          <span className='relative z-10 inline-flex items-center text-[15px]'>
            {t('freeTrial.action')}
            <svg
              className='ml-1 h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-1'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'>
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M13 7l5 5m0 0l-5 5m5-5H6'
              />
            </svg>
          </span>
          <div className='absolute inset-0 h-full w-full -translate-x-full bg-gradient-to-r from-transparent via-indigo-100 to-transparent group-hover:animate-shimmer'></div>
        </button>
      </div>
    </div>
  )
}

export default FreeTrialPromo
