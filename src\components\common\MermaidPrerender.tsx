import React, { useEffect, useState } from 'react'
import mermaid from 'mermaid'

interface MermaidPrerenderProps {
  id: string
  chart: string
  onError?: (error: Error) => void
}

/**
 * Mermaid 预渲染组件
 * 用于初始化和预处理 Mermaid 图表
 */
const MermaidPrerender: React.FC<MermaidPrerenderProps> = ({ id, chart, onError }) => {
  const [svg, setSvg] = useState<string>('')
  const [error, setError] = useState<Error | null>(null)

  // 完全清除所有 Mermaid 错误提示的函数
  const cleanupMermaidErrors = () => {
    try {
      // 查找并移除左下角的错误提示元素 - 更多选择器以确保找到所有错误元素
      const errorSelectors = [
        '.mermaid-error-display',
        '[id^="mermaid-error-"]',
        '.error-text',
        '.error-icon',
        '.mermaid > svg > g.error-icon',
        '.mermaid-wrapper > svg > g.error-icon',
        '.bomb-error',
      ]

      errorSelectors.forEach((selector) => {
        const elements = document.querySelectorAll(selector)
        elements.forEach((el) => {
          if (el.parentNode) {
            el.parentNode.removeChild(el)
          }
        })
      })

      // 找到文档中所有包含 "Syntax error" 或 "mermaid version" 的文本节点并移除其父元素
      const allElements = document.querySelectorAll('body *')
      allElements.forEach((el) => {
        if (
          el.textContent &&
          (el.textContent.includes('Syntax error') || el.textContent.includes('mermaid version'))
        ) {
          // 获取最近的有意义的父元素
          let parent = el.parentElement
          // 向上找到一个合适的容器元素
          while (parent && parent.children.length <= 2 && parent !== document.body) {
            el = parent
            parent = parent.parentElement
          }
          // 移除找到的元素
          if (el && el.parentElement) {
            el.parentElement.removeChild(el)
          }
        }
      })

      // 移除所有 mermaid 相关的错误样式
      document.querySelectorAll('style').forEach((style) => {
        if (
          style.id &&
          style.id.startsWith('mermaid') &&
          style.textContent &&
          (style.textContent.includes('error') || style.textContent.includes('.bomb'))
        ) {
          if (style.parentNode) {
            style.parentNode.removeChild(style)
          }
        }
      })
    } catch (e) {
      console.error('清除 Mermaid 错误提示失败:', e)
    }
  }

  // 预处理图表代码，确保各种图表类型都能正确渲染
  const preprocessChartCode = (code: string): string => {
    if (!code) return code

    // 标准化图表代码（去除回车符，保留换行符）
    let cleanCode = code.trim().replace(/\r/g, '')

    // 检测图表类型
    const lowercaseCode = cleanCode.toLowerCase()

    // 状态图处理
    if (lowercaseCode.includes('statediagram')) {
      // 首先确保使用 stateDiagram-v2 而不是 stateDiagram
      if (!lowercaseCode.includes('statediagram-v2')) {
        cleanCode = cleanCode.replace(/^\s*stateDiagram\b/i, 'stateDiagram-v2')
      }

      // 确保状态名称有正确的格式 (使用引号或括号包裹含空格的状态名)
      const lines = cleanCode.split('\n')
      for (let i = 0; i < lines.length; i++) {
        // 状态转换行通常包含 --> 或 -->
        if (lines[i].includes('-->') || lines[i].includes('--&gt;')) {
          // 查找未被引号或括号包裹且包含空格的状态名
          const parts = lines[i].split(/-->|--&gt;/).map((p) => p.trim())
          for (let j = 0; j < parts.length; j++) {
            const part = parts[j]
            // 如果包含空格但不是以引号开头或括号开头，则添加引号
            if (
              part.includes(' ') &&
              !part.startsWith('"') &&
              !part.startsWith("'") &&
              !part.startsWith('[') &&
              !part.startsWith('(')
            ) {
              // 替换原始部分为带引号的版本
              lines[i] = lines[i].replace(part, `"${part}"`)
            }
          }
        }
      }
      cleanCode = lines.join('\n')
    }

    // 甘特图处理
    if (lowercaseCode.startsWith('gantt')) {
      const lines = cleanCode.split('\n')

      // 1. 确保有日期格式
      if (!lowercaseCode.includes('dateformat')) {
        const titleLineIndex = lines.findIndex((line) =>
          line.trim().toLowerCase().startsWith('title'),
        )
        const insertPosition = titleLineIndex >= 0 ? titleLineIndex + 1 : 1
        lines.splice(insertPosition, 0, 'dateFormat YYYY-MM-DD')
      }

      // 2. 确保每个任务都有正确的日期格式
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()
        // 寻找可能是任务定义的行
        if (
          line &&
          !line.startsWith('section') &&
          !line.startsWith('title') &&
          !line.startsWith('dateFormat') &&
          line.includes(':')
        ) {
          // 检查日期部分
          const parts = line.split(':')
          if (parts.length >= 2) {
            const datePart = parts[1].trim()
            // 如果日期部分没有日期格式，添加一个
            if (!datePart.match(/\d{4}-\d{2}-\d{2}/)) {
              // 使用当前日期作为开始
              const today = new Date()
              const dateStr = today.toISOString().slice(0, 10) // YYYY-MM-DD
              // 添加合适的持续时间
              lines[i] = `${parts[0]}: ${dateStr}, 3d`
            }
          }
        }
      }
      cleanCode = lines.join('\n')
    }

    // 实体关系图处理
    if (lowercaseCode.includes('erdiagram') || lowercaseCode.includes('er diagram')) {
      // 1. 确保使用正确的语法
      cleanCode = cleanCode.replace(/^(?:\s*)(?:er diagram|erdiagram)/i, 'erDiagram')

      // 2. 确保关系定义正确
      const lines = cleanCode.split('\n')
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()

        // 检查并修复关系定义行
        if (line && line.includes(' ') && !line.startsWith('%')) {
          // 识别关系定义行：通常有两个实体和关系符号
          const hasRelation =
            /\s+[|ox]?--[|ox]?\s+/.test(line) ||
            /\s+}[|ox]?--[|ox]?\{\s+/.test(line) ||
            /\s+}[|ox]?\.\.+[|ox]?\{\s+/.test(line)

          if (hasRelation && !line.includes(' : ')) {
            // 找到关系符号的位置
            const relationMatch = line.match(
              /(\s+[|ox]?--[|ox]?\s+)|(\s+}[|ox]?--[|ox]?\{\s+)|(\s+}[|ox]?\.\.+[|ox]?\{\s+)/,
            )
            if (relationMatch && relationMatch.index !== undefined) {
              // 在关系后添加标识符
              const before = line.substring(0, relationMatch.index + relationMatch[0].length)
              const after = line.substring(relationMatch.index + relationMatch[0].length)
              lines[i] = `${before}: relates to ${after}`
            }
          }
        }
      }
      cleanCode = lines.join('\n')
    }

    // XY 图表处理
    if (lowercaseCode.includes('xychart-')) {
      const lines = cleanCode.split('\n')

      // 确保有标题和x/y轴标签
      let hasTitle = false
      let hasXAxis = false
      let hasYAxis = false

      for (const line of lines) {
        if (line.trim().startsWith('title ')) hasTitle = true
        if (line.trim().startsWith('x-axis ')) hasXAxis = true
        if (line.trim().startsWith('y-axis ')) hasYAxis = true
      }

      // 如果没有标题，添加一个
      if (!hasTitle) {
        lines.splice(1, 0, 'title XY Chart')
      }

      // 如果没有x轴标签，添加一个
      if (!hasXAxis) {
        const insertPos = hasTitle ? 2 : 1
        lines.splice(insertPos, 0, 'x-axis [A, B, C, D, E]')
      }

      // 如果没有y轴标签，添加一个
      if (!hasYAxis) {
        const insertPos = hasTitle && hasXAxis ? 3 : hasTitle || hasXAxis ? 2 : 1
        lines.splice(insertPos, 0, 'y-axis "Values"')
      }

      cleanCode = lines.join('\n')
    }

    // Timeline 图表处理
    if (lowercaseCode.startsWith('timeline')) {
      const lines = cleanCode.split('\n')

      // 确保有至少一个部分和事件
      let hasSection = false
      for (const line of lines) {
        if (line.trim().startsWith('section ')) {
          hasSection = true
          break
        }
      }

      if (!hasSection) {
        lines.push('section 默认部分')
        lines.push('    项目 1 : 描述...')
      }

      cleanCode = lines.join('\n')
    }

    // Sankey 图表处理
    if (lowercaseCode.startsWith('sankey')) {
      const lines = cleanCode.split('\n')

      // 确保有至少一个流向定义
      let hasFlow = false
      for (const line of lines) {
        if (line.includes('-->')) {
          hasFlow = true
          break
        }
      }

      if (!hasFlow) {
        lines.push('A --> B')
      }

      cleanCode = lines.join('\n')
    }

    // Quadrant 图表处理
    if (lowercaseCode.startsWith('quadrant')) {
      const lines = cleanCode.split('\n')

      // 确保有标题和四个象限
      let hasTitle = false
      let quadrants = 0

      for (const line of lines) {
        if (line.trim().startsWith('title ')) hasTitle = true
        if (line.trim().startsWith('quadrant-')) quadrants++
      }

      if (!hasTitle) {
        lines.splice(1, 0, 'title 四象限分析')
      }

      // 添加缺少的象限
      if (quadrants < 4) {
        const quadrantNames = ['左上', '右上', '左下', '右下']
        for (let i = quadrants; i < 4; i++) {
          lines.push(`quadrant-${i + 1} ${quadrantNames[i]}`)
        }
      }

      cleanCode = lines.join('\n')
    }

    // Git Graph 处理
    if (lowercaseCode.startsWith('gitgraph')) {
      // 将tab替换为空格以避免格式问题
      cleanCode = cleanCode.replace(/\t/g, '    ')

      // 确保使用正确的语法版本
      if (!lowercaseCode.includes('gitgraph:')) {
        cleanCode = cleanCode.replace(/^gitgraph\s*$/i, 'gitGraph:')
      }
    }

    // Kanban 处理
    if (lowercaseCode.includes('kanban')) {
      const lines = cleanCode.split('\n')
      const result = ['---', 'displayMode: compact', '---', '']
      let kanbanLineFound = false

      // 处理每一行
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()
        if (line === 'kanban') {
          kanbanLineFound = true
          result.push('kanban')
          continue
        }

        // 检测列定义
        if (kanbanLineFound && line.startsWith('columnName:')) {
          // 已经有列定义行，添加内容
          result.push(line)
        } else if (kanbanLineFound && !line.startsWith('columnName:') && line.length > 0) {
          // 内容行，但没有列定义，添加默认列定义
          if (!result.some((l) => l.startsWith('columnName:'))) {
            result.push('columnName: 待办, 进行中, 已完成')
          }
          // 添加内容行
          result.push(line)
        } else if (line.length > 0) {
          // 其他非空行
          result.push(line)
        }
      }

      // 如果只有kanban关键字，没有其他内容
      if (kanbanLineFound && !result.some((l) => l.startsWith('columnName:'))) {
        result.push('columnName: 待办, 进行中, 已完成')
        result.push('')
        result.push('任务1: 待办')
      }

      cleanCode = result.join('\n')
    }

    console.log('预处理后的图表代码:', cleanCode)
    return cleanCode
  }

  useEffect(() => {
    // 图表代码为空时不进行处理
    if (!chart) return

    const initialize = async () => {
      try {
        // 尝试禁用 Mermaid 的错误记录器
        if (typeof mermaid.parseError === 'function') {
          const originalParseError = mermaid.parseError
          mermaid.parseError = (err, hash) => {
            // 捕获错误但不在UI中显示
            console.error('Mermaid 解析错误:', err)
            // 主动清除所有错误提示
            setTimeout(cleanupMermaidErrors, 0)
            return originalParseError(err, hash)
          }
        }

        // 强制加载所有所需的 Mermaid 模块
        try {
          // 确保所有图表类型都被预加载
          mermaid.contentLoaded()
        } catch (e) {
          console.warn('Mermaid 模块预加载失败:', e)
        }

        // 配置 mermaid - 使用更特定的配置
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'sans-serif',
          logLevel: 'error',

          // 流程图配置
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis',
          },

          // 实体关系图配置
          er: {
            useMaxWidth: true,
            layoutDirection: 'TB',
            minEntityWidth: 100,
            minEntityHeight: 75,
            entityPadding: 15,
          },

          // 时序图配置
          sequence: {
            useMaxWidth: true,
            showSequenceNumbers: false,
            mirrorActors: false,
          },

          // 甘特图配置
          gantt: {
            useMaxWidth: true,
            titleTopMargin: 25,
            barHeight: 20,
            barGap: 4,
            topPadding: 50,
            leftPadding: 75,
            gridLineStartPadding: 35,
            fontSize: 14,
            sectionFontSize: 14,
            numberSectionStyles: 3,
          },

          // 状态图配置
          state: {
            useMaxWidth: true,
            defaultRenderer: 'dagre-wrapper',
            dividerMargin: 10,
            sizeUnit: 5,
          },

          // 饼图配置
          pie: {
            useMaxWidth: true,
            textPosition: 0.75,
          },

          // XY 图表配置
          xyChart: {
            useMaxWidth: true,
          },
        })

        // 渲染前先清理可能存在的错误提示
        cleanupMermaidErrors()

        // 预处理图表代码
        const processedChart = preprocessChartCode(chart)

        // 验证图表语法
        console.log(`正在渲染 Mermaid 图表:`, processedChart)

        try {
          const { svg } = await mermaid.render(`mermaid-${id}`, processedChart)
          console.log('Mermaid 渲染成功')
          setSvg(svg)
          setError(null)
        } catch (renderError) {
          console.error('Mermaid 渲染失败, 尝试使用简化语法:', renderError)

          // 二次尝试 - 对复杂的图表使用更简单的语法
          try {
            // 对特定图表类型进行简化处理
            let simplifiedChart = processedChart

            if (processedChart.includes('stateDiagram')) {
              // 简化状态图，移除可能有问题的部分
              simplifiedChart = processedChart
                .split('\n')
                .filter((line) => !line.includes('note') && !line.includes('<<')) // 移除注释和特殊标记
                .join('\n')
            } else if (processedChart.includes('erDiagram')) {
              // 简化ER图，保留基本结构
              const lines = processedChart.split('\n')
              const header = lines[0]
              const relationships = lines
                .slice(1)
                .filter((line) => line.includes('--') || line.includes('..'))
                .slice(0, 5) // 只保留前5个关系定义

              simplifiedChart = [header, ...relationships].join('\n')
            }

            const { svg } = await mermaid.render(`mermaid-${id}-simplified`, simplifiedChart)
            console.log('Mermaid 简化渲染成功')
            setSvg(svg)
            setError(null)
          } catch (simplifiedError) {
            console.error('Mermaid 简化渲染也失败:', simplifiedError)
            throw renderError // 抛出原始错误
          }
        }

        // 即使渲染成功，也清理可能的错误
        setTimeout(cleanupMermaidErrors, 10)
      } catch (e) {
        console.error('Mermaid 渲染错误:', e)
        setError(e instanceof Error ? e : new Error('未知错误'))
        if (onError && e instanceof Error) {
          onError(e)
        }

        // 捕获到错误后，移除全局错误显示
        setTimeout(cleanupMermaidErrors, 0)
        // 多次尝试清除错误，确保延迟加载的错误也被清除
        setTimeout(cleanupMermaidErrors, 100)
        setTimeout(cleanupMermaidErrors, 500)
      }
    }

    initialize()

    // 组件卸载时清理错误提示
    return () => {
      cleanupMermaidErrors()
    }
  }, [chart, id, onError])

  // 检查元素的 className 是否包含特定字符串
  const hasClassContaining = (el: Element, substr: string): boolean => {
    // className 可能是字符串或 DOMTokenList
    if (!el.className) return false

    if (typeof el.className === 'string') {
      return el.className.includes(substr)
    }

    // 对于 SVGElement 的 className (SVGAnimatedString)
    if (
      el instanceof SVGElement &&
      el.className &&
      typeof el.className === 'object' &&
      'baseVal' in el.className
    ) {
      return el.className.baseVal.includes(substr)
    }

    // 对于 DOMTokenList (HTML元素)
    if (el instanceof HTMLElement && el.classList) {
      return Array.from(el.classList).some((cls) => cls.includes(substr))
    }

    // 最后的备选方案，转为字符串处理
    return String(el.className).includes(substr)
  }

  // 使用加强版的 MutationObserver 监听全文档变化
  useEffect(() => {
    // 创建一个观察器实例，用于监听文档变化
    const observer = new MutationObserver((mutations) => {
      let foundErrorElement = false

      for (const mutation of mutations) {
        // 检查新增节点
        if (mutation.addedNodes.length) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const el = node as Element

              // 检查是否是错误相关元素
              if (
                (el.id && el.id.includes('mermaid-error')) ||
                hasClassContaining(el, 'error') ||
                (el.textContent &&
                  (el.textContent.includes('Syntax error') ||
                    el.textContent.includes('mermaid version')))
              ) {
                foundErrorElement = true
              }
            }
          })
        }

        // 检查节点内容变化
        if (mutation.type === 'characterData' && mutation.target.textContent) {
          const text = mutation.target.textContent
          if (text.includes('Syntax error') || text.includes('mermaid version')) {
            foundErrorElement = true
          }
        }
      }

      // 如果找到了错误相关元素，执行清理
      if (foundErrorElement) {
        cleanupMermaidErrors()
      }
    })

    // 开始观察整个文档，包括属性变化
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true,
    })

    // 组件卸载时停止观察
    return () => {
      observer.disconnect()
    }
  }, [])

  // 添加更全面的全局 CSS 样式隐藏错误提示
  useEffect(() => {
    // 创建一个隐藏所有 mermaid 错误提示的样式
    const style = document.createElement('style')
    style.id = 'mermaid-error-hider'
    style.textContent = `
            /* 隐藏所有可能的错误元素 */
            [id^="mermaid-error-"],
            .mermaid-error-display,
            .error-text,
            .error-icon,
            .bomb-error,
            g.error-icon,
            g[class*="error"],
            g[class*="Error"],
            body > svg[style*="position: fixed"],
            body > div > svg[style*="position: fixed"],
            text:has(tspan:contains("Syntax error")),
            text:has(tspan:contains("mermaid version")) {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important;
                position: absolute !important;
                width: 0 !important;
                height: 0 !important;
                overflow: hidden !important;
                clip: rect(0, 0, 0, 0) !important;
            }
            
            /* 特别处理左下角的错误图标 */
            body > svg,
            body > div:not(.mermaid-svg-container) > svg {
                display: none !important;
            }

            /* 隐藏任何可能包含 Syntax error 的元素 */
            *:has(text:contains("Syntax error")),
            *:has(text:contains("mermaid version")) {
                display: none !important;
            }
        `
    document.head.appendChild(style)

    return () => {
      const styleElement = document.getElementById('mermaid-error-hider')
      if (styleElement && styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement)
      }
    }
  }, [])

  // 设置定时器定期清理错误
  useEffect(() => {
    // 首次加载时清理
    cleanupMermaidErrors()

    // 设置定时器定期检查并清理
    const intervalId = setInterval(cleanupMermaidErrors, 1000)

    return () => {
      clearInterval(intervalId)
    }
  }, [])

  // 发生错误时显示原始代码（简洁版本）
  if (error) {
    return (
      <pre className='my-3 overflow-auto rounded bg-gray-100 p-4'>
        <code className='text-sm'>{chart}</code>
      </pre>
    )
  }

  // 使用 dangerouslySetInnerHTML 渲染 SVG
  return svg ? (
    <div
      className='mermaid-svg-container max-w-full overflow-auto'
      dangerouslySetInnerHTML={{ __html: svg }}
    />
  ) : (
    <div className='mermaid-loading p-4 text-center text-gray-500'>图表加载中...</div>
  )
}

export default MermaidPrerender
