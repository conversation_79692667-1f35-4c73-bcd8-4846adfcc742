import React, { useEffect, useState } from 'react'

interface CollapsibleSectionProps {
  children: React.ReactNode
  title: string
  subtitle?: string
  storageKey: string
  className?: string
  initialExpanded?: boolean
}

export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  children,
  title,
  subtitle,
  storageKey,
  className = '',
  initialExpanded = true,
}) => {
  const [isExpanded, setIsExpanded] = useState(() => {
    if (typeof window === 'undefined') return initialExpanded
    const storedValue = localStorage.getItem(`section_${storageKey}`)
    return storedValue === null ? initialExpanded : storedValue !== 'collapsed'
  })

  useEffect(() => {
    localStorage.setItem(`section_${storageKey}`, isExpanded ? 'expanded' : 'collapsed')
  }, [isExpanded, storageKey])

  return (
    <div className={`rounded-lg bg-white shadow-sm ${className}`}>
      <div className='flex items-center justify-between p-4 pb-0'>
        <div>
          <h3 className='text-lg font-semibold text-gray-800'>{title}</h3>
          {subtitle && <p className='mt-1 text-sm text-gray-500'>{subtitle}</p>}
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className='p-1 text-gray-500 transition-colors hover:text-gray-700'
          aria-label={isExpanded ? 'Collapse section' : 'Expand section'}>
          <svg
            className={`h-4 w-4 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'>
            <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
          </svg>
        </button>
      </div>
      <div
        className={`overflow-hidden transition-all duration-200 ${isExpanded ? 'p-4' : 'p-0'}`}
        style={{
          maxHeight: isExpanded ? '2000px' : '0', // Increased max height
          opacity: isExpanded ? 1 : 0,
        }}>
        {children}
      </div>
    </div>
  )
}
