/* eslint-disable react/no-unknown-property */
import { Namespace, SupportedLangs } from '@/i18n'
import Cookies from 'js-cookie'
import Image from 'next/image'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

interface NavItem {
  label: string
  href: string
}

export interface BaseLayoutProps {
  children: React.ReactNode
}

const BaseLayout: React.FC<BaseLayoutProps> = ({ children }) => {
  const { t, i18n } = useTranslation(Namespace.GLOBAL)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isHeaderVisible, setIsHeaderVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false)
  const [languageReady, setLanguageReady] = useState(false)

  const navItems: NavItem[] = [
    { label: t('baseLayout.home'), href: '/' },
    { label: t('baseLayout.deepSearch'), href: '/deep-research' },
    { label: t('baseLayout.pricing'), href: '/pricing/' },
    { label: t('baseLayout.about'), href: '/about/' },
    // { label: 'Report Document Generator', href: '/document-generator' },
  ]

  const handleLoginClick = (e: React.MouseEvent) => {
    e.preventDefault()
    window.location.href = 'https://www.portal.ai-smarties.com/signup'
  }

  const changeLanguage = (lang: SupportedLangs) => {
    i18n.changeLanguage(lang)
    setIsLangDropdownOpen(false)
    // 可以选择将语言设置保存到localStorage或cookie
    Cookies.set('lang', lang)
  }

  // Handle scroll events for the header
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY

      // Show header in these conditions:
      // 1. Scrolling up
      // 2. At the top of the page
      // 3. Scrolled just a little bit (less than 150px)
      if (currentScrollY < 150 || currentScrollY < lastScrollY) {
        setIsHeaderVisible(true)
      } else {
        setIsHeaderVisible(false)
      }

      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  // 初始化语言设置
  useEffect(() => {
    const savedLanguage = Cookies.get('lang') as SupportedLangs
    if (savedLanguage) {
      i18n.changeLanguage(savedLanguage).then(() => {
        setLanguageReady(true)
      })
    } else {
      // 如果没有保存的语言设置，则根据浏览器语言决定
      const browserLang = navigator.language.toLowerCase()
      // 检测浏览器语言是否为简体中文或繁体中文
      if (browserLang.includes('zh')) {
        i18n.changeLanguage(SupportedLangs.ZH).then(() => {
          setLanguageReady(true)
        })
        Cookies.set('lang', SupportedLangs.ZH)
      } else {
        i18n.changeLanguage(SupportedLangs.EN).then(() => {
          setLanguageReady(true)
        })
        Cookies.set('lang', SupportedLangs.EN)
      }
    }
  }, [i18n])

  if (!languageReady) return null

  return (
    <div className='relative min-h-screen bg-gray-50 pt-16 md:pt-0'>
      {/* Header */}
      <header
        className={`fixed left-0 right-0 top-0 z-20 bg-white shadow-sm transition-transform duration-300 md:relative ${!isHeaderVisible ? '-translate-y-full md:translate-y-0' : 'translate-y-0'}`}>
        <div className='mx-auto max-w-7xl px-4'>
          <div className='flex h-16'>
            {/* Logo and Navigation Container */}
            <div className='flex min-w-0 flex-1 items-center'>
              {/* Logo */}
              <Link href='/' className='flex flex-shrink-0 items-center'>
                <Image
                  src='/images/logo.svg'
                  alt='AI Smarties'
                  width={40}
                  height={40}
                  className='h-8 w-8'
                />
                <span className='ml-2 truncate text-lg font-semibold md:text-xl'>AI Smarties</span>
              </Link>

              {/* Desktop Navigation */}
              <nav className='ml-4 hidden md:flex'>
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className='px-3 py-2 text-sm font-medium text-gray-600 transition-colors duration-150 hover:text-gray-900'>
                    {item.label}
                  </Link>
                ))}
              </nav>
            </div>

            {/* Mobile Menu Button and Signup Button */}
            <div className='flex items-center space-x-2 md:hidden'>
              {/* Language Switch (Mobile) */}
              <div className='relative'>
                <button
                  onClick={() => setIsLangDropdownOpen(!isLangDropdownOpen)}
                  className='inline-flex items-center rounded-md px-2 py-1 text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-900'>
                  {i18n.language === SupportedLangs.ZH
                    ? t('baseLayout.zhShort')
                    : t('baseLayout.enShort')}
                  <svg
                    className={`ml-1 h-4 w-4 transition-transform duration-200 ${isLangDropdownOpen ? 'rotate-180' : ''}`}
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='M19 9l-7 7-7-7'></path>
                  </svg>
                </button>
                {isLangDropdownOpen && (
                  <div className='ring-black absolute right-0 mt-2 w-32 rounded-md bg-white py-1 shadow-lg ring-1 ring-opacity-5'>
                    <button
                      onClick={() => changeLanguage(SupportedLangs.EN)}
                      className='block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100'>
                      English
                    </button>
                    <button
                      onClick={() => changeLanguage(SupportedLangs.ZH)}
                      className='block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100'>
                      中文
                    </button>
                  </div>
                )}
              </div>

              {/* Mobile Signup Button */}
              <button
                asm-tracking='CLICK_SEO_SIGNUP:CLICK'
                asm-tracking-p-view='Mobile'
                asm-tracking-p-position='Header'
                onClick={handleLoginClick}
                className='inline-flex items-center rounded-md border border-transparent bg-primary px-3 py-1.5 text-sm font-medium text-white hover:bg-indigo-700'>
                {t('baseLayout.signup')}
              </button>

              {/* Mobile Menu Toggle */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className='inline-flex items-center justify-center rounded-md p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500'
                aria-expanded='false'>
                <span className='sr-only'>Open main menu</span>
                {/* Hamburger Icon */}
                <svg
                  className={`h-6 w-6 ${isMobileMenuOpen ? 'hidden' : 'block'}`}
                  xmlns='http://www.w3.org/2000/svg'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M4 6h16M4 12h16M4 18h16'
                  />
                </svg>
                {/* Close Icon */}
                <svg
                  className={`h-6 w-6 ${isMobileMenuOpen ? 'block' : 'hidden'}`}
                  xmlns='http://www.w3.org/2000/svg'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M6 18L18 6M6 6l12 12'
                  />
                </svg>
              </button>
            </div>

            {/* Desktop Language Switcher and Login Button */}
            <div className='ml-4 hidden items-center space-x-4 md:flex'>
              {/* Language Switch (Desktop) */}
              <div className='relative'>
                <button
                  onClick={() => setIsLangDropdownOpen(!isLangDropdownOpen)}
                  className='inline-flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-900'>
                  {i18n.language === SupportedLangs.ZH
                    ? t('baseLayout.zhShort')
                    : t('baseLayout.enShort')}
                  <svg
                    className={`ml-1 h-4 w-4 transition-transform duration-200 ${isLangDropdownOpen ? 'rotate-180' : ''}`}
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='M19 9l-7 7-7-7'></path>
                  </svg>
                </button>
                {isLangDropdownOpen && (
                  <div className='ring-black absolute right-0 mt-2 w-32 rounded-md bg-white py-1 shadow-lg ring-1 ring-opacity-5'>
                    <button
                      onClick={() => changeLanguage(SupportedLangs.EN)}
                      className='block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100'>
                      English
                    </button>
                    <button
                      onClick={() => changeLanguage(SupportedLangs.ZH)}
                      className='block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100'>
                      中文
                    </button>
                  </div>
                )}
              </div>

              {/* Desktop Login Button */}
              <button
                asm-tracking='CLICK_SEO_SIGNUP:CLICK'
                asm-tracking-p-view='Desktop'
                asm-tracking-p-position='Header'
                onClick={handleLoginClick}
                className='inline-flex items-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700'>
                {t('baseLayout.signupMore')}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        <div
          className={`absolute w-full border-b border-gray-200 bg-white md:hidden ${
            isMobileMenuOpen ? 'block' : 'hidden'
          }`}>
          <div className='space-y-1 px-2 pb-3 pt-2'>
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className='block rounded-md px-3 py-2 text-base font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                onClick={() => setIsMobileMenuOpen(false)}>
                {item.label}
              </Link>
            ))}
            <button
              asm-tracking='CLICK_SEO_SIGNUP:CLICK'
              asm-tracking-p-view='Mobile'
              asm-tracking-p-position='Mobile Menu'
              onClick={handleLoginClick}
              className='block w-full rounded-md bg-primary px-3 py-2 text-center text-base font-medium text-white hover:bg-indigo-700'>
              {t('baseLayout.signupMore')}
            </button>
          </div>
        </div>
      </header>

      {/* Content */}
      {children}
      {/* Footer Section */}
      <footer className='border-t border-gray-200 bg-black-1 py-8 text-white'>
        <div className='px-8 md:px-20'>
          <div className='flex flex-col justify-between md:flex-row'>
            <div className='mb-4 md:mb-0'>
              <div className='flex items-center'>
                <Image src='/images/logo.svg' alt='Logo image' width={40} height={40} />
                <span className='ml-2 text-xl font-semibold'>AI Smarties</span>
              </div>
              <p className='mt-5 text-sm text-gray-500'>
                &copy; {new Date().getFullYear()} {t('footer.rights')}
              </p>
              <div className='mt-3 flex items-center'>
                <a
                  className='block'
                  href='https://www.youtube.com/@AISmartiesAssistant'
                  target='_blank'
                  rel='noreferrer'>
                  <Image
                    className='opacity-50'
                    src='/images/Youtube.svg'
                    alt='Youtube link logo'
                    width={30}
                    height={30}
                  />
                </a>
                <a
                  className='ml-2 block'
                  href='https://www.linkedin.com/company/ai-smarties'
                  target='_blank'
                  rel='noreferrer'>
                  <Image
                    className='opacity-50'
                    src='/images/linkedin.svg'
                    alt='LinkedIn logo'
                    width={25}
                    height={25}
                  />
                </a>
                <a
                  className='ml-2 block'
                  href='https://x.com/AISmarties01'
                  target='_blank'
                  rel='noreferrer'>
                  <Image
                    className='opacity-50'
                    src='/images/x.svg'
                    alt='X logo'
                    width={20}
                    height={20}
                  />
                </a>
              </div>
            </div>
            <div>
              <h3>{t('footer.product.title')}</h3>
              <Link className='mt-2 block text-sm text-gray-500' href='/pricing'>
                {t('footer.product.pricing')}
              </Link>
            </div>
            <div className='mt-4 md:mt-0'>
              <h3>{t('footer.about.title')}</h3>
              <Link className='mt-2 block text-sm text-gray-500' href='/about'>
                {t('footer.about.aboutUs')}
              </Link>
            </div>
            <div className='mt-4 md:mt-0'>
              <h3>{t('footer.resources.title')}</h3>
              <Link
                className='mt-2 block text-sm text-gray-500'
                href='https://portal.ai-smarties.com/policies/terms-of-use/index.html'>
                {t('footer.resources.termsOfUse')}
              </Link>
              <Link
                className='mt-2 block text-sm text-gray-500'
                href='https://portal.ai-smarties.com/policies/privacy-policy/index.html'>
                {t('footer.resources.privacy')}
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default BaseLayout
