import mixpanel from 'mixpanel-browser'

// 定义事件类型枚举
export enum TrackingEventType {
  VIEW = 'VIEW',
  CLICK = 'CLICK',
  HOVER = 'HOVER',
  SUBMIT = 'SUBMIT',
  CHANGE = 'CHANGE',
  FOCUS = 'FOCUS',
  BLUR = 'BLUR',
}

// 定义追踪属性接口
interface TrackingProperties {
  [key: string]: any
}

// 初始化 Mixpanel
// 注意：在实际使用时，你需要将 token 放在环境变量中
const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN || ''

// 只在客户端初始化 Mixpanel
if (typeof window !== 'undefined') {
  mixpanel.init(MIXPANEL_TOKEN, {
    debug: process.env.NODE_ENV !== 'production',
    track_pageview: false, // 我们会手动追踪页面浏览
    ignore_dnt: true, // 忽略 "Do Not Track"（DNT）设置
  })
}

// 获取通用属性
const getCommonProperties = (): TrackingProperties => {
  if (typeof window === 'undefined') return {}

  return {
    url: window.location.href,
    path: window.location.pathname,
    language: document.documentElement.lang || navigator.language,
    referrer: document.referrer,
    user_id: getUserId(),
    timestamp: new Date().toISOString(),
    screen_width: window.innerWidth,
    screen_height: window.innerHeight,
    user_agent: navigator.userAgent,
  }
}

// 获取用户ID (可以根据你的应用逻辑修改)
const getUserId = (): string => {
  // 这里可以从你的应用状态、localStorage、cookie等获取用户ID
  // 示例实现
  try {
    const storedUserId = localStorage.getItem('userId')
    if (storedUserId) return storedUserId

    // 如果没有用户ID，可以返回匿名ID或空字符串
    return 'anonymous'
  } catch (e) {
    return 'anonymous'
  }
}

// 解析元素上的追踪参数
const parseTrackingParams = (element: HTMLElement): TrackingProperties => {
  const params: TrackingProperties = {}

  // 获取所有以asm-tracking-p-开头的属性
  for (let i = 0; i < element.attributes.length; i++) {
    const attr = element.attributes[i]
    if (attr.name.startsWith('asm-tracking-p-')) {
      const paramName = attr.name.replace('asm-tracking-p-', '')
      params[paramName] = attr.value
    }
  }

  return params
}

// 执行自定义方法获取参数
const executeCustomMethod = (
  methodName: string,
  defaultParams: TrackingProperties,
): TrackingProperties => {
  try {
    // 尝试从window对象获取自定义方法
    const customMethod = (window as any)[methodName]
    if (typeof customMethod === 'function') {
      const result = customMethod(defaultParams)
      if (result && typeof result === 'object') {
        return result
      }
    }
  } catch (error) {
    console.error(`Error executing custom tracking method '${methodName}':`, error)
  }

  return defaultParams
}

export const Tracking = {
  /**
   * 手动追踪页面浏览事件
   */
  trackPageView: (pageName: string, test: boolean = true, properties: TrackingProperties = {}) => {
    try {
      console.info('处理埋点：', pageName, properties)
      const eventName = test ? 'TEST Page View' : 'Page View'

      // 合并通用属性和自定义属性
      const allProperties = {
        ...getCommonProperties(),
        page_name: pageName,
        ...properties,
      }

      mixpanel.track(eventName, allProperties)
    } catch (error) {
      console.error('Error tracking page view:', error)
    }
  },

  /**
   * 手动追踪自定义事件
   */
  trackEvent: (
    eventName: string,
    eventType: TrackingEventType,
    properties: TrackingProperties = {},
  ) => {
    try {
      console.info(`处理埋点：${eventName} (${eventType})`, properties)

      // 合并通用属性和自定义属性
      const allProperties = {
        ...getCommonProperties(),
        event_type: eventType,
        ...properties,
      }

      mixpanel.track(eventName, allProperties)
    } catch (error) {
      console.error(`Error tracking event ${eventName}:`, error)
    }
  },

  /**
   * 初始化自动追踪功能
   * 应在应用初始化时调用
   */
  initAutoTracking: () => {
    if (typeof window === 'undefined') return

    // 在DOM加载完成后初始化
    document.addEventListener('DOMContentLoaded', () => {
      // 处理VIEW事件
      Tracking.handleViewEvents()

      // 处理点击事件
      document.addEventListener('click', (event) => {
        Tracking.handleClickEvents(event)
      })

      // 处理悬停事件
      document.addEventListener('mouseover', (event) => {
        Tracking.handleHoverEvents(event)
      })

      // 处理表单提交事件
      document.addEventListener('submit', (event) => {
        Tracking.handleSubmitEvents(event)
      })

      // 处理输入变化事件
      document.addEventListener('change', (event) => {
        Tracking.handleChangeEvents(event)
      })

      // 处理焦点事件
      document.addEventListener(
        'focus',
        (event) => {
          Tracking.handleFocusEvents(event)
        },
        true,
      )

      // 处理失焦事件
      document.addEventListener(
        'blur',
        (event) => {
          Tracking.handleBlurEvents(event)
        },
        true,
      )

      console.info('自动埋点系统已初始化')
    })

    // 监听DOM变化，处理动态添加的元素
    if ('MutationObserver' in window) {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // 检查新添加的节点是否需要VIEW事件追踪
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1) {
                // 元素节点
                Tracking.checkViewTracking(node as HTMLElement)
              }
            })
          }
        })
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      })
    }
  },

  /**
   * 处理所有VIEW类型的事件
   */
  handleViewEvents: () => {
    // 查找所有带有asm-tracking属性且事件类型为VIEW的元素
    const elements = document.querySelectorAll('[asm-tracking]')
    elements.forEach((element) => {
      Tracking.checkViewTracking(element as HTMLElement)
    })
  },

  /**
   * 检查元素是否需要VIEW事件追踪
   */
  checkViewTracking: (element: HTMLElement) => {
    const trackingAttr = element.getAttribute('asm-tracking')
    if (!trackingAttr) return

    const [eventName, eventType] = trackingAttr.split(':')
    if (eventType?.toUpperCase() === TrackingEventType.VIEW) {
      // 收集元素上的所有追踪参数
      let properties = parseTrackingParams(element)

      // 检查是否有自定义方法
      const customMethod = element.getAttribute('asm-tracking-method')
      if (customMethod) {
        properties = executeCustomMethod(customMethod, properties)
      }

      // 触发VIEW事件
      Tracking.trackEvent(eventName, TrackingEventType.VIEW, properties)
    }
  },

  /**
   * 处理点击事件
   */
  handleClickEvents: (event: MouseEvent) => {
    let target = event.target as HTMLElement

    // 向上查找带有asm-tracking属性的最近元素
    while (target && target !== document.body) {
      const trackingAttr = target.getAttribute('asm-tracking')
      if (trackingAttr) {
        const [eventName, eventType] = trackingAttr.split(':')
        if (eventType?.toUpperCase() === TrackingEventType.CLICK) {
          // 收集元素上的所有追踪参数
          let properties = parseTrackingParams(target)

          // 检查是否有自定义方法
          const customMethod = target.getAttribute('asm-tracking-method')
          if (customMethod) {
            properties = executeCustomMethod(customMethod, properties)
          }

          // 触发CLICK事件
          Tracking.trackEvent(eventName, TrackingEventType.CLICK, properties)
        }
      }
      target = target.parentElement as HTMLElement
    }
  },

  /**
   * 处理悬停事件
   */
  handleHoverEvents: (event: MouseEvent) => {
    let target = event.target as HTMLElement

    // 向上查找带有asm-tracking属性的最近元素
    while (target && target !== document.body) {
      const trackingAttr = target.getAttribute('asm-tracking')
      if (trackingAttr) {
        const [eventName, eventType] = trackingAttr.split(':')
        if (eventType?.toUpperCase() === TrackingEventType.HOVER) {
          // 防止重复触发，使用data属性标记
          if (target.dataset.asmHoverTracked === 'true') return
          target.dataset.asmHoverTracked = 'true'

          // 收集元素上的所有追踪参数
          let properties = parseTrackingParams(target)

          // 检查是否有自定义方法
          const customMethod = target.getAttribute('asm-tracking-method')
          if (customMethod) {
            properties = executeCustomMethod(customMethod, properties)
          }

          // 触发HOVER事件
          Tracking.trackEvent(eventName, TrackingEventType.HOVER, properties)

          // 添加鼠标离开事件，重置标记
          target.addEventListener(
            'mouseleave',
            () => {
              target.dataset.asmHoverTracked = 'false'
            },
            { once: true },
          )
        }
      }
      target = target.parentElement as HTMLElement
    }
  },

  /**
   * 处理表单提交事件
   */
  handleSubmitEvents: (event: Event) => {
    const form = event.target as HTMLFormElement
    const trackingAttr = form.getAttribute('asm-tracking')

    if (trackingAttr) {
      const [eventName, eventType] = trackingAttr.split(':')
      if (eventType?.toUpperCase() === TrackingEventType.SUBMIT) {
        // 收集元素上的所有追踪参数
        let properties = parseTrackingParams(form)

        // 检查是否有自定义方法
        const customMethod = form.getAttribute('asm-tracking-method')
        if (customMethod) {
          properties = executeCustomMethod(customMethod, properties)
        }

        // 触发SUBMIT事件
        Tracking.trackEvent(eventName, TrackingEventType.SUBMIT, properties)
      }
    }
  },

  /**
   * 处理输入变化事件
   */
  handleChangeEvents: (event: Event) => {
    const target = event.target as HTMLElement
    const trackingAttr = target.getAttribute('asm-tracking')

    if (trackingAttr) {
      const [eventName, eventType] = trackingAttr.split(':')
      if (eventType?.toUpperCase() === TrackingEventType.CHANGE) {
        // 收集元素上的所有追踪参数
        let properties = parseTrackingParams(target)

        // 检查是否有自定义方法
        const customMethod = target.getAttribute('asm-tracking-method')
        if (customMethod) {
          properties = executeCustomMethod(customMethod, properties)
        }

        // 触发CHANGE事件
        Tracking.trackEvent(eventName, TrackingEventType.CHANGE, properties)
      }
    }
  },

  /**
   * 处理获取焦点事件
   */
  handleFocusEvents: (event: FocusEvent) => {
    const target = event.target as HTMLElement
    const trackingAttr = target.getAttribute('asm-tracking')

    if (trackingAttr) {
      const [eventName, eventType] = trackingAttr.split(':')
      if (eventType?.toUpperCase() === TrackingEventType.FOCUS) {
        // 收集元素上的所有追踪参数
        let properties = parseTrackingParams(target)

        // 检查是否有自定义方法
        const customMethod = target.getAttribute('asm-tracking-method')
        if (customMethod) {
          properties = executeCustomMethod(customMethod, properties)
        }

        // 触发FOCUS事件
        Tracking.trackEvent(eventName, TrackingEventType.FOCUS, properties)
      }
    }
  },

  /**
   * 处理失去焦点事件
   */
  handleBlurEvents: (event: FocusEvent) => {
    const target = event.target as HTMLElement
    const trackingAttr = target.getAttribute('asm-tracking')

    if (trackingAttr) {
      const [eventName, eventType] = trackingAttr.split(':')
      if (eventType?.toUpperCase() === TrackingEventType.BLUR) {
        // 收集元素上的所有追踪参数
        let properties = parseTrackingParams(target)

        // 检查是否有自定义方法
        const customMethod = target.getAttribute('asm-tracking-method')
        if (customMethod) {
          properties = executeCustomMethod(customMethod, properties)
        }

        // 触发BLUR事件
        Tracking.trackEvent(eventName, TrackingEventType.BLUR, properties)
      }
    }
  },
}
