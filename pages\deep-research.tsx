import React, { useCallback, useEffect, useRef, useState } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import SimpleLayout from '@/components/layouts/SimpleLayout'
import Toast from '@/components/common/Toast'
import { debounce } from '@/utils/debounce'
import { useTranslation } from 'react-i18next'
import { v4 as uuidv4 } from 'uuid'
import dynamic from 'next/dynamic'
import Cookies from 'js-cookie'
import { getCookieConsentValue } from 'react-cookie-consent'

// 使用dynamic动态导入带有SSR禁用配置的EditableTableOfContents组件
// 这是因为react-dnd需要DOM API，在服务器端渲染时会有问题
const EditableTableOfContents = dynamic(
  () => import('@/components/common/EditableTableOfContents'),
  { ssr: false },
)

// 创建一个客户端专用的包装组件
const ClientOnly = ({ children }: { children: React.ReactNode }) => {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return <>{children}</>
}

// 导入类型
import type {
  TocItem,
  EditableTableOfContentsRef,
} from '@/components/common/EditableTableOfContents'
import authRequest from '@/lib/authRequest'
import useWebSocketWithReconnection, {
  SocketMessage,
  SocketMessageStatus,
} from '@/lib/hooks/socket'
import { Namespace } from '@/i18n'

// 从环境变量中获取API基础URL，如果不存在则使用默认值
const API_BASE_URL = process.env.NEXT_PUBLIC_AUTH_API_URL || 'http://localhost:8000'

const MAX_CHARS = 4000

// Types for loading messages
type LoadingMessage = {
  id: number
  text: string
  delay: number
}

// Loading messages sequence
const loadingMessages = (t: any): LoadingMessage[] => [
  { id: 1, text: t('deepResearch.loadingMessage1'), delay: 0 },
  { id: 2, text: t('deepResearch.loadingMessage2'), delay: 3000 },
  { id: 3, text: t('deepResearch.loadingMessage3'), delay: 10000 },
]

// Confirmation loading messages
const confirmationLoadingMessages = (t: any): LoadingMessage[] => [
  { id: 0, text: t('deepResearch.confirmLoadingMessage1'), delay: 0 },
  { id: 1, text: t('deepResearch.confirmLoadingMessage2'), delay: 1000 },
  { id: 2, text: t('deepResearch.confirmLoadingMessage3'), delay: 4000 },
  { id: 3, text: t('deepResearch.confirmLoadingMessage4'), delay: 10000 },
]

// Types for toast messages
type ToastMessage = {
  message: string
  type: 'error' | 'success'
}

// 添加推荐话题的接口定义
interface SuggestedTopic {
  id: string
  displayText: {
    en: string
    zh: string
  }
  queryText: {
    en: string
    zh: string
  }
  trackingSample: string
  bgColor: string
  textColor: string
}

// 步骤指示器组件
const StepIndicator: React.FC<{
  currentStep: number
  steps: { title: string; description: string }[]
}> = ({ currentStep, steps }) => {
  return (
    <div className='mb-8 w-full'>
      <div className='flex w-full items-center'>
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            {/* 步骤标记 */}
            <div className='flex flex-col items-center'>
              <div
                className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                  index < currentStep
                    ? 'border-primary bg-primary text-white'
                    : index === currentStep
                      ? 'border-primary bg-white text-primary'
                      : 'border-gray-300 bg-white text-gray-300'
                }`}>
                {index < currentStep ? (
                  <svg className='h-6 w-6' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M5 13l4 4L19 7'
                    />
                  </svg>
                ) : (
                  <span className='text-sm font-medium'>{index + 1}</span>
                )}
              </div>
              <div className='mt-2 text-center'>
                <div
                  className={`text-sm font-medium ${
                    index <= currentStep ? 'text-primary' : 'text-gray-500'
                  }`}>
                  {step.title}
                </div>
                <div className='hidden text-xs text-gray-500 sm:block'>{step.description}</div>
              </div>
            </div>

            {/* 连接线 */}
            {index < steps.length - 1 && (
              <div
                className={`flex-auto border-t-2 ${
                  index < currentStep ? 'border-primary' : 'border-gray-300'
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  )
}

const DeepResearchPage: React.FC = () => {
  const { t, i18n } = useTranslation(Namespace.GLOBAL)
  const router = useRouter()
  const [query, setQuery] = useState<string>('')
  const [error, setError] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isConfirmLoading, setIsConfirmLoading] = useState<boolean>(false)
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false)
  const [isConfirmationStep, setIsConfirmationStep] = useState<boolean>(false)
  const [isOutlineStep, setIsOutlineStep] = useState<boolean>(false)
  const [confirmationText, setConfirmationText] = useState<string>('')
  // const [taskId, setTaskId] = useState<string>('')
  const [userLanguage, setUserLanguage] = useState<string>('和用户输入语言保持一致')
  const [outlineData, setOutlineData] = useState<any[]>([])
  const [isOutlineLoading, setIsOutlineLoading] = useState<boolean>(false)
  const [toastMessage, setToastMessage] = useState<ToastMessage | null>(null)
  const [loadingMessage, setLoadingMessage] = useState<string>('')
  const [confirmLoadingMessage, setConfirmLoadingMessage] = useState<string>('')
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const confirmInputRef = useRef<HTMLTextAreaElement>(null)
  const outlineRef = useRef<EditableTableOfContentsRef>(null)
  const taskIdRef = useRef<string>('')
  const [consent, setConsent] = useState<boolean>()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const value = getCookieConsentValue()
    setConsent(value === 'true')
  }, [])

  // 根据 URL 参数设置初始状态
  useEffect(() => {
    const { step } = router.query
    if (step === '0') {
      // 重置所有状态到第一步
      setQuery('')
      setError('')
      setIsLoading(false)
      setIsSubmitted(false)
      setIsConfirmationStep(false)
      setIsOutlineStep(false)
      setConfirmationText('')
      taskIdRef.current = ''
      setIsConfirmLoading(false)
      setOutlineData([])
      setIsOutlineLoading(false)

      // 清除 localStorage
      localStorage.removeItem('deepResearchQuery')
      localStorage.removeItem('deepResearchConfirmation')
      localStorage.removeItem('deepResearchIsConfirmationStep')
      localStorage.removeItem('deepResearchIsOutlineStep')
      localStorage.removeItem('deepResearchOutlineData')
      localStorage.removeItem('deepResearchTaskId')
    }
  }, [router.query])

  // 定义步骤信息
  const steps = [
    {
      title: t('deepResearch.steps.inputQuery.title'),
      description: t('deepResearch.steps.inputQuery.description'),
    },
    {
      title: t('deepResearch.steps.requirementAnalysis.title'),
      description: t('deepResearch.steps.requirementAnalysis.description'),
    },
    {
      title: t('deepResearch.steps.outlineConfirmation.title'),
      description: t('deepResearch.steps.outlineConfirmation.description'),
    },
  ]

  // 当前步骤
  const currentStep = isOutlineStep ? 2 : isConfirmationStep ? 1 : 0

  // 定义推荐话题数组
  const suggestedTopics: SuggestedTopic[] = [
    {
      id: 'byd',
      displayText: {
        en: 'BYD Strategy',
        zh: '比亚迪',
      },
      queryText: {
        en: 'Strategic focus of BYD in the last three years',
        zh: '比亚迪最近三年的战略重点',
      },
      trackingSample: '比亚迪',
      bgColor: 'bg-indigo-100',
      textColor: 'text-indigo-700',
    },
    {
      id: 'coffee',
      displayText: {
        en: 'Coffee Market',
        zh: '咖啡市场调研',
      },
      queryText: {
        en: "List the top 10 coffee brands worldwide by annual revenue in descending order, and provide information on each brand's annual sales, main sales channels, main coffee product categories, and online sales ratio",
        zh: '按年营业额从高到低举出10家世界范围内最大的咖啡品牌，并告诉我每个品牌的年销售额，主要销售渠道，以及主要咖啡产品类目，以及线上销售额占比',
      },
      trackingSample: '咖啡市场调研',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
    },
    {
      id: 'phantom',
      displayText: {
        en: 'Phantom Quantitative',
        zh: '幻方量化',
      },
      queryText: {
        en: 'Phantom Quantitative',
        zh: '幻方量化',
      },
      trackingSample: '幻方量化',
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
    },
  ]

  // 获取当前语言
  const currentLang = i18n.language === 'zh' ? 'zh' : 'en'

  // Calculate remaining characters
  const remainingChars = MAX_CHARS - query.length

  const handleSocketMessage = (message: SocketMessage) => {
    const { type, data, task_id } = message
    if (type !== 'confirm') {
      return
    }
    if (data.status === SocketMessageStatus.ERROR) {
      console.error(data.message)
      setIsConfirmLoading(false)
      return
    }
    if (data.status !== SocketMessageStatus.FINISH) {
      return
    }
    if (task_id !== taskIdRef.current) {
      return
    }

    const response = data.message
    // 如果合规检测通过，获取报告大纲并进入大纲确认步骤
    if (response['合规判定']) {
      setQuery(response['原始问题'])
      setConfirmationText(response['问题确认'])
      // 获取报告大纲
      if (response['报告大纲'] && Array.isArray(response['报告大纲'])) {
        // 添加id字段和level字段，使之符合EditableTableOfContents需要的格式
        const processOutlineData = (items: any[], level = 0): TocItem[] => {
          return items.map((item) => ({
            ...item,
            id: item.id || uuidv4(),
            level,
            children: item.children ? processOutlineData(item.children, level + 1) : [],
          }))
        }

        const processedOutline = processOutlineData(response['报告大纲'])
        console.log('处理后的大纲数据:', processedOutline)
        setOutlineData(processedOutline)

        // 保存到 localStorage
        localStorage.setItem('deepResearchOutlineData', JSON.stringify(processedOutline))

        // 进入大纲确认步骤
        setIsOutlineStep(true)
        setIsConfirmationStep(false) // 确保从确认步骤切换到大纲步骤
        localStorage.setItem('deepResearchIsOutlineStep', 'true')
        setIsConfirmLoading(false)
      } else {
        // 如果没有报告大纲，则直接初始化任务
        // await initTask(response);
      }
    }
  }

  const { startWebSocket, isConnected } = useWebSocketWithReconnection({
    onMessage: handleSocketMessage,
  })

  // Update loading message based on sequence
  useEffect(() => {
    if (!isLoading) return

    // 使用加载消息获取函数
    const messages = loadingMessages(t)

    // Set initial loading message
    setLoadingMessage(messages[0].text)

    const timeouts: NodeJS.Timeout[] = []

    // Skip the first message since we set it immediately
    for (let i = 1; i < messages.length; i++) {
      const msg = messages[i]
      const timeout = setTimeout(() => {
        setLoadingMessage(msg.text)
      }, msg.delay)

      timeouts.push(timeout)
    }

    return () => {
      timeouts.forEach((timeout) => clearTimeout(timeout))
    }
  }, [isLoading, t])

  // Load saved state from localStorage when component mounts
  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return

    const user_language = i18n.language === 'zh' ? '中文' : '英文'

    const savedQuery = localStorage.getItem('deepResearchQuery')
    const savedConfirmationText = localStorage.getItem('deepResearchConfirmation')
    const savedIsConfirmationStep = localStorage.getItem('deepResearchIsConfirmationStep')
    const savedIsOutlineStep = localStorage.getItem('deepResearchIsOutlineStep')
    const savedOutlineData = localStorage.getItem('deepResearchOutlineData')
    const savedTaskId = localStorage.getItem('deepResearchTaskId')
    const savedUserLanguage = localStorage.getItem('userLanguage') || user_language

    if (savedQuery) {
      setQuery(savedQuery)
    }

    if (savedConfirmationText) {
      setConfirmationText(savedConfirmationText)
    }

    if (savedTaskId) {
      taskIdRef.current = savedTaskId
    }

    console.log('lang: ', savedUserLanguage)
    setUserLanguage(savedUserLanguage)

    if (savedIsConfirmationStep === 'true') {
      setIsConfirmationStep(true)
    }

    if (savedIsOutlineStep === 'true') {
      setIsOutlineStep(true)
      // 确保从确认步骤切换到大纲步骤时，状态不冲突
      setIsConfirmationStep(false)
    }

    if (savedOutlineData) {
      try {
        const parsedData = JSON.parse(savedOutlineData)
        console.log('从localStorage加载的大纲数据:', parsedData)
        setOutlineData(parsedData)
      } catch (e) {
        console.error('Failed to parse outline data from localStorage', e)
      }
    }
  }, [i18n])

  // Update confirmation loading message based on sequence
  useEffect(() => {
    if (!isConfirmLoading) return

    // 使用确认加载消息获取函数
    const messages = confirmationLoadingMessages(t)

    // Set initial loading message
    setConfirmLoadingMessage(messages[0].text)

    const timeouts: NodeJS.Timeout[] = []

    // Skip the first message since we set it immediately
    for (let i = 1; i < messages.length; i++) {
      const msg = messages[i]
      const timeout = setTimeout(() => {
        setConfirmLoadingMessage(msg.text)
      }, msg.delay)

      timeouts.push(timeout)
    }

    return () => {
      timeouts.forEach((timeout) => clearTimeout(timeout))
    }
  }, [isConfirmLoading, t])

  // Debounced API call function
  const debouncedApiCall = useCallback(
    debounce(async (question: string) => {
      try {
        const user_language = i18n.language === 'zh' ? '中文' : '英文'

        question = `
        用户输入：
        ${question}
        `

        // 解析JSON响应
        const res = await authRequest.post(`${API_BASE_URL}/deep-research/simple`, {
          question: question,
          user_language,
        })

        await startWebSocket()

        const result = res.data.data

        const response = result.requirement
        taskIdRef.current = result.task_id
        // Check compliance status
        if (response['合规判定'] === false) {
          setToastMessage({
            message: response['问题确认'] || t('error.systemError'),
            type: 'error',
          })
          setIsLoading(false)
          return
        }

        // Save confirmation state to localStorage
        localStorage.setItem('userLanguage', user_language)
        localStorage.setItem('deepResearchTaskId', result.task_id)

        setUserLanguage(user_language)
        setIsLoading(false)
        // 使用返回的task_id进行导航
        router.push({
          pathname: '/research-results',
          query: { id: result.task_id },
        })
      } catch (err) {
        setToastMessage({
          message: err instanceof Error ? err.message : t('error.systemError'),
          type: 'error',
        })
        setIsLoading(false)
      }
    }, 300),
    [t, i18n],
  )

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!query.trim()) {
      setError(t('deepResearch.errorEnterQuery'))
      return
    }

    setError('')
    setIsLoading(true)

    // Save query to localStorage
    localStorage.setItem('deepResearchQuery', query)

    // Call the debounced API function
    debouncedApiCall(query)
  }

  // Close toast notification
  const closeToast = () => {
    setToastMessage(null)
  }

  const handleAccept = () => {
    Cookies.set('CookieConsent', 'true', { expires: 365 })
    setConsent(true)
  }

  if (!isClient) return null

  return (
    <>
      <Head>
        <title>{t('deepResearch.title')} | AI Smarties</title>
        <meta
          name='description'
          content='Use AI to perform deep research on companies and markets'
        />
        <style jsx global>{`
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: translateY(10px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .outline-container {
            min-height: 600px;
            overflow: auto;
            background-color: #fff;
            border: 1px solid #eaeaea;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
          }
        `}</style>
      </Head>

      <SimpleLayout>
        {/* 左上角标题 */}
        <div className='border-b border-gray-200 pb-4 pt-6'>
          <div className='container mx-auto px-4 sm:px-6 lg:px-8'>
            <h1 className='text-2xl font-bold text-gray-900'>{t('deepResearch.title')}</h1>
            <p className='mt-1 text-sm text-gray-500'>{t('deepResearch.description')}</p>
          </div>
        </div>

        <div
          className='flex min-h-[calc(100vh-250px)] flex-col items-center justify-center px-4 py-12 sm:px-6 lg:px-8'
          asm-tracking='VISIT_DEEP_RESEARCH_PAGE:VIEW'
          asm-tracking-p-page='QUESTION'>
          {toastMessage && (
            <Toast message={toastMessage.message} type={toastMessage.type} onClose={closeToast} />
          )}
          {/*
          {!consent && (
            <Toast
              message={
                <div className='flex items-center'>
                  <div>{t('gdpr.explain')}</div>
                  <button
                    className='whitespace-nowrap rounded bg-primary px-2 py-1 text-xs font-bold text-white'
                    onClick={() => {
                      handleAccept()
                    }}>
                    {t('gdpr.accept')}
                  </button>
                </div>
              }
              type={'error'}
              onClose={closeToast}
            />
          )}
          */}
          <div
            className='w-full max-w-5xl translate-y-0 transform space-y-8 opacity-100 transition-all duration-500 ease-in-out'
            style={{ animationName: 'fadeIn', animationDuration: '0.5s' }}>
            {/* 步骤指示器 */}
            <StepIndicator currentStep={currentStep} steps={steps} />

            {!isSubmitted && (
              <div className='mt-6 border border-gray-100 bg-white p-8 shadow-lg sm:rounded-xl'>
                <form onSubmit={handleSubmit} className='space-y-6'>
                  <div>
                    <label
                      htmlFor='research-query'
                      className='mb-2 flex items-center text-base font-medium text-gray-700'>
                      {t('deepResearch.researchQuery')}
                    </label>
                    <div className='mt-2 rounded-lg border-2 border-gray-200 bg-gray-50 transition-all duration-200 focus-within:border-indigo-500 focus-within:ring-1 focus-within:ring-indigo-500'>
                      <textarea
                        ref={inputRef}
                        id='research-query'
                        name='research-query'
                        rows={5}
                        maxLength={MAX_CHARS}
                        placeholder={t('deepResearch.placeholderText')}
                        className='block w-full rounded-lg border-0 bg-transparent px-4 py-3 text-gray-900 focus:outline-none focus:ring-0 sm:text-sm sm:leading-6'
                        value={query}
                        onChange={(e) => setQuery(e.target.value)}
                      />

                      {/* Clear button */}
                      {query && (
                        <div className='absolute right-3 top-3'>
                          <button
                            type='button'
                            onClick={() => setQuery('')}
                            className='rounded-full p-1 text-gray-400 transition-all hover:bg-gray-200 hover:text-gray-500'>
                            <span className='sr-only'>Clear input</span>
                            <svg
                              className='h-5 w-5'
                              fill='none'
                              stroke='currentColor'
                              viewBox='0 0 24 24'>
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={2}
                                d='M6 18L18 6M6 6l12 12'
                              />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Character counter and submit button */}
                    <div className='mt-2 flex items-center justify-between'>
                      <div className='flex items-center text-xs text-gray-500'>
                        <span
                          className={`${remainingChars < 20 ? 'text-red-500' : remainingChars < 50 ? 'text-yellow-500' : 'text-gray-400'}`}>
                          {remainingChars} {t('deepResearch.charactersLeft')}
                        </span>
                      </div>

                      {
                        <button
                          type='submit'
                          disabled={isLoading || !query.trim()}
                          className={`inline-flex items-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm transition-all hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${isLoading || !query.trim() ? 'cursor-not-allowed opacity-70' : 'hover:scale-105'}`}
                          asm-tracking='CLICK_DEEP_RESEARCH_STEP_BUTTON:CLICK'
                          asm-tracking-p-step='submit'>
                          {isLoading ? (
                            <>
                              <svg
                                className='mr-2 h-4 w-4 animate-spin text-white'
                                xmlns='http://www.w3.org/2000/svg'
                                fill='none'
                                viewBox='0 0 24 24'>
                                <circle
                                  className='opacity-25'
                                  cx='12'
                                  cy='12'
                                  r='10'
                                  stroke='currentColor'
                                  strokeWidth='4'></circle>
                                <path
                                  className='opacity-75'
                                  fill='currentColor'
                                  d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
                              </svg>
                              {loadingMessage}
                            </>
                          ) : (
                            <>
                              <svg
                                className='mr-1 h-4 w-4'
                                fill='none'
                                stroke='currentColor'
                                viewBox='0 0 24 24'>
                                <path
                                  strokeLinecap='round'
                                  strokeLinejoin='round'
                                  strokeWidth='2'
                                  d='M13 5l7 7-7 7M5 5l7 7-7 7'></path>
                              </svg>
                              {t('deepResearch.startResearch')}
                            </>
                          )}
                        </button>
                      }
                    </div>

                    {/* Tag suggestions */}
                    {!isLoading && (
                      <div className='mt-6 flex flex-wrap items-center gap-2'>
                        <span className='mr-1 text-sm text-gray-700'>
                          {t('deepResearch.suggestedTopics')}
                        </span>
                        {suggestedTopics.map((topic) => (
                          <button
                            key={topic.id}
                            type='button'
                            asm-tracking='CLICK_DEEP_RESEARCH_SAMPLE:CLICK'
                            asm-tracking-p-sample={topic.trackingSample}
                            onClick={() =>
                              setQuery((prev) =>
                                prev.includes('Market Analysis')
                                  ? prev
                                  : `${prev.trim() ? prev.trim() + ' ' : ''}${topic.queryText[currentLang]}`.substring(
                                      0,
                                      MAX_CHARS,
                                    ),
                              )
                            }
                            className={`inline-flex items-center rounded-full ${topic.bgColor} px-3 py-1 text-xs font-medium ${topic.textColor} transition-all hover:scale-105 hover:${topic.bgColor.replace('100', '200')}`}>
                            <svg className='mr-1 h-3 w-3' fill='currentColor' viewBox='0 0 20 20'>
                              <path
                                fillRule='evenodd'
                                d='M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z'
                                clipRule='evenodd'
                              />
                            </svg>
                            {topic.displayText[currentLang]}
                          </button>
                        ))}
                        <div className='mt-2 w-full'>
                          <p className='text-xs italic text-gray-500'>
                            {t('deepResearch.disclaimer')}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {error && (
                    <div className='mt-4 rounded-md border border-red-100 bg-red-50 p-4'>
                      <div className='flex'>
                        <div className='flex-shrink-0'>
                          <svg
                            className='h-5 w-5 text-red-400'
                            fill='currentColor'
                            viewBox='0 0 20 20'>
                            <path
                              fillRule='evenodd'
                              d='M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z'
                              clipRule='evenodd'
                            />
                          </svg>
                        </div>
                        <div className='ml-3'>
                          <p className='text-sm font-medium text-red-800'>{error}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </form>
              </div>
            )}
          </div>
        </div>
      </SimpleLayout>
    </>
  )
}

export default DeepResearchPage
