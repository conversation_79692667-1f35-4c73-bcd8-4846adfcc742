import React from 'react'

interface PromptInputProps {
  placeholderText: string
  onClick?: () => void
}

const PromptInput: React.FC<PromptInputProps> = ({ placeholderText, onClick }) => {
  return (
    // <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-10">
    //   <div className="max-w-7xl mx-auto px-4">
    //     <div className="relative flex items-center">
    //       <input
    //         type="text"
    //         disabled
    //         placeholder={placeholderText}
    //         className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg pr-12 focus:outline-none cursor-pointer"
    //         onClick={onClick}
    //       />
    //       <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
    //         <span className="text-xs text-gray-400">AI 可能会出错，请检查重要信息。</span>
    //       </div>
    //     </div>
    //   </div>
    // </div>

    <div className='relative flex items-center'>
      <textarea
        rows={2}
        placeholder={placeholderText}
        className='w-full cursor-pointer rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 pr-12 focus:outline-none'
        onClick={onClick}
      />
    </div>
  )
}

export default PromptInput
