import { create } from 'zustand'
import { setLangToCookies } from '@/lib/cookie'

export interface User {
  userId: string
  name: string
  email: string
  setting: {
    language: string
  }
}
//
interface UserStore {
  user: User | undefined | null // User can be undefined or null
  updateUser: (updatedUser: User) => void
  clearUser: () => void
}

export const useUserStore = create<UserStore>((set) => ({
  user: null, // Initialize as null
  updateUser: (updatedUser) => {
    set({ user: updatedUser }) // Directly update the user
    setLangToCookies(updatedUser.setting.language)
  },
  clearUser: () => set({ user: null }), // Clear user info
}))
