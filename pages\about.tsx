import React from 'react'
import Head from 'next/head'
import Link from 'next/link'
import BaseLayout from '@/components/layouts/BaseLayout'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

interface PainPointProps {
  text: string
}

const PainPoint: React.FC<PainPointProps> = ({ text }) => {
  return (
    <motion.div
      className='mb-4 flex items-start space-x-3'
      initial={{ opacity: 0, x: -20 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}>
      <div className='mt-0.5 rounded-full bg-red-100 p-1'>
        <svg
          className='h-4 w-4 text-red-600'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
          xmlns='http://www.w3.org/2000/svg'>
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M6 18L18 6M6 6l12 12'
          />
        </svg>
      </div>
      <p className='text-gray-700'>{text}</p>
    </motion.div>
  )
}

interface ValuePointProps {
  title: string
  description: string
}

const ValuePoint: React.FC<ValuePointProps> = ({ title, description }) => {
  return (
    <motion.div
      className='mb-6'
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}>
      <h3 className='mb-2 text-lg font-bold text-gray-900'>{title}</h3>
      <p className='text-gray-700'>{description}</p>
    </motion.div>
  )
}

const AboutPage: React.FC = () => {
  const { t } = useTranslation(Namespace.ABOUT)

  return (
    <>
      <Head>
        <title>About Us | AI-Smarties</title>
        <meta name='description' content={t('visionDescription')} />
        <meta property='og:title' content='About AI-Smarties' />
        <meta property='og:description' content={t('visionDescription')} />
        <meta property='og:type' content='website' />
      </Head>

      <section className='relative overflow-hidden bg-gradient-to-r from-gray-900 to-gray-800 py-24'>
        <div className='bg-primary/20 absolute -left-20 -top-20 h-96 w-96 rounded-full blur-3xl'></div>
        <div className='absolute -bottom-20 -right-20 h-96 w-96 rounded-full bg-blue-500/20 blur-3xl'></div>

        <div className='relative z-10 mx-auto max-w-5xl px-4 text-center sm:px-6 lg:px-8'>
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className='mb-6 text-4xl font-bold text-white md:text-5xl lg:text-6xl'>
            {t('visionTitle')}
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className='mx-auto mb-8 max-w-3xl text-xl leading-relaxed text-white/80'>
            {t('visionDescription')}
          </motion.p>
        </div>
      </section>

      <section className='bg-gray-50 py-20'>
        <div className='mx-auto max-w-5xl px-4 sm:px-6 lg:px-8'>
          <motion.div
            className='mx-auto mb-16 text-center'
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}>
            <h2 className='mb-4 text-3xl font-bold text-gray-900'>{t('buildProductTitle')}</h2>
            <p className='mx-auto max-w-3xl text-xl italic text-gray-700'>
              {t('buildProductQuote')}
            </p>
          </motion.div>

          <div className='grid items-center gap-10 md:grid-cols-2'>
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}>
              <div className='flex aspect-video items-center justify-center rounded-2xl bg-gradient-to-br from-primary to-blue-600 shadow-xl'>
                <svg
                  className='h-24 w-24 text-white/80'
                  fill='currentColor'
                  viewBox='0 0 20 20'
                  xmlns='http://www.w3.org/2000/svg'>
                  <path
                    fillRule='evenodd'
                    d='M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z'
                    clipRule='evenodd'
                  />
                </svg>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}>
              <h3 className='mb-4 text-2xl font-bold text-gray-900'>{t('teamTitle')}</h3>
              <p className='mb-6 text-gray-700'>{t('teamDescription1')}</p>
              <p className='text-gray-700'>{t('teamDescription2')}</p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className='py-20'>
        <div className='mx-auto max-w-5xl px-4 sm:px-6 lg:px-8'>
          <motion.div
            className='mb-16 text-center'
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}>
            <h2 className='mb-4 text-3xl font-bold text-gray-900'>{t('challengeTitle')}</h2>
            <p className='mx-auto max-w-3xl text-lg text-gray-700'>{t('challengeDescription')}</p>
          </motion.div>

          <div className='mb-16 rounded-2xl bg-white p-8 shadow-xl'>
            <div className='grid gap-6 md:grid-cols-2'>
              <div>
                <PainPoint text={t('painPoint1')} />
                <PainPoint text={t('painPoint2')} />
                <PainPoint text={t('painPoint3')} />
              </div>
              <div>
                <PainPoint text={t('painPoint4')} />
                <PainPoint text={t('painPoint5')} />
                <PainPoint text={t('painPoint6')} />
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className='bg-gradient-to-b from-gray-50 to-white py-20'>
        <div className='mx-auto max-w-5xl px-4 sm:px-6 lg:px-8'>
          <motion.div
            className='mb-16 text-center'
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}>
            <h2 className='mb-4 text-3xl font-bold text-gray-900'>
              {t('valueTitle').split('AI Smarties')[0]}
              <span className='text-primary'>AI Smarties</span>
            </h2>
            <p className='mx-auto max-w-3xl text-lg text-gray-700'>{t('valueDescription')}</p>
          </motion.div>

          <div className='grid gap-x-10 gap-y-6 md:grid-cols-2'>
            <ValuePoint title={t('value1Title')} description={t('value1Desc')} />
            <ValuePoint title={t('value2Title')} description={t('value2Desc')} />
            <ValuePoint title={t('value3Title')} description={t('value3Desc')} />
            <ValuePoint title={t('value4Title')} description={t('value4Desc')} />
          </div>

          <motion.div
            className='mt-12 rounded-2xl border border-gray-100 bg-white p-6 shadow-lg'
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}>
            <div className='flex flex-col items-center justify-between md:flex-row'>
              <div className='mb-6 md:mb-0 md:mr-6'>
                <h3 className='mb-2 text-2xl font-bold text-gray-900'>{t('affordabilityTitle')}</h3>
                <p className='text-gray-700'>{t('affordabilityDesc')}</p>
              </div>
              <div className='flex-shrink-0'>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 10 }}>
                  <Link
                    href='/deep-research'
                    className='inline-block rounded-lg bg-primary px-6 py-3 text-center text-base font-medium text-white shadow-md transition-all duration-300 hover:-translate-y-1 hover:bg-primary-dark hover:shadow-lg'>
                    {t('tryButton')}
                  </Link>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  )
}

export default AboutPage
