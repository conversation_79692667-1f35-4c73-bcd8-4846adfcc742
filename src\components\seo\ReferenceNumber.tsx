import React, { useEffect, useRef, useState } from 'react'

interface Citation {
  page_id: string
  url: string
  title: string
  text: string
}

interface Citations {
  [key: string]: Citation
}

interface ReferenceNumberProps {
  id: string
  number: string | number
  className?: string
  style?: React.CSSProperties
  citations?: Citations
}

export const ReferenceNumber: React.FC<ReferenceNumberProps> = ({
  id,
  number,
  className = '',
  style = {},
  citations = {},
}) => {
  const [showTooltip, setShowTooltip] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 })
  const tooltipRef = useRef<HTMLDivElement>(null)
  // console.log('Citations:', citations)

  // console.log('citation id:', id);
  const citation = citations[id]

  // Debug log
  useEffect(() => {
    if (showTooltip) {
      console.log('Current citation:', citation)
      console.log('Reference id:', id)
    }
  }, [showTooltip, citations, citation, id])

  const handleMouseMove = (e: React.MouseEvent) => {
    if (tooltipRef.current) {
      const tooltipWidth = tooltipRef.current.offsetWidth
      const tooltipHeight = tooltipRef.current.offsetHeight
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      // Calculate position to keep tooltip within viewport
      let x = e.clientX + 10 // 10px offset from cursor
      let y = e.clientY + 10

      if (x + tooltipWidth > viewportWidth) {
        x = e.clientX - tooltipWidth - 10
      }
      if (y + tooltipHeight > viewportHeight) {
        y = e.clientY - tooltipHeight - 10
      }

      setTooltipPosition({ x, y })
    }
  }

  const handleClick = () => {
    if (citation?.url) {
      window.open(citation.url.replaceAll('"', ''), '_blank')
    }
  }

  return (
    <span
      className={`relative cursor-pointer ${className}`}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#818cf8',
        color: 'white',
        borderRadius: '50%',
        width: '14px',
        height: '14px',
        fontSize: '12px',
        marginLeft: '4px',
        marginRight: '4px',
        marginTop: '-5px',
        ...style,
      }}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
      onMouseMove={handleMouseMove}
      onClick={handleClick}>
      {number}
      {showTooltip && citation && (
        <div
          ref={tooltipRef}
          className='fixed z-50 max-w-md rounded-lg border border-gray-200 bg-white p-4 shadow-lg'
          style={{
            left: tooltipPosition.x,
            top: tooltipPosition.y,
          }}>
          <h4 className='mb-2 font-semibold text-primary'>{citation.title}</h4>
          <div className='text-sm text-gray-600'>{citation.text}</div>
        </div>
      )}
    </span>
  )
}

export default ReferenceNumber
