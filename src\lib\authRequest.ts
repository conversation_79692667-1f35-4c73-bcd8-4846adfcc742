import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosError, AxiosResponse } from 'axios'
import { v4 as uuidv4 } from 'uuid'
import { getCookie, setCookie, smartiesDomain } from './cookie'

interface AnonymousLoginResponse {
  accessToken: string
}

const authRequest: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
})

const cookieGuard = (cookie: { anonymousId?: string }) => {
  if (!getCookie('anonymousId') && cookie.anonymousId) {
    setCookie('anonymousId', cookie.anonymousId, {
      httpOnly: false,
      domain: smartiesDomain,
    })
  }
}
// 请求拦截器：每次请求都附上 token，如果没有就匿名登录获取
authRequest.interceptors.request.use(
  async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
    if (typeof window === 'undefined') return config

    let token = getCookie('smartToken') || localStorage.getItem('accessToken')
    let anonymousId = localStorage.getItem('anonymousId')
    if (!token) {
      if (!anonymousId) {
        anonymousId = uuidv4()
        localStorage.setItem('anonymousId', localStorage.getItem('anonymousId') || anonymousId)
      }
      cookieGuard({ anonymousId })
      try {
        const res = await axios.post(
          `${process.env.NEXT_PUBLIC_AUTH_API_URL}/auth/loginAnonymous`,
          {
            anonymousId,
          },
        )
        const data: AnonymousLoginResponse = res.data
        token = data.accessToken
        localStorage.setItem('accessToken', token)
      } catch (err) {
        console.error('匿名登录失败:', err)
      }
    }

    if (!config.headers.Authorization && token) {
      cookieGuard({ anonymousId: anonymousId || '' })
      config.headers = config.headers || {}
      config.headers['Authorization'] = `Bearer ${token}`
    }

    return config
  },
  (error: AxiosError) => Promise.reject(error),
)

// 响应拦截器：出错时重新获取 token 并重试一次
authRequest.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    // 非认证错误不重试
    if (error.status === 503) {
      const serviceError = {
        code: 'SERVICE_UNAVAILABLE',
        messageKey: 'error.systemError',
        originalError: error,
      }
      return Promise.reject(serviceError)
    }
    if (error.status !== 401 && error.status !== 403) {
      return Promise.reject(error)
    }
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retried?: boolean }

    if (typeof window === 'undefined') return Promise.reject(error)

    // 如果已经重试过，就不再重试，防止死循环
    if (originalRequest._retried) {
      return Promise.reject(error)
    }

    // 如果auth error, 并且存在smartToken cookie，则删除smartToken cookie
    if (getCookie('smartToken')) {
      setCookie('smartToken', '', {
        httpOnly: false,
        domain: smartiesDomain,
        maxAge: 0,
      })
    }

    originalRequest._retried = true

    let anonymousId = localStorage.getItem('anonymousId')
    if (!anonymousId) {
      anonymousId = uuidv4()
      localStorage.setItem('anonymousId', localStorage.getItem('anonymousId') || anonymousId)
    }
    cookieGuard({ anonymousId })
    try {
      const res = await axios.post(`${process.env.NEXT_PUBLIC_AUTH_API_URL}/auth/loginAnonymous`, {
        anonymousId,
      })
      const data: AnonymousLoginResponse = res.data
      const newToken = data.accessToken
      localStorage.setItem('accessToken', newToken)

      // 更新 Authorization header
      originalRequest.headers = originalRequest.headers || {}
      originalRequest.headers['Authorization'] = `Bearer ${newToken}`

      cookieGuard({ anonymousId })
      // 重试原始请求
      return authRequest(originalRequest)
    } catch (retryError) {
      console.error('重新获取 token 失败:', retryError)
      localStorage.removeItem('accessToken')
      return Promise.reject(retryError)
    }
  },
)

export default authRequest
