import axios from 'axios'

export enum GeneratorStatus {
  INIT = 'INIT', // 初始状态
  WAITING = 'WAITING', // 等待生成
  CREATING = 'CREATING', // 生成中
  FINISH = 'FINISH', // 生成结束
  CANCELED = 'CANCELED', // 取消生成文档
  FAILED = 'FAILED', // 失败
}

export enum ReportTypeEnum {
  EMPTY = 'EMPTY',
  BUSINESS_PLAN = 'BUSINESS_PLAN',
  COMPANY = 'COMPANY',
}

export interface ReportItemType {
  content?: string
  createdAt: number
  description: string
  reportId: string
  status: GeneratorStatus
  title: string
  userId: string
  type: ReportTypeEnum | null
  updatedAt: number
  documentGeneratorStatus?: GeneratorStatus | undefined
}

export const getReportById = async (data: { reportId: string }) => {
  const s3Url = `${process.env.NEXT_PUBLIC_AWS_SHARE_S3_URL}/${data.reportId}.json`

  const response = await axios.get(s3Url)

  return response.data
}
