import { getSessionById } from '@/api/getSessionById'
import Loading from '@/components/common/Loading'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/common/Resizable'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/Select'
import BaseLayout from '@/components/layouts/BaseLayout'
import MarkdownParser from '@/components/markdown/MarkdownParser'
import CallToAction from '@/components/seo/CallToAction'
import { Namespace } from '@/i18n'
import { Message, MessageRoleEnum } from '@/types'
import clsx from 'clsx'
import { throttle } from 'lodash'
import { ChevronLeftIcon, ChevronRightIcon, DownloadIcon } from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/esm/Page/AnnotationLayer.css'
import 'react-pdf/dist/esm/Page/TextLayer.css'

pdfjs.GlobalWorkerOptions.workerSrc =
  'https://d1ij1j35k83uw7.cloudfront.net/pdfjs/3.11.174/pdf.worker.min.js'

const PDFOptions = {
  cMapUrl: 'https://d1ij1j35k83uw7.cloudfront.net/pdfjs/3.11.174/cmaps/',
  cMapPacked: true,
  Worker: 'https://d1ij1j35k83uw7.cloudfront.net/pdfjs/3.11.174/pdf.worker.min.js',
}

const MAX_PDF_PAGES = 200

const PdfPreviewPage = () => {
  const { t } = useTranslation(Namespace.SHARE)
  const router = useRouter()
  const { pdfName, pdfId } = router.query
  const [s3Url, setS3Url] = useState('')
  const [loading, setLoading] = useState(false)

  const [totalPages, setTotalPages] = useState<number>(Math.min(MAX_PDF_PAGES, 0))
  const pageRefs = useRef<(HTMLDivElement | null)[]>(Array(totalPages).fill(null))
  const [inputPage, setInputPage] = useState<number>(1)
  const isProgrammaticScroll = useRef(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const [scale, setScale] = useState<number | undefined>(undefined)
  const [wid, setWid] = useState<number | undefined>(undefined)
  const [isAutoScale, setIsAutoScale] = useState<boolean>(true)
  const containerRef = useRef<HTMLDivElement>(null)
  const [messages, setMessages] = useState<Array<Message>>([])

  const scrollToView = (page: number) => {
    isProgrammaticScroll.current = true
    if (pageRefs.current[page - 1] && page !== null) {
      pageRefs.current[page - 1]?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest',
      })
    }
  }

  const getSession = async () => {
    try {
      setLoading(true)
      const res = await getSessionById({ sessionId: pdfId as string })
      if (res) {
        const message = res.content.map((item: Message, index: number) => ({
          ...item,
          key: index + item.role,
          reasoningExpand: false,
        }))
        setMessages(message)
        setS3Url(res.s3Url)
        setLoading(false)
      }
    } catch (e) {
      console.error(e)
      setLoading(false)
    }
  }

  // 滚动时更新当前显示的页码
  useEffect(() => {
    const handleScroll = () => {
      // 如果是程序化滚动，不执行 handleScroll 逻辑
      if (isProgrammaticScroll.current) {
        return
      }
      if (pageRefs.current.length > 0 && containerRef.current) {
        const containerTop = containerRef.current.getBoundingClientRect().top

        // 找到最接近顶部的页面
        let closestPageIndex = -1
        let minDistance = Infinity

        pageRefs.current.forEach((pageRef, idx) => {
          if (pageRef) {
            const pageTop = pageRef.getBoundingClientRect().top
            const distance = Math.abs(pageTop - containerTop)
            if (distance < minDistance) {
              minDistance = distance
              closestPageIndex = idx
            }
          }
        })
        // 更新页码
        if (closestPageIndex !== -1 && closestPageIndex + 1 !== inputPage) {
          setInputPage(closestPageIndex + 1)
        }
      }
    }

    const throttledHandleScroll = throttle(handleScroll, 50) // 节流处理
    const containerEl = containerRef.current
    containerEl?.addEventListener('scroll', throttledHandleScroll)

    // 清理事件监听器
    return () => containerEl?.removeEventListener('scroll', handleScroll)
  }, [])

  // s3Url变换后 页面重置
  useEffect(() => {
    setInputPage(1)
  }, [s3Url])

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth
        const wid = containerWidth - 32 // 根据父容器宽度计算缩放比例
        setWid(wid > 0 ? wid : undefined)
      }
    }

    if (isAutoScale) {
      handleResize() // 初始化缩放

      const resizeObserver = new ResizeObserver(() => {
        handleResize()
      })

      if (containerRef.current) {
        resizeObserver.observe(containerRef.current)
      }

      return () => {
        if (containerRef.current) {
          resizeObserver.unobserve(containerRef.current)
        }
        resizeObserver.disconnect()
      }
    }
  }, [isAutoScale])

  const handleScaleChange = (value: string) => {
    if (value === 'auto') {
      setIsAutoScale(true)
      setScale(undefined)
    } else {
      setIsAutoScale(false)
      setScale(Number(value))
    }
  }

  const handleClickDownLoad = async (s3Url: string) => {
    try {
      const response = await fetch(s3Url, {
        method: 'GET',
        mode: 'cors',
      })
      if (!response.ok) {
        throw new Error('Network response was not ok')
      }

      const blob = await response.blob()
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = (pdfName as string) || s3Url.split('/').pop() || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setTotalPages(numPages)
  }

  useEffect(() => {
    if (pdfId) {
      getSession()
    }
  }, [pdfId])

  if (!router.isReady) {
    return null
  }

  return (
    <>
      {/* Main Content */}
      <main>
        <div className='mx-auto max-w-7xl bg-background px-2 sm:px-4 md:max-w-full'>
          <ResizablePanelGroup direction='horizontal'>
            {/* Left Content Area */}
            <ResizablePanel defaultSize={70} minSize={20} className='relative'>
              <div className='w-full flex-1'>
                <div className='lg:px-12'>
                  <>
                    <div
                      onMouseLeave={() => {
                        isProgrammaticScroll.current = false
                      }}>
                      <div className='flex items-center justify-between py-2 text-sm'>
                        <div className='font-bold'>{pdfName ? `${pdfName}.pdf` : ''}</div>
                        <div className='mr-4 flex'>
                          <div className='mr-2 flex h-6 cursor-pointer items-center justify-center gap-1 rounded bg-card px-2 py-1'>
                            <DownloadIcon className='h-4 w-4' />
                            <div
                              className='min-w-8'
                              onClick={() => {
                                handleClickDownLoad(s3Url ?? '')
                              }}>
                              {t('pdf.download')}
                            </div>
                          </div>
                          <Select onValueChange={handleScaleChange} defaultValue='auto'>
                            <SelectTrigger className='h-6 rounded border-none bg-card'>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup className='cursor-pointer'>
                                <SelectItem value='auto' className='cursor-pointer'>
                                  {t('pdf.auto')}
                                </SelectItem>
                                <SelectItem value='0.5' className='cursor-pointer'>
                                  50%
                                </SelectItem>
                                <SelectItem value='0.8' className='cursor-pointer'>
                                  80%
                                </SelectItem>
                                <SelectItem value='1' className='cursor-pointer'>
                                  100%
                                </SelectItem>
                                <SelectItem value='1.5' className='cursor-pointer'>
                                  150%
                                </SelectItem>
                                <SelectItem value='2' className='cursor-pointer'>
                                  200%
                                </SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                    <div className='h-screen-minus-2-header overflow-auto' ref={containerRef}>
                      <Document
                        options={PDFOptions}
                        onLoadSuccess={onDocumentLoadSuccess}
                        onLoadError={(err) => console.error('PDF load error:', err)}
                        file={s3Url}
                        noData={null}
                        loading={<Loading />}>
                        {Array.from(new Array(totalPages), (item, i) => (
                          <div
                            key={i}
                            className='mb-2'
                            ref={(el) => {
                              pageRefs.current[i] = el
                            }}>
                            <Page
                              pageNumber={i + 1}
                              renderTextLayer={false}
                              scale={scale}
                              width={wid}
                              loading={null}
                            />
                          </div>
                        ))}
                      </Document>
                      <div
                        className='absolute bottom-2 left-1/2 z-50 flex -translate-x-1/2 items-center justify-center'
                        onMouseLeave={() => {
                          isProgrammaticScroll.current = false
                        }}>
                        <ChevronLeftIcon
                          className='my-2 mr-4 h-4 cursor-pointer'
                          onClick={() => {
                            inputRef.current?.focus()
                            setInputPage(Math.max(inputPage - 1, 1))
                            scrollToView(Math.max(inputPage - 1, 1))
                          }}
                        />
                        <input
                          ref={inputRef}
                          className={clsx(
                            'my-1 mr-2 flex h-6 w-8 items-center justify-center rounded border bg-card',
                            'ring-offset-transparent focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0',
                          )}
                          value={inputPage}
                          maxLength={999}
                          onChange={(e) => {
                            const onlyNums = e.target.value.replace(/[^0-9]/g, '')
                            if (Number(onlyNums) <= MAX_PDF_PAGES) {
                              setInputPage(() => Number(onlyNums))
                              scrollToView(Number(onlyNums))
                            } else {
                              setInputPage(() => MAX_PDF_PAGES)
                              scrollToView(MAX_PDF_PAGES)
                            }
                          }}
                        />
                        / {totalPages || 0}
                        <ChevronRightIcon
                          className='ml-4 h-4 cursor-pointer'
                          onClick={() => {
                            inputRef.current?.focus()
                            setInputPage(Math.min(inputPage + 1, totalPages!))
                            scrollToView(Math.min(inputPage + 1, totalPages!))
                          }}
                        />
                      </div>
                    </div>
                  </>
                </div>
              </div>
            </ResizablePanel>
            {messages.length > 0 && (
              <ResizableHandle
                withHandle
                className='hidden w-[2px] bg-border hover:bg-primary active:bg-primary lg:inline-flex'
              />
            )}
            {messages.length > 0 && (
              <ResizablePanel defaultSize={30} minSize={30} className='hidden lg:block'>
                <DocChat messages={messages} loading={loading} />
              </ResizablePanel>
            )}
          </ResizablePanelGroup>
          <div className='block lg:hidden'>
            <DocChat messages={messages} loading={loading} />
          </div>
          <div className='flex items-center justify-center py-12'>
            <CallToAction />
          </div>
        </div>
      </main>
    </>
  )
}

const DocChat = ({ messages, loading }: { messages: Array<Message>; loading: boolean }) => {
  return (
    <div className='h-screen-minus-2-header overflow-auto'>
      {loading ? (
        <Loading />
      ) : (
        messages.map((item, index) => {
          return (
            <div className='flex p-3' key={index}>
              <div className='shrink-0'>
                <Image
                  src={
                    item.role === MessageRoleEnum.ASSISTANT
                      ? '/images/system_avatar.svg'
                      : '/images/default_avatar.svg'
                  }
                  width={32}
                  height={32}
                  className='mr-3 rounded-lg'
                  alt={'avatar'}
                />
              </div>
              {item.role === MessageRoleEnum.USER ? (
                <div>
                  <MarkdownParser content={item.content} />
                </div>
              ) : (
                <div className='w-[calc(100%-32px)] flex-1'>
                  <MarkdownParser content={item.content} />
                </div>
              )}
            </div>
          )
        })
      )}
    </div>
  )
}

export default PdfPreviewPage
