import React, { <PERSON>actNode, useEffect, useState } from 'react'
import { ChevronDown, ChevronRight } from 'lucide-react'

type AccordionItem = {
  id: string
  title: string
  imageSrc: string
  imageAlt: string
  content: ReactNode
}

type AccordionProps = {
  items: AccordionItem[]
  onChange?: (openId: string) => void
}

const Accordion: React.FC<AccordionProps> = ({ items, onChange }) => {
  const [openItem, setOpenItem] = useState<string>(items[0]?.id || '')

  useEffect(() => {
    if (onChange) {
      onChange(openItem)
    }
  }, [openItem])

  const toggleItem = (id: string) => {
    if (id !== openItem) {
      setOpenItem(id)
    }
  }

  return (
    <div className='space-y-2'>
      {items.map(({ id, title, content }) => (
        <div key={id} className='overflow-hidden rounded-2xl border shadow-sm'>
          <button
            onClick={() => toggleItem(id)}
            className='flex w-full items-center justify-between p-4 text-left font-semibold transition hover:bg-gray-100'>
            {title}
            <span>
              {openItem === id ? (
                <ChevronDown width={16} height={16} />
              ) : (
                <ChevronRight width={16} height={16} />
              )}
            </span>
          </button>
          {openItem === id && (
            <div className='animate-fadeIn px-4 pb-4 text-gray-700'>{content}</div>
          )}
        </div>
      ))}
    </div>
  )
}

export default Accordion
