import { useEffect, useState } from 'react'

export type LocalStorageType = string | null

export function useLocalStorage(key: string) {
  const [state, setState] = useState<LocalStorageType>()
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
    setState(localStorage.getItem(key))
  }, [])

  const setWithLocalStorage = (nextState: NonNullable<LocalStorageType>) => {
    localStorage.setItem(key, nextState)
    setState(nextState)
  }

  const removeLocalStorage = () => localStorage.removeItem(key)

  return [state, setWithLocalStorage, removeLocalStorage, isLoaded] as const
}
