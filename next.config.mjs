import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer'

/** @type {import('next').NextConfig} */
const nextConfig = {
  compress: true,
  productionBrowserSourceMaps: false,
  // basePath: '/seo', // 替换为你动态路由的基础路径
  assetPrefix: '/seo', // 静态资源的基础路径
  experimental: {
    outputFileTracingIgnores: [
      '**/*.map',
      '**/*.log',
      '**/*.tmp',
      '**/node_modules/sharp/**',
      '**/node_modules/canvas/**',
      '**/node_modules/puppeteer/**',
    ],
  },
  reactStrictMode: true,
  swcMinify: false, // for pdf-react
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**', // 因为要展示外部网站的icon, 但这不是推荐的做法
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '**', // 因为要展示外部网站的icon, 但这不是推荐的做法
        port: '',
        pathname: '/**',
      },
    ],
  },
  webpack: (config, { dev }) => {
    config.resolve.alias.canvas = false
    // 添加 BundleAnalyzerPlugin 插件配置
    if (process.env.ANALYZE === 'true') {
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'server',
          openAnalyzer: true,
        }),
      )
    }

    if (!dev) {
      config.devtool = false // 禁用 source map 生成
    }

    return config
  },
}

export default nextConfig
