// @ts-nocheck
import React, { memo, useCallback, useEffect } from 'react'
import dynamic from 'next/dynamic'
import Head from 'next/head'
import { GetStaticPaths, GetStaticProps } from 'next'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'
import { Language, ResearchData } from '@/types/research'
import RegisterGuide from '@/components/common/RegisterGuide'
import { useRouter } from 'next/router'
import ReferenceNumber from '@/components/seo/ReferenceNumber'
import { References } from '@/components/seo/References'
import { getCompanyData } from '@/utils/getCompanyData'

// 动态导入组件
const ReactMarkdown = dynamic(() => import('react-markdown'), {
  loading: () => <div>Loading...</div>,
})

const CommonLayout = dynamic(() => import('@/components/seo/CommonLayout'), {
  loading: () => <div>Loading...</div>,
})

const LanguageSelector = dynamic(() => import('@/components/seo/LanguageSelector'), {
  loading: () => <div>Loading...</div>,
})

interface ResearchPageProps {
  researchData: ResearchData | null
  lang: Language
  relatedArticles: Array<{
    title: string
    url: string
  }>
  param: string
}

const MarketPage: React.FC<ResearchPageProps> = memo(
  ({ researchData, lang, relatedArticles, param }) => {
    // 配置注册引导的插入位置
    const guideConfig = ['3', '4']

    // 追踪h2标签的计数
    // let h2Count = 0;
    const router = useRouter()

    useEffect(() => {
      // 追踪页面浏览事件
      // Tracking.trackPageView('[SEO]Market Page', true, {
      //   language: lang,
      //   market_param: param,
      //   market_domain: researchData?.marketDomain || '',
      //   market_region: researchData?.marketRegion || '',
      //   market_year: researchData?.marketYear || '',
      //   title: researchData?.title || '',
      // })
    }, [lang, param, researchData?.title])

    const handleLanguageChange = useCallback(
      (newLang: Language) => {
        const { param } = router.query
        router.push(`/${newLang}/company/${param}`)
      },
      [router],
    )

    const localizedContent = researchData?.localizedData[lang]
    const availableLanguages = researchData ? researchData.marketLangs : []

    // 如果页面正在生成中，显示加载状态
    if (router.isFallback || !researchData) {
      return (
        <CommonLayout>
          <div className='flex h-screen items-center justify-center'>
            <div className='text-lg'>Loading...</div>
          </div>
        </CommonLayout>
      )
    }

    // Get the current URL
    const currentURL =
      typeof window !== 'undefined'
        ? window.location.href
        : `https://smarties.ai/${lang}/market/${router.query.param}`

    // Get the logo URL based on environment
    const logoURL = process.env.NEXT_PUBLIC_LOGO_URL || 'https://smarties.ai/logo.png'

    // Format the date strings
    const publishDate = researchData.updatedAt
    const modifiedDate = researchData.updatedAt

    // Get keywords from related domains
    const keywords = [
      ...localizedContent.keywords.split(','),
      localizedContent.domain,
      ...(researchData.relatedDomains || []),
    ].filter(Boolean)

    const jsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      isAccessibleForFree: true,
      headline: localizedContent.title,
      // "alternativeHeadline": "",
      description: (localizedContent.description || localizedContent.content || '').substring(0, 200),
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': currentURL,
      },
      author: {
        '@type': 'Organization',
        name: 'AI-Smarties Group',
      },
      publisher: {
        '@type': 'Organization',
        name: 'AI-Smarties Group',
        logo: {
          '@type': 'ImageObject',
          url: logoURL,
        },
      },
      datePublished: publishDate,
      dateModified: modifiedDate,
      inLanguage: lang,
      articleSection: localizedContent.domain,
      keywords: keywords,
      about: [
        {
          '@type': 'Market Name',
          name: localizedContent.domain,
        },
        {
          '@type': 'Market Region',
          name: researchData.marketRegion || '',
        },
        {
          '@type': 'Market Year',
          name: researchData.marketYear || '',
        },
      ],
    }

    return (
      <div>
        <Head>
          <title>{localizedContent.title}</title>
          <meta
            name='description'
            content={(localizedContent.description || localizedContent.content).substring(0, 160)}
          />
          <meta name='keywords' content={localizedContent.keywords} />

          {/* Open Graph tags */}
          <meta property='og:title' content={localizedContent.title} />
          <meta
            property='og:description'
            content={(localizedContent.description || localizedContent.content).substring(0, 160)}
          />
          <meta property='og:url' content={currentURL} />
          <meta property='og:type' content='article' />
          <meta property='og:site_name' content='Smarties AI' />
          <meta property='og:image' content='https://ai-smarties.com/seo/images/logo.svg' />

          {/* Twitter Card tags */}
          <meta name='twitter:card' content='summary_large_image' />
          <meta name='twitter:title' content={localizedContent.title} />
          <meta
            name='twitter:description'
            content={(researchData.seoDescription || localizedContent.content).substring(0, 160)}
          />
          <meta name='twitter:image' content='https://ai-smarties.com/seo/images/logo.svg' />
          <meta property='twitter:creator' content='Jie@SmartiesAI' />

          {/* Alternate language versions */}
          {availableLanguages.map((altLang) => (
            <link
              key={altLang}
              rel='alternate'
              hrefLang={altLang}
              href={`https://smarties.ai/${altLang}/company/${router.query.param}`}
            />
          ))}

          {/* JSON-LD structured data */}
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
          />
        </Head>
        <CommonLayout relatedArticles={relatedArticles} currentLang={lang}>
          <div className='min-h-screen pb-20'
            asm-tracking='VISIT_SEO_PAGE:VIEW'
            asm-tracking-p-page='SEO_COMPANY'
            asm-tracking-p-language={lang}
            asm-tracking-p-research_domain={researchData?.researchDomain || ''}
            asm-tracking-p-research_year={researchData?.researchYear || ''}
            asm-tracking-p-title={researchData?.title || ''}
          >
            <article className='overflow-hidden rounded-lg bg-white shadow-sm'>
              <div className='p-3 sm:p-4 md:p-6 lg:p-8'>
                <header className='border-b border-gray-100 pb-4'>
                  <h1 className='mb-4 bg-gradient-to-r from-indigo-600 to-indigo-400 bg-clip-text text-2xl font-bold text-transparent sm:mb-6 sm:text-3xl md:text-4xl'>
                    {localizedContent.title}
                  </h1>
                  <div className='flex flex-wrap gap-3 text-sm text-gray-500 sm:gap-3'>
                    {/* <div className='flex items-center'>
                      <span className='mr-2 h-2.5 w-2.5 rounded-full bg-indigo-100 sm:h-3 sm:w-3 md:h-4 md:w-4'></span>
                      <span className='font-medium text-gray-700'>Theme:</span>
                      <span className='ml-1 sm:ml-2'>{localizedContent?.domain}</span>
                    </div> */}
                    <div className='flex items-center'>
                      <span className='mr-2 h-2.5 w-2.5 rounded-full bg-yellow-100 sm:h-3 sm:w-3 md:h-4 md:w-4'></span>
                      <span className='font-medium text-gray-700'>Year:</span>
                      <span className='ml-1 sm:ml-2'>{researchData.researchYear}</span>
                    </div>


                    <div className='flex items-center'>
                      <span className='mr-2 h-2.5 w-2.5 rounded-full bg-blue-100 sm:h-3 sm:w-3 md:h-4 md:w-4'></span>
                      <span className='font-medium text-gray-700'>Updated:</span>
                      <span className='ml-1 sm:ml-2'>{researchData.updatedAt}</span>
                    </div>


                    <LanguageSelector
                      currentLang={lang}
                      availableLanguages={availableLanguages}
                      onLanguageChange={handleLanguageChange}
                    />
                  </div>
                </header>

                <div className='prose prose-sm sm:prose lg:prose-lg mx-auto mt-6'>
                  <ReactMarkdown
                    rehypePlugins={[rehypeRaw]}
                    remarkPlugins={[remarkGfm]}
                    components={{
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      h1: ({ node, children, ...props }) => (
                        <h1
                          style={{
                            fontSize: '2rem',
                            fontWeight: 'bold',
                            color: '#1f2937',
                            marginBottom: '1rem',
                          }}
                          {...props}>
                          {children}
                        </h1>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      h2: ({ node, children, ...props }) => {
                        const num = children.split('.')[0]

                        const shouldInsertGuide = guideConfig.includes(num)

                        return (
                          <div>
                            {shouldInsertGuide && <RegisterGuide />}
                            <h2
                              style={{
                                fontSize: '1.5rem',
                                fontWeight: 'bold',
                                color: '#1f2937',
                                margin: '1.5rem 0 1rem',
                              }}
                              {...props}>
                              {children}
                            </h2>
                          </div>
                        )
                      },
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      h3: ({ node, children, ...props }) => (
                        <h3
                          style={{
                            fontSize: '1.25rem',
                            fontWeight: 'bold',
                            color: '#1f2937',
                            margin: '1rem 0 0.75rem',
                          }}
                          {...props}>
                          {children}
                        </h3>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      p: ({ node, children, ...props }) => (
                        <p
                          style={{
                            marginBottom: '1rem',
                            lineHeight: '1.625',
                            color: '#4b5563',
                          }}
                          {...props}>
                          {children}
                        </p>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      ul: ({ node, children, ...props }) => (
                        <ul
                          style={{
                            listStyleType: 'disc',
                            paddingLeft: '1.5rem',
                            marginBottom: '1rem',
                          }}
                          {...props}>
                          {children}
                        </ul>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      ol: ({ node, children, ...props }) => (
                        <ol
                          style={{
                            listStyleType: 'decimal',
                            paddingLeft: '1.5rem',
                            marginBottom: '1rem',
                          }}
                          {...props}>
                          {children}
                        </ol>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      li: ({ node, children, ...props }) => (
                        <li
                          style={{
                            marginBottom: '0.25rem',
                            color: '#4b5563',
                          }}
                          {...props}>
                          {children}
                        </li>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      strong: ({ node, children, ...props }) => (
                        <strong
                          style={{
                            fontWeight: '600',
                            color: '#1f2937',
                          }}
                          {...props}>
                          {children}
                        </strong>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      blockquote: ({ node, children, ...props }) => (
                        <blockquote
                          style={{
                            borderLeftWidth: '4px',
                            borderLeftColor: '#818cf8',
                            paddingLeft: '1rem',
                            margin: '1rem 0',
                            fontStyle: 'italic',
                            color: '#4b5563',
                          }}
                          {...props}>
                          {children}
                        </blockquote>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      code: ({
                        // node,
                        inline,
                        children,
                        ...props
                      }: {
                        node?: any
                        inline?: boolean
                        children?: React.ReactNode
                      } & React.HTMLAttributes<HTMLElement>) =>
                        inline ? (
                          <code
                            style={{
                              padding: '0.2rem 0.4rem',
                              backgroundColor: '#f3f4f6',
                              borderRadius: '0.25rem',
                              fontSize: '0.875rem',
                              color: '#4f46e5',
                            }}
                            {...props}>
                            {children}
                          </code>
                        ) : (
                          <pre
                            style={{
                              backgroundColor: '#f9fafb',
                              padding: '1rem',
                              borderRadius: '0.5rem',
                              overflow: 'auto',
                              marginBottom: '1rem',
                            }}>
                            <code style={{ color: '#1f2937', fontSize: '0.875rem' }} {...props}>
                              {children}
                            </code>
                          </pre>
                        ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      table: ({ node, children, ...props }) => (
                        <div style={{ overflowX: 'auto', maxWidth: '100%' }}>
                          <table
                            style={{
                              borderCollapse: 'collapse',
                              width: '100%',
                              marginBottom: '1rem',
                            }}
                            {...props}>
                            {children}
                          </table>
                        </div>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      thead: ({ node, children, ...props }) => (
                        <thead style={{ backgroundColor: '#f3f4f6' }} {...props}>
                          {children}
                        </thead>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      tr: ({ node, children, ...props }) => (
                        <tr style={{ borderBottom: '1px solid #e5e7eb' }} {...props}>
                          {children}
                        </tr>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      th: ({ node, children, ...props }) => (
                        <th
                          style={{
                            padding: '0.75rem',
                            borderBottom: '2px solid #d1d5db',
                            textAlign: 'left',
                            fontWeight: '600',
                          }}
                          {...props}>
                          {children}
                        </th>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      td: ({ node, children, ...props }) => (
                        <td
                          style={{
                            padding: '0.75rem',
                            borderBottom: '1px solid #e5e7eb',
                          }}
                          {...props}>
                          {children}
                        </td>
                      ),
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      ref: ({ node, children, ...props }) => {
                        const number = Array.isArray(children)
                          ? children.join('')
                          : String(children)
                        if (number === 'undefined') {
                          return ''
                        }
                        return (
                          <ReferenceNumber
                            asm-tracking='CLICK_SEO_REFERENCE:CLICK'
                            asm-tracking-p-page='SEO_COMPANY'
                            id={node.attributes?.id || ''}
                            number={number}
                            citations={researchData?.citations}
                            {...props}
                          />
                        )
                      },
                    }}>
                    {localizedContent.content}
                  </ReactMarkdown>
                </div>

                <hr className='my-8 border-t border-gray-100' />
                <References references={researchData?.references} citations={researchData?.citations} />

                <footer className='mt-6 border-t border-gray-100 pt-4 sm:mt-8 sm:pt-6 md:mt-12'>
                  <h2 className='mb-3 text-lg font-medium text-gray-800 sm:mb-4'>
                    Related Domains
                  </h2>
                  <div className='flex flex-wrap gap-2'>
                    {researchData.relatedDomains.map((domain) => (
                      <span
                        key={domain}
                        className='rounded-full border border-indigo-100 bg-gradient-to-r from-indigo-50 to-blue-50 px-2.5 py-1 text-xs font-medium text-indigo-700 transition-colors duration-200 hover:border-indigo-200 sm:px-3 sm:py-1.5 sm:text-sm md:px-4 md:py-2'>
                        {domain}
                      </span>
                    ))}
                  </div>
                </footer>

                {/* <TextEditMenu
              onExpand={handleExpand}
              onPolish={handlePolish}
              onClose={() => { }}
            /> */}
              </div>
            </article>
          </div>
        </CommonLayout>
      </div>
    )
  },
)

MarketPage.displayName = 'MarketPage' // 移动 displayName 到这里

export const getStaticPaths: GetStaticPaths = async () => {
  return {
    paths: [],
    fallback: 'blocking',
  }
}

export const getStaticProps: GetStaticProps<ResearchPageProps> = async ({ params }) => {
  const lang = params?.lang as Language
  const param = params?.param as string

  try {
    // 获取当前文章数据
    const researchData = await getCompanyData(param)

    if (!researchData) {
      return {
        notFound: true, // 使用 Next.js 的 404 页面
        revalidate: 60, // 1分钟后重新验证
      }
    }

    // 获取相关文章数据
    const relatedArticles = researchData.localizedData[lang]?.relates || [] // 这里可以实现相关文章的逻辑
    // console.log('localizedData: ', researchData.localizedData[lang])
    // console.log('relatedArticles: ', relatedArticles)
    // console.log('Citations:', researchData.citations)

    // 优化：只保留当前语言的数据，减少数据量
    const optimizedMarketData = {
      ...researchData,
      localizedData: {
        [lang]: researchData.localizedData[lang],
      },
      // 只保留必要的字段
      // marketDomain: researchData.marketDomain,
      // researchRegion: researchData.researchRegion,
      researchYear: researchData.researchYear,
      marketLangs: Object.keys(researchData.localizedData) as Language[],
      updatedAt: researchData.updatedAt,
    }

    console.log('marketLangs: ', optimizedMarketData.marketLangs)

    const data = {
      props: {
        researchData: optimizedMarketData,
        lang,
        relatedArticles,
        param,
      },
      revalidate: 3600, // 每小时重新验证一次
    }

    console.log(data)
    return data
  } catch (error) {
    console.error('Error in getStaticProps:', error)
    return {
      notFound: true,
      revalidate: 60,
    }
  }
}

export default MarketPage
