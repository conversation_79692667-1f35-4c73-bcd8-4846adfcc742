import React from 'react'
import MarkdownRenderer from '../common/MarkdownRenderer'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'
import RegisterGuide from '../common/RegisterGuide'
import createMarkdownComponents, { Citations } from '../common/MarkdownComponents'

interface SeoMarkdownRendererProps {
  content: string
  isStreaming?: boolean
  citations?: Citations
  className?: string
  guideConfig?: string[] // 配置需要在哪些h2前插入注册引导
}

/**
 * SEO页面专用的Markdown渲染器
 * 扩展了基础MarkdownRenderer，添加了引用编号、注册引导等SEO特定功能
 */
const SeoMarkdownRenderer: React.FC<SeoMarkdownRendererProps> = ({
  content,
  isStreaming = false,
  citations = {} as Citations,
  className = 'prose prose-sm sm:prose lg:prose-lg mx-auto',
  guideConfig = [],
}) => {
  // 创建引导注册函数
  const guideRegisterFn = (num: string) => {
    return guideConfig.includes(num) ? <RegisterGuide /> : null
  }

  // 使用统一的Markdown组件
  const components = createMarkdownComponents({
    citations,
    guideRegisterFn,
    trackingPage: 'SEO_MARKET',
  })

  return (
    <MarkdownRenderer
      content={content}
      isStreaming={isStreaming}
      className={className}
      rehypePlugins={[rehypeRaw]}
      remarkPlugins={[remarkGfm]}
      components={components}
    />
  )
}

export default SeoMarkdownRenderer